import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react';
import { toast } from 'react-hot-toast';

const ClerkAuthContext = createContext();

export const useAuth = () => {
  const context = useContext(ClerkAuthContext);
  if (!context) {
    throw new Error('useAuth must be used within a ClerkAuthProvider');
  }
  return context;
};

export const ClerkAuthProvider = ({ children }) => {
  const { user, isLoaded: userLoaded, isSignedIn } = useUser();
  const { signOut, getToken } = useClerkAuth();
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState(null);

  // Sync Clerk user with our backend
  useEffect(() => {
    const syncUserWithBackend = async () => {
      if (!userLoaded) return;
      
      setLoading(true);
      
      try {
        if (isSignedIn && user) {
          // Get the JWT token from Clerk
          const token = await getToken();
          
          // Sync user data with our backend
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/clerk/sync`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              clerkId: user.id,
              email: user.primaryEmailAddress?.emailAddress,
              name: user.fullName || `${user.firstName} ${user.lastName}`.trim(),
              firstName: user.firstName,
              lastName: user.lastName,
              imageUrl: user.imageUrl,
              emailVerified: user.primaryEmailAddress?.verification?.status === 'verified',
            }),
          });

          if (response.ok) {
            const data = await response.json();
            setUserProfile(data.data.user);
            
            // Store token in localStorage for API calls
            localStorage.setItem('clerk_token', token);
          } else {
            console.error('Failed to sync user with backend');
            toast.error('Failed to sync user data');
          }
        } else {
          // User is not signed in, clear local data
          setUserProfile(null);
          localStorage.removeItem('clerk_token');
        }
      } catch (error) {
        console.error('Error syncing user:', error);
        toast.error('Authentication error');
      } finally {
        setLoading(false);
      }
    };

    syncUserWithBackend();
  }, [userLoaded, isSignedIn, user, getToken]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut();
      setUserProfile(null);
      localStorage.removeItem('clerk_token');
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      const token = await getToken();
      
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(profileData),
      });

      if (response.ok) {
        const data = await response.json();
        setUserProfile(data.data.user);
        toast.success('Profile updated successfully');
        return data.data.user;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
      throw error;
    }
  };

  // Get fresh token for API calls
  const getAuthToken = async () => {
    try {
      if (isSignedIn) {
        return await getToken();
      }
      return null;
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return userProfile?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(userProfile?.role);
  };

  // Check if user is admin
  const isAdmin = () => {
    return hasRole('admin');
  };

  // Check if user is author
  const isAuthor = () => {
    return hasAnyRole(['author', 'admin']);
  };

  const value = {
    // User data
    user: userProfile,
    clerkUser: user,
    isAuthenticated: isSignedIn && !!userProfile,
    isSignedIn,
    loading: loading || !userLoaded,
    
    // Auth methods
    logout: handleLogout,
    updateProfile,
    getAuthToken,
    
    // Role checking
    hasRole,
    hasAnyRole,
    isAdmin,
    isAuthor,
    
    // Clerk-specific data
    clerkLoaded: userLoaded,
  };

  return (
    <ClerkAuthContext.Provider value={value}>
      {children}
    </ClerkAuthContext.Provider>
  );
};

export default ClerkAuthProvider;
