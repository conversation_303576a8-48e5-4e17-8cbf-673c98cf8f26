const express = require('express');
const { verifyClerkToken, clerk } = require('../middleware/clerkAuth');
const User = require('../models/User');
const logger = require('../config/logger');

const router = express.Router();

// @desc    Sync user with Clerk
// @route   POST /api/auth/clerk/sync
// @access  Private (Clerk authenticated)
router.post('/sync', verifyClerkToken, async (req, res) => {
  try {
    const { clerkId, email, name, firstName, lastName, imageUrl, emailVerified } = req.body;

    // Validate required fields
    if (!clerkId || !email) {
      return res.status(400).json({
        success: false,
        message: 'Clerk ID and email are required',
      });
    }

    // Find or create user
    let user = await User.findOne({ clerkId });

    if (!user) {
      // Create new user
      user = await User.create({
        clerkId,
        email,
        name: name || email.split('@')[0],
        firstName,
        lastName,
        imageUrl,
        isVerified: emailVerified || false,
        role: 'user',
        lastLoginAt: new Date(),
      });

      logger.info('New user created via Clerk sync', {
        userId: user._id,
        clerkId,
        email,
      });
    } else {
      // Update existing user
      const updateData = {
        email,
        name: name || user.name,
        firstName,
        lastName,
        imageUrl,
        isVerified: emailVerified !== undefined ? emailVerified : user.isVerified,
        lastLoginAt: new Date(),
      };

      user = await User.findByIdAndUpdate(user._id, updateData, { new: true });

      logger.info('User updated via Clerk sync', {
        userId: user._id,
        clerkId,
        email,
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          _id: user._id,
          clerkId: user.clerkId,
          email: user.email,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          imageUrl: user.imageUrl,
          role: user.role,
          isVerified: user.isVerified,
          blogDomain: user.blogDomain,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
        },
      },
    });
  } catch (error) {
    logger.logError(error, req, {
      route: 'clerk/sync',
      clerkId: req.body.clerkId,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to sync user data',
    });
  }
});

// @desc    Get current user profile
// @route   GET /api/auth/profile
// @access  Private (Clerk authenticated)
router.get('/profile', verifyClerkToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-__v');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user,
      },
    });
  } catch (error) {
    logger.logError(error, req, {
      route: 'auth/profile',
      userId: req.user?._id,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to get user profile',
    });
  }
});

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private (Clerk authenticated)
router.put('/profile', verifyClerkToken, async (req, res) => {
  try {
    const { name, firstName, lastName, blogDomain, bio } = req.body;

    // Validate blog domain if provided
    if (blogDomain) {
      const domainRegex = /^[a-z0-9-]+$/;
      if (!domainRegex.test(blogDomain)) {
        return res.status(400).json({
          success: false,
          message: 'Blog domain can only contain lowercase letters, numbers, and hyphens',
        });
      }

      // Check if domain is already taken
      const existingUser = await User.findOne({ 
        blogDomain, 
        _id: { $ne: req.user._id } 
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Blog domain is already taken',
        });
      }
    }

    const updateData = {};
    if (name) updateData.name = name;
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (blogDomain !== undefined) updateData.blogDomain = blogDomain;
    if (bio !== undefined) updateData.bio = bio;

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-__v');

    logger.info('User profile updated', {
      userId: user._id,
      updatedFields: Object.keys(updateData),
    });

    res.status(200).json({
      success: true,
      data: {
        user,
      },
    });
  } catch (error) {
    logger.logError(error, req, {
      route: 'auth/profile',
      userId: req.user?._id,
    });

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: Object.values(error.errors).map(err => err.message).join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update user profile',
    });
  }
});

// @desc    Delete user account
// @route   DELETE /api/auth/profile
// @access  Private (Clerk authenticated)
router.delete('/profile', verifyClerkToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const clerkId = req.user.clerkId;

    // Delete user from our database
    await User.findByIdAndDelete(userId);

    // Optionally delete user from Clerk (uncomment if needed)
    // await clerk.users.deleteUser(clerkId);

    logger.info('User account deleted', {
      userId,
      clerkId,
    });

    res.status(200).json({
      success: true,
      message: 'Account deleted successfully',
    });
  } catch (error) {
    logger.logError(error, req, {
      route: 'auth/profile/delete',
      userId: req.user?._id,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to delete account',
    });
  }
});

// @desc    Get user statistics
// @route   GET /api/auth/stats
// @access  Private (Clerk authenticated)
router.get('/stats', verifyClerkToken, async (req, res) => {
  try {
    const Post = require('../models/Post');
    
    const stats = await Promise.all([
      Post.countDocuments({ author: req.user._id }),
      Post.countDocuments({ author: req.user._id, status: 'published' }),
      Post.countDocuments({ author: req.user._id, status: 'draft' }),
    ]);

    const [totalPosts, publishedPosts, draftPosts] = stats;

    res.status(200).json({
      success: true,
      data: {
        stats: {
          totalPosts,
          publishedPosts,
          draftPosts,
          memberSince: req.user.createdAt,
          lastLogin: req.user.lastLoginAt,
        },
      },
    });
  } catch (error) {
    logger.logError(error, req, {
      route: 'auth/stats',
      userId: req.user?._id,
    });

    res.status(500).json({
      success: false,
      message: 'Failed to get user statistics',
    });
  }
});

module.exports = router;
