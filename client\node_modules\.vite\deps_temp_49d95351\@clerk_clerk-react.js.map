{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../@clerk/shared/src/error.ts", "../../@clerk/shared/dist/chunk-7ELT755Q.mjs", "../../@clerk/shared/src/authorization.ts", "../../@clerk/shared/src/underscore.ts", "../../@clerk/shared/src/isomorphicAtob.ts", "../../@clerk/shared/src/constants.ts", "../../@clerk/shared/src/keys.ts", "../../@clerk/shared/src/telemetry/throttler.ts", "../../@clerk/shared/src/telemetry/collector.ts", "../../@clerk/shared/src/telemetry/events/component-mounted.ts", "../../@clerk/shared/src/telemetry/events/method-called.ts", "../../@clerk/shared/src/telemetry/events/framework-metadata.ts", "../../@clerk/clerk-react/src/errors/errorThrower.ts", "../../@clerk/clerk-react/src/hooks/useAuth.ts", "../../@clerk/clerk-react/src/contexts/AuthContext.ts", "../../@clerk/clerk-react/src/contexts/IsomorphicClerkContext.tsx", "../../@clerk/clerk-react/src/errors/messages.ts", "../../@clerk/clerk-react/src/hooks/useAssertWrappedByClerkProvider.ts", "../../@clerk/clerk-react/src/hooks/utils.ts", "../../@clerk/clerk-react/src/hooks/useEmailLink.ts", "../../@clerk/clerk-react/src/hooks/useSignIn.ts", "../../@clerk/clerk-react/src/hooks/useSignUp.ts", "../../@clerk/clerk-react/src/hooks/index.ts", "../../@clerk/clerk-react/src/components/controlComponents.tsx", "../../@clerk/clerk-react/src/contexts/SessionContext.tsx", "../../@clerk/clerk-react/src/components/withClerk.tsx", "../../@clerk/shared/src/organization.ts", "../../@clerk/shared/src/utils/noop.ts", "../../@clerk/shared/src/utils/createDeferredPromise.ts", "../../@clerk/shared/src/authorization-errors.ts", "../../@clerk/shared/src/react/hooks/createContextAndHook.ts", "../../@clerk/shared/src/react/contexts.tsx", "../../@clerk/shared/src/react/clerk-swr.ts", "../../@clerk/shared/src/react/hooks/usePagesOrInfinite.ts", "../../@clerk/shared/src/react/hooks/useOrganization.tsx", "../../@clerk/shared/src/react/hooks/useOrganizationList.tsx", "../../@clerk/shared/src/react/hooks/useSafeLayoutEffect.tsx", "../../@clerk/shared/src/react/hooks/useSession.ts", "../../@clerk/shared/src/react/hooks/useSessionList.ts", "../../@clerk/shared/src/react/hooks/useUser.ts", "../../@clerk/shared/src/react/hooks/useClerk.ts", "../../@clerk/shared/src/react/hooks/useDeepEqualMemo.ts", "../../@clerk/shared/src/react/hooks/useReverification.ts", "../../@clerk/shared/src/react/hooks/createCommerceHook.tsx", "../../@clerk/shared/src/react/hooks/useStatements.tsx", "../../@clerk/shared/src/react/hooks/usePaymentAttempts.tsx", "../../@clerk/shared/src/react/hooks/usePaymentMethods.tsx", "../../@clerk/shared/src/react/hooks/useSubscriptionItems.tsx", "../../swr/dist/index/index.mjs", "../../swr/dist/_internal/config-context-client-v7VOFo66.mjs", "../../swr/dist/_internal/events.mjs", "../../dequal/lite/index.mjs", "../../swr/dist/_internal/constants.mjs", "../../swr/dist/_internal/index.mjs", "../../swr/dist/infinite/index.mjs", "../../dequal/dist/index.mjs", "../../@clerk/shared/src/utils/runtimeEnvironment.ts", "../../@clerk/shared/src/deprecated.ts", "../../@clerk/clerk-react/dist/chunk-OANWQR3B.mjs", "../../@clerk/shared/src/versionSelector.ts", "../../@clerk/shared/src/proxy.ts", "../../@clerk/shared/src/url.ts", "../../@clerk/shared/src/retry.ts", "../../@clerk/shared/src/loadScript.ts", "../../@clerk/shared/src/loadClerkJsScript.ts", "../../@clerk/shared/src/utils/allSettled.ts", "../../@clerk/shared/src/utils/logErrorInDevMode.ts", "../../@clerk/shared/src/utils/fastDeepMerge.ts", "../../@clerk/shared/src/utils/handleValueOrFn.ts", "../../@clerk/clerk-react/src/polyfills.ts", "../../@clerk/clerk-react/src/index.ts", "../../@clerk/clerk-react/src/components/uiComponents.tsx", "../../@clerk/clerk-react/src/utils/childrenUtils.tsx", "../../@clerk/clerk-react/src/utils/isConstructor.ts", "../../@clerk/clerk-react/src/utils/useMaxAllowedInstancesGuard.tsx", "../../@clerk/clerk-react/src/utils/useCustomElementPortal.tsx", "../../@clerk/clerk-react/src/utils/useCustomPages.tsx", "../../@clerk/clerk-react/src/utils/componentValidation.ts", "../../@clerk/clerk-react/src/utils/useCustomMenuItems.tsx", "../../@clerk/clerk-react/src/utils/useWaitForComponentMount.ts", "../../@clerk/clerk-react/src/components/ClerkHostRenderer.tsx", "../../@clerk/clerk-react/src/components/SignInButton.tsx", "../../@clerk/clerk-react/src/components/SignUpButton.tsx", "../../@clerk/clerk-react/src/components/SignOutButton.tsx", "../../@clerk/clerk-react/src/components/SignInWithMetamaskButton.tsx", "../../@clerk/clerk-react/src/contexts/ClerkProvider.tsx", "../../@clerk/clerk-react/src/contexts/ClerkContextProvider.tsx", "../../@clerk/clerk-react/src/isomorphicClerk.ts", "../../@clerk/shared/src/object.ts", "../../@clerk/shared/src/deriveState.ts", "../../@clerk/shared/src/browser.ts", "../../@clerk/shared/src/eventBus.ts", "../../@clerk/shared/src/clerkEventBus.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\nexport {\n  __export,\n  __reExport,\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod\n};\n//# sourceMappingURL=chunk-7ELT755Q.mjs.map", "import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => value.replace(/^(org:)*/, 'org:');\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return prefixWithOrg(orgRole) === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages.\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n", "/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n", "import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n", "import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from Clerk <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `Clerk<PERSON>rovider` also accepts a `telemetry` prop that will be passed to the collector during initialization:\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry\n */\nimport type {\n  InstanceType,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryEvent[] = [];\n  #pendingFlush: any;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (this.#config.disabled || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return this.#config.debug || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DEBUG));\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push(preparedPayload);\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (typeof eventSamplingRate === 'undefined' || randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        const cancel = typeof cancelIdleCallback !== 'undefined' ? cancelIdleCallback : clearTimeout;\n        cancel(this.#pendingFlush);\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    fetch(new URL('/v1/event', this.#config.endpoint), {\n      method: 'POST',\n      // TODO: We send an array here with that idea that we can eventually send multiple events.\n      body: JSON.stringify({\n        events: this.#buffer,\n      }),\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n      .catch(() => void 0)\n      .then(() => {\n        this.#buffer = [];\n      })\n      .catch(() => void 0);\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    let sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n    if (typeof window !== 'undefined' && window.Clerk) {\n      // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n      sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Bo<PERSON>an(props?.appearance),\n        baseTheme: <PERSON><PERSON><PERSON>(props?.appearance?.baseTheme),\n        elements: <PERSON><PERSON><PERSON>(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n *\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n", "import type { ErrorThrowerOptions } from '@clerk/shared/error';\nimport { buildErrorThrower } from '@clerk/shared/error';\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/clerk-react' });\n\nexport { errorThrower };\n\n/**\n * Overrides options of the internal errorThrower (eg setting packageName prefix).\n *\n * @internal\n */\nexport function setErrorThrowerOptions(options: ErrorThrowerOptions) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n", "import { createCheckAuthorization, resolveAuthState } from '@clerk/shared/authorization';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type {\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  PendingSessionOptions,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { errorThrower } from '../errors/errorThrower';\nimport { invalidStateError } from '../errors/messages';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\nimport { createGetToken, createSignOut } from './utils';\n\n/**\n * @inline\n */\ntype UseAuthOptions = Record<string, any> | PendingSessionOptions | undefined | null;\n\n/**\n * The `useAuth()` hook provides access to the current user's authentication state and methods to manage the active session.\n *\n * > [!NOTE]\n * > To access auth data server-side, see the [`Auth` object reference doc](https://clerk.com/docs/references/backend/types/auth-object).\n *\n * <If sdk=\"nextjs\">\n * By default, Next.js opts all routes into static rendering. If you need to opt a route or routes into dynamic rendering because you need to access the authentication data at request time, you can create a boundary by passing the `dynamic` prop to `<ClerkProvider>`. See the [guide on rendering modes](https://clerk.com/docs/references/nextjs/rendering-modes) for more information, including code examples.\n * </If>\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in (no active organization)\", \"Signed in (with active organization)\"]\n *\n * @param [initialAuthStateOrOptions] - An object containing the initial authentication state or options for the `useAuth()` hook. If not provided, the hook will attempt to derive the state from the context. `treatPendingAsSignedOut` is a boolean that indicates whether pending sessions are considered as signed out or not. Defaults to `true`.\n *\n * @function\n *\n * @example\n *\n * The following example demonstrates how to use the `useAuth()` hook to access the current auth state, like whether the user is signed in or not. It also includes a basic example for using the `getToken()` method to retrieve a session token for fetching data from an external resource.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/ExternalDataPage.tsx' }}\n * import { useAuth } from '@clerk/clerk-react'\n *\n * export default function ExternalDataPage() {\n *   const { userId, sessionId, getToken, isLoaded, isSignedIn } = useAuth()\n *\n *   const fetchExternalData = async () => {\n *     const token = await getToken()\n *\n *     // Fetch data from an external API\n *     const response = await fetch('https://api.example.com/data', {\n *       headers: {\n *         Authorization: `Bearer ${token}`,\n *       },\n *     })\n *\n *     return response.json()\n *   }\n *\n *   if (!isLoaded) {\n *     return <div>Loading...</div>\n *   }\n *\n *   if (!isSignedIn) {\n *     return <div>Sign in to view this page</div>\n *   }\n *\n *   return (\n *     <div>\n *       <p>\n *         Hello, {userId}! Your current active session is {sessionId}.\n *       </p>\n *       <button onClick={fetchExternalData}>Fetch Data</button>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-auth.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useAuth = (initialAuthStateOrOptions: UseAuthOptions = {}): UseAuthReturn => {\n  useAssertWrappedByClerkProvider('useAuth');\n\n  const { treatPendingAsSignedOut, ...rest } = initialAuthStateOrOptions ?? {};\n  const initialAuthState = rest as any;\n\n  const authContextFromHook = useAuthContext();\n  let authContext = authContextFromHook;\n\n  if (authContext.sessionId === undefined && authContext.userId === undefined) {\n    authContext = initialAuthState != null ? initialAuthState : {};\n  }\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const getToken: GetToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut: SignOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useAuth', { treatPendingAsSignedOut }));\n\n  return useDerivedAuth(\n    {\n      ...authContext,\n      getToken,\n      signOut,\n    },\n    {\n      treatPendingAsSignedOut:\n        treatPendingAsSignedOut ?? isomorphicClerk.__internal_getOption?.('treatPendingAsSignedOut'),\n    },\n  );\n};\n\n/**\n * A hook that derives and returns authentication state and utility functions based on the provided auth object.\n *\n * @param authObject - An object containing authentication-related properties and functions.\n *\n * @returns A derived authentication state with helper methods. If the authentication state is invalid, an error is thrown.\n *\n * @remarks\n * This hook inspects session, user, and organization information to determine the current authentication state.\n * It returns an object that includes various properties such as whether the state is loaded, if a user is signed in,\n * session and user identifiers, organization roles, and a `has` function for authorization checks.\n * Additionally, it provides `signOut` and `getToken` functions if applicable.\n *\n * @example\n * ```tsx\n * const {\n *   isLoaded,\n *   isSignedIn,\n *   userId,\n *   orgId,\n *   has,\n *   signOut,\n *   getToken\n * } = useDerivedAuth(authObject);\n * ```\n */\nexport function useDerivedAuth(\n  authObject: any,\n  { treatPendingAsSignedOut = true }: PendingSessionOptions = {},\n): UseAuthReturn {\n  const { userId, orgId, orgRole, has, signOut, getToken, orgPermissions, factorVerificationAge, sessionClaims } =\n    authObject ?? {};\n\n  const derivedHas = useCallback(\n    (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => {\n      if (has) {\n        return has(params);\n      }\n      return createCheckAuthorization({\n        userId,\n        orgId,\n        orgRole,\n        orgPermissions,\n        factorVerificationAge,\n        features: ((sessionClaims as JwtPayload | undefined)?.fea as string) || '',\n        plans: ((sessionClaims as JwtPayload | undefined)?.pla as string) || '',\n      })(params);\n    },\n    [has, userId, orgId, orgRole, orgPermissions, factorVerificationAge],\n  );\n\n  const payload = resolveAuthState({\n    authObject: {\n      ...authObject,\n      getToken,\n      signOut,\n      has: derivedHas,\n    },\n    options: {\n      treatPendingAsSignedOut,\n    },\n  });\n\n  if (!payload) {\n    return errorThrower.throw(invalidStateError);\n  }\n\n  return payload;\n}\n", "import { createContextAndHook } from '@clerk/shared/react';\nimport type {\n  ActClaim,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SessionStatusClaim,\n} from '@clerk/types';\n\nexport type AuthContextValue = {\n  userId: string | null | undefined;\n  sessionId: string | null | undefined;\n  sessionStatus: SessionStatusClaim | null | undefined;\n  sessionClaims: JwtPayload | null | undefined;\n  actor: ActClaim | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: OrganizationCustomRoleKey | null | undefined;\n  orgSlug: string | null | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n};\n\nexport const [AuthContext, useAuthContext] = createContextAndHook<AuthContextValue>('AuthContext');\n", "import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nimport type { IsomorphicClerk } from '../isomorphicClerk';\n\nexport const IsomorphicClerkContext = ClerkInstanceContext;\nexport const useIsomorphicClerkContext = useClerkInstanceContext as unknown as () => IsomorphicClerk;\n", "export const noClerkProviderError = 'You must wrap your application in a <ClerkProvider> component.';\n\nexport const multipleClerkProvidersError =\n  \"You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\n\nexport const multipleChildrenInButtonComponent = (name: string) =>\n  `You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\n\nexport const invalidStateError =\n  'Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support';\n\nexport const unsupportedNonBrowserDomainOrProxyUrlFunction =\n  'Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.';\n\nexport const userProfilePageRenderedError =\n  '<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\nexport const userProfileLinkRenderedError =\n  '<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\n\nexport const organizationProfilePageRenderedError =\n  '<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\nexport const organizationProfileLinkRenderedError =\n  '<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\n\nexport const customPagesIgnoredComponent = (componentName: string) =>\n  `<${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\n\nexport const customPageWrongProps = (componentName: string) =>\n  `Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\n\nexport const customLinkWrongProps = (componentName: string) =>\n  `Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\n\nexport const useAuthHasRequiresRoleOrPermission =\n  'Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: \"org:posts:edit\"`';\n\nexport const noPathProvidedError = (componentName: string) =>\n  `The <${componentName}/> component uses path-based routing by default unless a different routing strategy is provided using the \\`routing\\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \\`path\\` prop. Example: <${componentName} path={'/my-path'} />`;\n\nexport const incompatibleRoutingWithPathProvidedError = (componentName: string) =>\n  `The \\`path\\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \\`routing='path'\\` to the <${componentName}/> component, or drop the \\`path\\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`;\n\nexport const userButtonIgnoredComponent = `<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\n\nexport const customMenuItemsIgnoredComponent =\n  '<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.';\n\nexport const userButtonMenuItemsRenderedError =\n  '<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.';\n\nexport const userButtonMenuActionRenderedError =\n  '<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.';\n\nexport const userButtonMenuLinkRenderedError =\n  '<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.';\n\nexport const userButtonMenuItemLinkWrongProps =\n  'Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.';\n\nexport const userButtonMenuItemsActionWrongsProps =\n  'Missing props. <UserButton.Action /> component requires the following props: label.';\n", "import { useAssertWrappedByClerkProvider as useSharedAssertWrappedByClerkProvider } from '@clerk/shared/react';\n\nimport { errorThrower } from '../errors/errorThrower';\n\nexport const useAssertWrappedByClerkProvider = (source: string): void => {\n  useSharedAssertWrappedByClerkProvider(() => {\n    errorThrower.throwMissingClerkProviderError({ source });\n  });\n};\n", "import type { IsomorphicClerk } from '../isomorphicClerk';\n\n/**\n * @internal\n */\nconst clerkLoaded = (isomorphicClerk: IsomorphicClerk) => {\n  return new Promise<void>(resolve => {\n    const handler = (status: string) => {\n      if (['ready', 'degraded'].includes(status)) {\n        resolve();\n        isomorphicClerk.off('status', handler);\n      }\n    };\n\n    // Register the event listener\n    isomorphicClerk.on('status', handler, { notify: true });\n  });\n};\n\n/**\n * @internal\n */\nexport const createGetToken = (isomorphicClerk: IsomorphicClerk) => {\n  return async (options: any) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\n\n/**\n * @internal\n */\nexport const createSignOut = (isomorphicClerk: IsomorphicClerk) => {\n  return async (...args: any) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n", "import type {\n  CreateEmailLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartEmailLinkFlowParams,\n  SignUpResource,\n  StartEmailLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype EmailLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseEmailLinkSignInReturn = CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;\ntype UseEmailLinkSignUpReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;\ntype UseEmailLinkEmailAddressReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, EmailAddressResource>;\n\nfunction useEmailLink(resource: SignInResource): UseEmailLinkSignInReturn;\nfunction useEmailLink(resource: SignUpResource): UseEmailLinkSignUpReturn;\nfunction useEmailLink(resource: EmailAddressResource): UseEmailLinkEmailAddressReturn;\nfunction useEmailLink(\n  resource: EmailLinkable,\n): UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = React.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow,\n  } as UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn;\n}\n\nexport { useEmailLink };\n", "import { useClientContext } from '@clerk/shared/react';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type { UseSignInReturn } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\n\n/**\n * The `useSignIn()` hook provides access to the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in) object, which allows you to check the current state of a sign-in attempt and manage the sign-in flow. You can use this to create a [custom sign-in flow](https://clerk.com/docs/custom-flows/overview#sign-in-flow).\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @example\n * ### Check the current state of a sign-in\n *\n * The following example uses the `useSignIn()` hook to access the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in) object, which contains the current sign-in attempt status and methods to create a new sign-in attempt. The `isLoaded` property is used to handle the loading state.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/SignInPage.tsx' }}\n * import { useSignIn } from '@clerk/clerk-react'\n *\n * export default function SignInPage() {\n *   const { isLoaded, signIn } = useSignIn()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return <div>The current sign-in attempt status is {signIn?.status}.</div>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-sign-in.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Create a custom sign-in flow with `useSignIn()`\n *\n * The `useSignIn()` hook can also be used to build fully custom sign-in flows, if Clerk's prebuilt components don't meet your specific needs or if you require more control over the authentication flow. Different sign-in flows include email and password, email and phone codes, email links, and multifactor (MFA). To learn more about using the `useSignIn()` hook to create custom flows, see the [custom flow guides](https://clerk.com/docs/custom-flows/overview).\n *\n * ```empty```\n */\nexport const useSignIn = (): UseSignInReturn => {\n  useAssertWrappedByClerkProvider('useSignIn');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useSignIn'));\n\n  if (!client) {\n    return { isLoaded: false, signIn: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import { useClientContext } from '@clerk/shared/react';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type { UseSignUpReturn } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\n\n/**\n * The `useSignUp()` hook provides access to the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up) object, which allows you to check the current state of a sign-up attempt and manage the sign-up flow. You can use this to create a [custom sign-up flow](https://clerk.com/docs/custom-flows/overview#sign-up-flow).\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @example\n * ### Check the current state of a sign-up\n *\n * The following example uses the `useSignUp()` hook to access the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up) object, which contains the current sign-up attempt status and methods to create a new sign-up attempt. The `isLoaded` property is used to handle the loading state.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/SignUpPage.tsx' }}\n * import { useSignUp } from '@clerk/clerk-react'\n *\n * export default function SignUpPage() {\n *   const { isLoaded, signUp } = useSignUp()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return <div>The current sign-up attempt status is {signUp?.status}.</div>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-sign-up.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Create a custom sign-up flow with `useSignUp()`\n *\n * The `useSignUp()` hook can also be used to build fully custom sign-up flows, if Clerk's prebuilt components don't meet your specific needs or if you require more control over the authentication flow. Different sign-up flows include email and password, email and phone codes, email links, and multifactor (MFA). To learn more about using the `useSignUp()` hook to create custom flows, see the [custom flow guides](https://clerk.com/docs/custom-flows/overview).\n *\n * ```empty```\n */\nexport const useSignUp = (): UseSignUpReturn => {\n  useAssertWrappedByClerkProvider('useSignUp');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useSignUp'));\n\n  if (!client) {\n    return { isLoaded: false, signUp: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "export { useAuth } from './useAuth';\nexport { useEmailLink } from './useEmailLink';\nexport { useSignIn } from './useSignIn';\nexport { useSignUp } from './useSignUp';\nexport {\n  useClerk,\n  useOrganization,\n  useOrganizationList,\n  useSessionList,\n  useUser,\n  useSession,\n  useReverification,\n} from '@clerk/shared/react';\n", "import { deprecated } from '@clerk/shared/deprecated';\nimport type { HandleOAuthCallbackParams, PendingSessionOptions, ProtectProps as _ProtectProps } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { useAuth } from '../hooks';\nimport { useAssertWrappedByClerkProvider } from '../hooks/useAssertWrappedByClerkProvider';\nimport type { RedirectToSignInProps, RedirectToSignUpProps, WithClerkProp } from '../types';\nimport { withClerk } from './withClerk';\n\nexport const SignedIn = ({ children, treatPendingAsSignedOut }: React.PropsWithChildren<PendingSessionOptions>) => {\n  useAssertWrappedByClerkProvider('SignedIn');\n\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId) {\n    return children;\n  }\n  return null;\n};\n\nexport const SignedOut = ({ children, treatPendingAsSignedOut }: React.PropsWithChildren<PendingSessionOptions>) => {\n  useAssertWrappedByClerkProvider('SignedOut');\n\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId === null) {\n    return children;\n  }\n  return null;\n};\n\nexport const ClerkLoaded = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkLoaded');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkLoading = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkLoading');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'loading') {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkFailed = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkFailed');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'error') {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkDegraded = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkDegraded');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'degraded') {\n    return null;\n  }\n  return children;\n};\n\nexport type ProtectProps = React.PropsWithChildren<\n  _ProtectProps & {\n    fallback?: React.ReactNode;\n  } & PendingSessionOptions\n>;\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport const Protect = ({ children, fallback, treatPendingAsSignedOut, ...restAuthorizedParams }: ProtectProps) => {\n  useAssertWrappedByClerkProvider('Protect');\n\n  const { isLoaded, has, userId } = useAuth({ treatPendingAsSignedOut });\n\n  /**\n   * Avoid flickering children or fallback while clerk is loading sessionId or userId\n   */\n  if (!isLoaded) {\n    return null;\n  }\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = fallback ?? null;\n\n  const authorized = children;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  if (\n    restAuthorizedParams.role ||\n    restAuthorizedParams.permission ||\n    restAuthorizedParams.feature ||\n    restAuthorizedParams.plan\n  ) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n};\n\nexport const RedirectToSignIn = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignInProps>) => {\n  const { client, session } = clerk;\n\n  const hasSignedInSessions = client.signedInSessions\n    ? client.signedInSessions.length > 0\n    : // Compat for clerk-js<5.54.0 (which was released with the `signedInSessions` property)\n      client.activeSessions && client.activeSessions.length > 0;\n\n  React.useEffect(() => {\n    if (session === null && hasSignedInSessions) {\n      void clerk.redirectToAfterSignOut();\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n\n  return null;\n}, 'RedirectToSignIn');\n\nexport const RedirectToSignUp = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignUpProps>) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n\n  return null;\n}, 'RedirectToSignUp');\n\n/**\n * @function\n * @deprecated Use [`redirectToUserProfile()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-user-profile) instead.\n */\nexport const RedirectToUserProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToUserProfile', 'Use the `redirectToUserProfile()` method instead.');\n    void clerk.redirectToUserProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToUserProfile');\n\n/**\n * @function\n * @deprecated Use [`redirectToOrganizationProfile()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-organization-profile) instead.\n */\nexport const RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToOrganizationProfile', 'Use the `redirectToOrganizationProfile()` method instead.');\n    void clerk.redirectToOrganizationProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToOrganizationProfile');\n\n/**\n * @function\n * @deprecated Use [`redirectToCreateOrganization()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-create-organization) instead.\n */\nexport const RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToCreateOrganization', 'Use the `redirectToCreateOrganization()` method instead.');\n    void clerk.redirectToCreateOrganization();\n  }, []);\n\n  return null;\n}, 'RedirectToCreateOrganization');\n\nexport const AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }: WithClerkProp<HandleOAuthCallbackParams>) => {\n    React.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n\n    return null;\n  },\n  'AuthenticateWithRedirectCallback',\n);\n\nexport const MultisessionAppSupport = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('MultisessionAppSupport');\n\n  const session = useSessionContext();\n  return <React.Fragment key={session ? session.id : 'no-users'}>{children}</React.Fragment>;\n};\n", "export { SessionContext, useSessionContext } from '@clerk/shared/react';\n", "import type { LoadedClerk, Without } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from '../hooks/useAssertWrappedByClerkProvider';\n\nexport const withClerk = <P extends { clerk: LoadedClerk; component?: string }>(\n  Component: React.ComponentType<P>,\n  displayNameOrOptions?: string | { component: string; renderWhileLoading?: boolean },\n) => {\n  const passedDisplayedName =\n    typeof displayNameOrOptions === 'string' ? displayNameOrOptions : displayNameOrOptions?.component;\n  const displayName = passedDisplayedName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n\n  const options = typeof displayNameOrOptions === 'string' ? undefined : displayNameOrOptions;\n\n  const HOC = (props: Without<P, 'clerk'>) => {\n    useAssertWrappedByClerkProvider(displayName || 'withClerk');\n\n    const clerk = useIsomorphicClerkContext();\n\n    if (!clerk.loaded && !options?.renderWhileLoading) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        component={displayName}\n        clerk={clerk}\n      />\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n", "import type { OrganizationMembershipResource } from '@clerk/types';\n\n/**\n * Finds the organization membership for a given organization ID from a list of memberships\n * @param organizationMemberships - Array of organization memberships to search through\n * @param organizationId - ID of the organization to find the membership for\n * @returns The matching organization membership or undefined if not found\n */\nexport function getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  organizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === organizationId,\n  );\n}\n", "export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n", "import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n", "'use client';\nimport React from 'react';\n\n/**\n * Assert that the context value exists, otherwise throw an error.\n *\n * @internal\n */\nexport function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal {\n  if (!contextVal) {\n    throw typeof msgOrCtx === 'string' ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\n\ntype Options = { assertCtxFn?: (v: unknown, msg: string) => void };\ntype ContextOf<T> = React.Context<{ value: T } | undefined>;\ntype UseCtxFn<T> = () => T;\n\n/**\n * Create and return a Context and two hooks that return the context value.\n * The Context type is derived from the type passed in by the user.\n *\n * The first hook returned guarantees that the context exists so the returned value is always `CtxValue`\n * The second hook makes no guarantees, so the returned value can be `CtxValue | undefined`\n *\n * @internal\n */\nexport const createContextAndHook = <CtxVal>(\n  displayName: string,\n  options?: Options,\n): [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>] => {\n  const { assertCtxFn = assertContextExists } = options || {};\n  const Ctx = React.createContext<{ value: CtxVal } | undefined>(undefined);\n  Ctx.displayName = displayName;\n\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return (ctx as any).value as CtxVal;\n  };\n\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n", "'use client';\n\nimport type {\n  ClerkO<PERSON>s,\n  ClientResource,\n  LoadedClerk,\n  OrganizationResource,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { SWRConfig } from './clerk-swr';\nimport { createContextAndHook } from './hooks/createContextAndHook';\n\nconst [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook<LoadedClerk>('ClerkInstanceContext');\nconst [UserContext, useUserContext] = createContextAndHook<UserResource | null | undefined>('UserContext');\nconst [ClientContext, useClientContext] = createContextAndHook<ClientResource | null | undefined>('ClientContext');\nconst [SessionContext, useSessionContext] = createContextAndHook<SignedInSessionResource | null | undefined>(\n  'SessionContext',\n);\n\nconst OptionsContext = React.createContext<ClerkOptions>({});\n\nfunction useOptionsContext(): ClerkOptions {\n  const context = React.useContext(OptionsContext);\n  if (context === undefined) {\n    throw new Error('useOptions must be used within an OptionsContext');\n  }\n  return context;\n}\n\ntype OrganizationContextProps = {\n  organization: OrganizationResource | null | undefined;\n};\nconst [OrganizationContextInternal, useOrganizationContext] = createContextAndHook<{\n  organization: OrganizationResource | null | undefined;\n}>('OrganizationContext');\n\nconst OrganizationProvider = ({\n  children,\n  organization,\n  swrConfig,\n}: PropsWithChildren<\n  OrganizationContextProps & {\n    // Exporting inferred types  directly from SWR will result in error while building declarations\n    swrConfig?: any;\n  }\n>) => {\n  return (\n    <SWRConfig value={swrConfig}>\n      <OrganizationContextInternal.Provider\n        value={{\n          value: { organization },\n        }}\n      >\n        {children}\n      </OrganizationContextInternal.Provider>\n    </SWRConfig>\n  );\n};\n\nfunction useAssertWrappedByClerkProvider(displayNameOrFn: string | (() => void)): void {\n  const ctx = React.useContext(ClerkInstanceContext);\n\n  if (!ctx) {\n    if (typeof displayNameOrFn === 'function') {\n      displayNameOrFn();\n      return;\n    }\n\n    throw new Error(\n      `${displayNameOrFn} can only be used within the <ClerkProvider /> component.\n\nPossible fixes:\n1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.\n2. Check for multiple versions of the \\`@clerk/shared\\` package in your project. Use a tool like \\`npm ls @clerk/shared\\` to identify multiple versions, and update your dependencies to only rely on one.\n\nLearn more: https://clerk.com/docs/components/clerk-provider`.trim(),\n    );\n  }\n}\n\nexport {\n  ClientContext,\n  useClientContext,\n  OrganizationProvider,\n  useOrganizationContext,\n  UserContext,\n  OptionsContext,\n  useOptionsContext,\n  useUserContext,\n  SessionContext,\n  useSessionContext,\n  ClerkInstanceContext,\n  useClerkInstanceContext,\n  useAssertWrappedByClerkProvider,\n};\n", "'use client';\n\nexport * from 'swr';\n\nexport { default as useSWR } from 'swr';\nexport { default as useSWRInfinite } from 'swr/infinite';\n", "'use client';\n\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nimport { useSWR, useSWRInfinite } from '../clerk-swr';\nimport type {\n  CacheSetter,\n  PagesOrInfiniteConfig,\n  PagesOrInfiniteOptions,\n  PaginatedResources,\n  ValueOrSetter,\n} from '../types';\n\n/**\n * Returns an object containing only the keys from the first object that are not present in the second object.\n * Useful for extracting unique parameters that should be passed to a request while excluding common cache keys.\n *\n * @internal\n *\n * @example\n * ```typescript\n * // Example 1: Basic usage\n * const obj1 = { name: '<PERSON>', age: 30, city: 'NY' };\n * const obj2 = { name: '<PERSON>', age: 30 };\n * getDifferentKeys(obj1, obj2); // Returns { city: 'NY' }\n *\n * // Example 2: With cache keys\n * const requestParams = { page: 1, limit: 10, userId: '123' };\n * const cacheKeys = { userId: '123' };\n * getDifferentKeys(requestParams, cacheKeys); // Returns { page: 1, limit: 10 }\n * ```\n */\nfunction getDifferentKeys(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject: Record<string, unknown> = {};\n\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n\n  return differentKeysObject;\n}\n\n/**\n * A hook that safely merges user-provided pagination options with default values.\n * It caches initial pagination values (page and size) until component unmount to prevent unwanted rerenders.\n *\n * @internal\n *\n * @example\n * ```typescript\n * // Example 1: With user-provided options\n * const userOptions = { initialPage: 2, pageSize: 20, infinite: true };\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(userOptions, defaults);\n * // Returns { initialPage: 2, pageSize: 20, infinite: true }\n *\n * // Example 2: With boolean true (use defaults)\n * const params = true;\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(params, defaults);\n * // Returns { initialPage: 1, pageSize: 10, infinite: false }\n *\n * // Example 3: With undefined options (fallback to defaults)\n * const params = undefined;\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(params, defaults);\n * // Returns { initialPage: 1, pageSize: 10, infinite: false }\n * ```\n */\nexport const useWithSafeValues = <T extends PagesOrInfiniteOptions>(params: T | true | undefined, defaultValues: T) => {\n  const shouldUseDefaults = typeof params === 'boolean' && params;\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(\n    shouldUseDefaults ? defaultValues.initialPage : (params?.initialPage ?? defaultValues.initialPage),\n  );\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : (params?.pageSize ?? defaultValues.pageSize));\n\n  const newObj: Record<string, unknown> = {};\n  for (const key of Object.keys(defaultValues)) {\n    // @ts-ignore\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : (params?.[key] ?? defaultValues[key]);\n  }\n\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current,\n  } as T;\n};\n\nconst cachingSWROptions = {\n  dedupingInterval: 1000 * 60,\n  focusThrottleInterval: 1000 * 60 * 2,\n} satisfies Parameters<typeof useSWR>[2];\n\ntype ArrayType<DataArray> = DataArray extends Array<infer ElementType> ? ElementType : never;\ntype ExtractData<Type> = Type extends { data: infer Data } ? ArrayType<Data> : Type;\n\ntype UsePagesOrInfinite = <\n  Params extends PagesOrInfiniteOptions,\n  FetcherReturnData extends Record<string, any>,\n  CacheKeys extends Record<string, unknown> = Record<string, unknown>,\n  TConfig extends PagesOrInfiniteConfig = PagesOrInfiniteConfig,\n>(\n  /**\n   * The parameters will be passed to the fetcher.\n   */\n  params: Params,\n  /**\n   * A Promise returning function to fetch your data.\n   */\n  fetcher: ((p: Params) => FetcherReturnData | Promise<FetcherReturnData>) | undefined,\n  /**\n   * Internal configuration of the hook.\n   */\n  config: TConfig,\n  cacheKeys: CacheKeys,\n) => PaginatedResources<ExtractData<FetcherReturnData>, TConfig['infinite']>;\n\n/**\n * A flexible pagination hook that supports both traditional pagination and infinite loading.\n * It provides a unified API for handling paginated data fetching, with built-in caching through SWR.\n * The hook can operate in two modes:\n * - Traditional pagination: Fetches one page at a time with page navigation\n * - Infinite loading: Accumulates data as more pages are loaded.\n *\n * Features:\n * - Cache management with SWR\n * - Loading and error states\n * - Page navigation helpers\n * - Data revalidation and updates\n * - Support for keeping previous data while loading.\n *\n * @internal\n */\nexport const usePagesOrInfinite: UsePagesOrInfinite = (params, fetcher, config, cacheKeys) => {\n  const [paginatedPage, setPaginatedPage] = useState(params.initialPage ?? 1);\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(params.initialPage ?? 1);\n  const pageSizeRef = useRef(params.pageSize ?? 10);\n\n  const enabled = config.enabled ?? true;\n  const cacheMode = config.__experimental_mode === 'cache';\n  const triggerInfinite = config.infinite ?? false;\n  const keepPreviousData = config.keepPreviousData ?? false;\n\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current,\n  };\n\n  // cacheMode being `true` indicates that the cache key is defined, but the fetcher is not.\n  // This allows to ready the cache instead of firing a request.\n  const shouldFetch = !triggerInfinite && enabled && (!cacheMode ? !!fetcher : true);\n  const swrKey = shouldFetch ? pagesCacheKey : null;\n  const swrFetcher =\n    !cacheMode && !!fetcher\n      ? (cacheKeyParams: Record<string, unknown>) => {\n          const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n          return fetcher({ ...params, ...requestParams });\n        }\n      : null;\n\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate,\n  } = useSWR(swrKey, swrFetcher, { keepPreviousData, ...cachingSWROptions });\n\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate,\n  } = useSWRInfinite(\n    pageIndex => {\n      if (!triggerInfinite || !enabled) {\n        return null;\n      }\n\n      return {\n        ...params,\n        ...cacheKeys,\n        initialPage: initialPageRef.current + pageIndex,\n        pageSize: pageSizeRef.current,\n      };\n    },\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n    cachingSWROptions,\n  );\n\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n\n  const fetchPage: ValueOrSetter<number> = useCallback(\n    numberOrgFn => {\n      if (triggerInfinite) {\n        void setSize(numberOrgFn);\n        return;\n      }\n      return setPaginatedPage(numberOrgFn);\n    },\n    [setSize],\n  );\n\n  const data = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.map(a => a?.data).flat() ?? [];\n    }\n    return swrData?.data ?? [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const count = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.[swrInfiniteData?.length - 1]?.total_count || 0;\n    }\n    return swrData?.total_count ?? 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const error = (triggerInfinite ? swrInfiniteError : swrError) ?? null;\n  const isError = !!error;\n  /**\n   * Helpers.\n   */\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n\n  const setData: CacheSetter = triggerInfinite\n    ? value =>\n        swrInfiniteMutate(value, {\n          revalidate: false,\n        })\n    : value =>\n        swrMutate(value, {\n          revalidate: false,\n        });\n\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n\n  return {\n    data,\n    count,\n    error,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate: revalidate as any,\n    // Let the hook return type define this type\n    setData: setData as any,\n  };\n};\n", "/* eslint-disable jsdoc/require-description-complete-sentence */\nimport type {\n  ClerkPaginatedResponse,\n  CommerceSubscriptionResource,\n  GetDomainsParams,\n  GetInvitationsParams,\n  GetMembershipRequestParams,\n  GetMembersParams,\n  GetSubscriptionsParams,\n  OrganizationDomainResource,\n  OrganizationInvitationResource,\n  OrganizationMembershipRequestResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n} from '@clerk/types';\n\nimport { getCurrentOrganizationMembership } from '../../organization';\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport {\n  useAssertWrappedByClerkProvider,\n  useClerkInstanceContext,\n  useOrganizationContext,\n  useSessionContext,\n} from '../contexts';\nimport type { PaginatedHookConfig, PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @interface\n */\nexport type UseOrganizationParams = {\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`enrollmentMode`: A string that filters the domains by the provided [enrollment mode](https://clerk.com/docs/organizations/verified-domains#enrollment-mode).</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  domains?: true | PaginatedHookConfig<GetDomainsParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`status`: A string that filters the membership requests by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  membershipRequests?: true | PaginatedHookConfig<GetMembershipRequestParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`role`: An array of [`OrganizationCustomRoleKey`](https://clerk.com/docs/references/javascript/types/organization-custom-role-key).</li>\n   *  <li>`query`: A string that filters the memberships by the provided string.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  memberships?: true | PaginatedHookConfig<GetMembersParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`status`: A string that filters the invitations by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  invitations?: true | PaginatedHookConfig<GetInvitationsParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`orgId`: A string that filters the subscriptions by the provided organization ID.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  subscriptions?: true | PaginatedHookConfig<GetSubscriptionsParams>;\n};\n\n/**\n * @interface\n */\nexport type UseOrganizationReturn<T extends UseOrganizationParams> =\n  | {\n      /**\n       * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.\n       */\n      isLoaded: false;\n      /**\n       * The currently active organization.\n       */\n      organization: undefined;\n      /**\n       * The current organization membership.\n       */\n      membership: undefined;\n      /**\n       * Includes a paginated list of the organization's domains.\n       */\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      /**\n       * Includes a paginated list of the organization's membership requests.\n       */\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      /**\n       * Includes a paginated list of the organization's memberships.\n       */\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      /**\n       * Includes a paginated list of the organization's invitations.\n       */\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n      /**\n       * Includes a paginated list of the organization's subscriptions.\n       */\n      subscriptions: PaginatedResourcesWithDefault<CommerceSubscriptionResource>;\n    }\n  | {\n      isLoaded: true;\n      organization: OrganizationResource;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n      subscriptions: PaginatedResourcesWithDefault<CommerceSubscriptionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      organization: OrganizationResource | null;\n      membership: OrganizationMembershipResource | null | undefined;\n      domains: PaginatedResources<\n        OrganizationDomainResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      membershipRequests: PaginatedResources<\n        OrganizationMembershipRequestResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      memberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['memberships'] extends { infinite: true } ? true : false\n      > | null;\n      invitations: PaginatedResources<\n        OrganizationInvitationResource,\n        T['invitations'] extends { infinite: true } ? true : false\n      > | null;\n      subscriptions: PaginatedResources<\n        CommerceSubscriptionResource,\n        T['subscriptions'] extends { infinite: true } ? true : false\n      > | null;\n    };\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  error: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\n/**\n * The `useOrganization()` hook retrieves attributes of the currently active organization.\n *\n * @example\n * ### Expand and paginate attributes\n *\n * To keep network usage to a minimum, developers are required to opt-in by specifying which resource they need to fetch and paginate through. By default, the `memberships`, `invitations`, `membershipRequests`, and `domains` attributes are not populated. You must pass `true` or an object with the desired properties to fetch and paginate the data.\n *\n * ```tsx\n * // invitations.data will never be populated.\n * const { invitations } = useOrganization()\n *\n * // Use default values to fetch invitations, such as initialPage = 1 and pageSize = 10\n * const { invitations } = useOrganization({\n *   invitations: true,\n * })\n *\n * // Pass your own values to fetch invitations\n * const { invitations } = useOrganization({\n *   invitations: {\n *     pageSize: 20,\n *     initialPage: 2, // skips the first page\n *   },\n * })\n *\n * // Aggregate pages in order to render an infinite list\n * const { invitations } = useOrganization({\n *   invitations: {\n *     infinite: true,\n *   },\n * })\n * ```\n *\n * @example\n * ### Infinite pagination\n *\n * The following example demonstrates how to use the `infinite` property to fetch and append new data to the existing list. The `memberships` attribute will be populated with the first page of the organization's memberships. When the \"Load more\" button is clicked, the `fetchNext` helper function will be called to append the next page of memberships to the list.\n *\n * ```tsx\n * import { useOrganization } from '@clerk/clerk-react'\n *\n * export default function MemberList() {\n *   const { memberships } = useOrganization({\n *     memberships: {\n *       infinite: true, // Append new data to the existing list\n *       keepPreviousData: true, // Persist the cached data until the new data has been fetched\n *     },\n *   })\n *\n *   if (!memberships) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <h2>Organization members</h2>\n *       <ul>\n *         {memberships.data?.map((membership) => (\n *           <li key={membership.id}>\n *             {membership.publicUserData.firstName} {membership.publicUserData.lastName} <\n *             {membership.publicUserData.identifier}> :: {membership.role}\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button\n *         disabled={!memberships.hasNextPage} // Disable the button if there are no more available pages to be fetched\n *         onClick={memberships.fetchNext}\n *       >\n *         Load more\n *       </button>\n *     </div>\n *   )\n * }\n * ```\n *\n * @example\n * ### Simple pagination\n *\n * The following example demonstrates how to use the `fetchPrevious` and `fetchNext` helper functions to paginate through the data. The `memberships` attribute will be populated with the first page of the organization's memberships. When the \"Previous page\" or \"Next page\" button is clicked, the `fetchPrevious` or `fetchNext` helper function will be called to fetch the previous or next page of memberships.\n *\n * Notice the difference between this example's pagination and the infinite pagination example above.\n *\n * ```tsx\n * import { useOrganization } from '@clerk/clerk-react'\n *\n * export default function MemberList() {\n *   const { memberships } = useOrganization({\n *     memberships: {\n *       keepPreviousData: true, // Persist the cached data until the new data has been fetched\n *     },\n *   })\n *\n *   if (!memberships) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <h2>Organization members</h2>\n *       <ul>\n *         {memberships.data?.map((membership) => (\n *           <li key={membership.id}>\n *             {membership.publicUserData.firstName} {membership.publicUserData.lastName} <\n *             {membership.publicUserData.identifier}> :: {membership.role}\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button disabled={!memberships.hasPreviousPage} onClick={memberships.fetchPrevious}>\n *         Previous page\n *       </button>\n *\n *       <button disabled={!memberships.hasNextPage} onClick={memberships.fetchNext}>\n *         Next page\n *       </button>\n *     </div>\n *   )\n * }\n * ```\n */\nexport function useOrganization<T extends UseOrganizationParams>(params?: T): UseOrganizationReturn<T> {\n  const {\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams,\n    subscriptions: subscriptionsListParams,\n  } = params || {};\n\n  useAssertWrappedByClerkProvider('useOrganization');\n\n  const { organization } = useOrganizationContext();\n  const session = useSessionContext();\n\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: undefined,\n  });\n\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: undefined,\n    keepPreviousData: false,\n    infinite: false,\n    query: undefined,\n  });\n\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: ['pending'],\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const subscriptionsSafeValues = useWithSafeValues(subscriptionsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled('useOrganization'));\n\n  const domainParams =\n    typeof domainListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: domainSafeValues.initialPage,\n          pageSize: domainSafeValues.pageSize,\n          enrollmentMode: domainSafeValues.enrollmentMode,\n        };\n\n  const membershipRequestParams =\n    typeof membershipRequestsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membershipRequestSafeValues.initialPage,\n          pageSize: membershipRequestSafeValues.pageSize,\n          status: membershipRequestSafeValues.status,\n        };\n\n  const membersParams =\n    typeof membersListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membersSafeValues.initialPage,\n          pageSize: membersSafeValues.pageSize,\n          role: membersSafeValues.role,\n          query: membersSafeValues.query,\n        };\n\n  const invitationsParams =\n    typeof invitationsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: invitationsSafeValues.initialPage,\n          pageSize: invitationsSafeValues.pageSize,\n          status: invitationsSafeValues.status,\n        };\n\n  const subscriptionsParams =\n    typeof subscriptionsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: subscriptionsSafeValues.initialPage,\n          pageSize: subscriptionsSafeValues.pageSize,\n          orgId: organization?.id,\n        };\n\n  const domains = usePagesOrInfinite<GetDomainsParams, ClerkPaginatedResponse<OrganizationDomainResource>>(\n    {\n      ...domainParams,\n    },\n    organization?.getDomains,\n    {\n      keepPreviousData: domainSafeValues.keepPreviousData,\n      infinite: domainSafeValues.infinite,\n      enabled: !!domainParams,\n    },\n    {\n      type: 'domains',\n      organizationId: organization?.id,\n    },\n  );\n\n  const membershipRequests = usePagesOrInfinite<\n    GetMembershipRequestParams,\n    ClerkPaginatedResponse<OrganizationMembershipRequestResource>\n  >(\n    {\n      ...membershipRequestParams,\n    },\n    organization?.getMembershipRequests,\n    {\n      keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n      infinite: membershipRequestSafeValues.infinite,\n      enabled: !!membershipRequestParams,\n    },\n    {\n      type: 'membershipRequests',\n      organizationId: organization?.id,\n    },\n  );\n\n  const memberships = usePagesOrInfinite<GetMembersParams, ClerkPaginatedResponse<OrganizationMembershipResource>>(\n    membersParams || {},\n    organization?.getMemberships,\n    {\n      keepPreviousData: membersSafeValues.keepPreviousData,\n      infinite: membersSafeValues.infinite,\n      enabled: !!membersParams,\n    },\n    {\n      type: 'members',\n      organizationId: organization?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<GetInvitationsParams, ClerkPaginatedResponse<OrganizationInvitationResource>>(\n    {\n      ...invitationsParams,\n    },\n    organization?.getInvitations,\n    {\n      keepPreviousData: invitationsSafeValues.keepPreviousData,\n      infinite: invitationsSafeValues.infinite,\n      enabled: !!invitationsParams,\n    },\n    {\n      type: 'invitations',\n      organizationId: organization?.id,\n    },\n  );\n\n  const subscriptions = usePagesOrInfinite<\n    GetSubscriptionsParams,\n    ClerkPaginatedResponse<CommerceSubscriptionResource>\n  >(\n    {\n      ...subscriptionsParams,\n    },\n    organization?.getSubscriptions,\n    {\n      keepPreviousData: subscriptionsSafeValues.keepPreviousData,\n      infinite: subscriptionsSafeValues.infinite,\n      enabled: !!subscriptionsParams,\n    },\n    {\n      type: 'subscriptions',\n      organizationId: organization?.id,\n    },\n  );\n\n  if (organization === undefined) {\n    return {\n      isLoaded: false,\n      organization: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n      subscriptions: undefinedPaginatedResource,\n    };\n  }\n\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null,\n      subscriptions: null,\n    };\n  }\n\n  /** In SSR context we include only the organization object when loadOrg is set to true. */\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n      subscriptions: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: clerk.loaded,\n    organization,\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    membership: getCurrentOrganizationMembership(session!.user.organizationMemberships, organization.id), // your membership in the current org\n    domains,\n    membershipRequests,\n    memberships,\n    invitations,\n    subscriptions,\n  };\n}\n", "/* eslint-disable jsdoc/require-description-complete-sentence */\nimport type {\n  ClerkPaginatedResponse,\n  CreateOrganizationParams,\n  GetUserOrganizationInvitationsParams,\n  GetUserOrganizationMembershipParams,\n  GetUserOrganizationSuggestionsParams,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  OrganizationSuggestionResource,\n  SetActive,\n  UserOrganizationInvitationResource,\n} from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useUserContext } from '../contexts';\nimport type { PaginatedHookConfig, PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @interface\n */\nexport type UseOrganizationListParams = {\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userMemberships?: true | PaginatedHookConfig<GetUserOrganizationMembershipParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>`status`: A string that filters the invitations by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userInvitations?: true | PaginatedHookConfig<GetUserOrganizationInvitationsParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>`status`: A string that filters the suggestions by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userSuggestions?: true | PaginatedHookConfig<GetUserOrganizationSuggestionsParams>;\n};\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  error: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\n/**\n * @interface\n */\nexport type UseOrganizationListReturn<T extends UseOrganizationListParams> =\n  | {\n      /**\n       * A boolean that indicates whether Clerk has completed initialization and there is an authenticated user. Initially `false`, becomes `true` once Clerk loads with a user.\n       */\n      isLoaded: false;\n      /**\n       * A function that returns a `Promise` which resolves to the newly created `Organization`.\n       */\n      createOrganization: undefined;\n      /**\n       * A function that sets the active session and/or organization.\n       */\n      setActive: undefined;\n      /**\n       * Returns `PaginatedResources` which includes a list of the user's organization memberships.\n       */\n      userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      /**\n       * Returns `PaginatedResources` which includes a list of the user's organization invitations.\n       */\n      userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;\n      /**\n       * Returns `PaginatedResources` which includes a list of suggestions for organizations that the user can join.\n       */\n      userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      createOrganization: (CreateOrganizationParams: CreateOrganizationParams) => Promise<OrganizationResource>;\n      setActive: SetActive;\n      userMemberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['userMemberships'] extends { infinite: true } ? true : false\n      >;\n      userInvitations: PaginatedResources<\n        UserOrganizationInvitationResource,\n        T['userInvitations'] extends { infinite: true } ? true : false\n      >;\n      userSuggestions: PaginatedResources<\n        OrganizationSuggestionResource,\n        T['userSuggestions'] extends { infinite: true } ? true : false\n      >;\n    };\n\n/**\n * The `useOrganizationList()` hook provides access to the current user's organization memberships, invitations, and suggestions. It also includes methods for creating new organizations and managing the active organization.\n *\n * @example\n * ### Expanding and paginating attributes\n *\n * To keep network usage to a minimum, developers are required to opt-in by specifying which resource they need to fetch and paginate through. So by default, the `userMemberships`, `userInvitations`, and `userSuggestions` attributes are not populated. You must pass true or an object with the desired properties to fetch and paginate the data.\n *\n * ```tsx\n * // userMemberships.data will never be populated\n * const { userMemberships } = useOrganizationList()\n *\n * // Use default values to fetch userMemberships, such as initialPage = 1 and pageSize = 10\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: true,\n * })\n *\n * // Pass your own values to fetch userMemberships\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: {\n *     pageSize: 20,\n *     initialPage: 2, // skips the first page\n *   },\n * })\n *\n * // Aggregate pages in order to render an infinite list\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: {\n *     infinite: true,\n *   },\n * })\n * ```\n *\n * @example\n * ### Infinite pagination\n *\n * The following example demonstrates how to use the `infinite` property to fetch and append new data to the existing list. The `userMemberships` attribute will be populated with the first page of the user's organization memberships. When the \"Load more\" button is clicked, the `fetchNext` helper function will be called to append the next page of memberships to the list.\n *\n * ```tsx {{ filename: 'src/components/JoinedOrganizations.tsx' }}\n * import { useOrganizationList } from '@clerk/clerk-react'\n * import React from 'react'\n *\n * const JoinedOrganizations = () => {\n *   const { isLoaded, setActive, userMemberships } = useOrganizationList({\n *     userMemberships: {\n *       infinite: true,\n *     },\n *   })\n *\n *   if (!isLoaded) {\n *     return <>Loading</>\n *   }\n *\n *   return (\n *     <>\n *       <ul>\n *         {userMemberships.data?.map((mem) => (\n *           <li key={mem.id}>\n *             <span>{mem.organization.name}</span>\n *             <button onClick={() => setActive({ organization: mem.organization.id })}>Select</button>\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button disabled={!userMemberships.hasNextPage} onClick={() => userMemberships.fetchNext()}>\n *         Load more\n *       </button>\n *     </>\n *   )\n * }\n *\n * export default JoinedOrganizations\n * ```\n *\n * @example\n * ### Simple pagination\n *\n * The following example demonstrates how to use the `fetchPrevious` and `fetchNext` helper functions to paginate through the data. The `userInvitations` attribute will be populated with the first page of invitations. When the \"Previous page\" or \"Next page\" button is clicked, the `fetchPrevious` or `fetchNext` helper function will be called to fetch the previous or next page of invitations.\n *\n * Notice the difference between this example's pagination and the infinite pagination example above.\n *\n * ```tsx {{ filename: 'src/components/UserInvitationsTable.tsx' }}\n * import { useOrganizationList } from '@clerk/clerk-react'\n * import React from 'react'\n *\n * const UserInvitationsTable = () => {\n *   const { isLoaded, userInvitations } = useOrganizationList({\n *     userInvitations: {\n *       infinite: true,\n *       keepPreviousData: true,\n *     },\n *   })\n *\n *   if (!isLoaded || userInvitations.isLoading) {\n *     return <>Loading</>\n *   }\n *\n *   return (\n *     <>\n *       <table>\n *         <thead>\n *           <tr>\n *             <th>Email</th>\n *             <th>Org name</th>\n *           </tr>\n *         </thead>\n *\n *         <tbody>\n *           {userInvitations.data?.map((inv) => (\n *             <tr key={inv.id}>\n *               <th>{inv.emailAddress}</th>\n *               <th>{inv.publicOrganizationData.name}</th>\n *             </tr>\n *           ))}\n *         </tbody>\n *       </table>\n *\n *       <button disabled={!userInvitations.hasPreviousPage} onClick={userInvitations.fetchPrevious}>\n *         Prev\n *       </button>\n *       <button disabled={!userInvitations.hasNextPage} onClick={userInvitations.fetchNext}>\n *         Next\n *       </button>\n *     </>\n *   )\n * }\n *\n * export default UserInvitationsTable\n * ```\n */\nexport function useOrganizationList<T extends UseOrganizationListParams>(params?: T): UseOrganizationListReturn<T> {\n  const { userMemberships, userInvitations, userSuggestions } = params || {};\n\n  useAssertWrappedByClerkProvider('useOrganizationList');\n\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n\n  clerk.telemetry?.record(eventMethodCalled('useOrganizationList'));\n\n  const userMembershipsParams =\n    typeof userMemberships === 'undefined'\n      ? undefined\n      : {\n          initialPage: userMembershipsSafeValues.initialPage,\n          pageSize: userMembershipsSafeValues.pageSize,\n        };\n\n  const userInvitationsParams =\n    typeof userInvitations === 'undefined'\n      ? undefined\n      : {\n          initialPage: userInvitationsSafeValues.initialPage,\n          pageSize: userInvitationsSafeValues.pageSize,\n          status: userInvitationsSafeValues.status,\n        };\n\n  const userSuggestionsParams =\n    typeof userSuggestions === 'undefined'\n      ? undefined\n      : {\n          initialPage: userSuggestionsSafeValues.initialPage,\n          pageSize: userSuggestionsSafeValues.pageSize,\n          status: userSuggestionsSafeValues.status,\n        };\n\n  const isClerkLoaded = !!(clerk.loaded && user);\n\n  const memberships = usePagesOrInfinite<\n    GetUserOrganizationMembershipParams,\n    ClerkPaginatedResponse<OrganizationMembershipResource>\n  >(\n    userMembershipsParams || {},\n    user?.getOrganizationMemberships,\n    {\n      keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n      infinite: userMembershipsSafeValues.infinite,\n      enabled: !!userMembershipsParams,\n    },\n    {\n      type: 'userMemberships',\n      userId: user?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<\n    GetUserOrganizationInvitationsParams,\n    ClerkPaginatedResponse<UserOrganizationInvitationResource>\n  >(\n    {\n      ...userInvitationsParams,\n    },\n    user?.getOrganizationInvitations,\n    {\n      keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n      infinite: userInvitationsSafeValues.infinite,\n      enabled: !!userInvitationsParams,\n    },\n    {\n      type: 'userInvitations',\n      userId: user?.id,\n    },\n  );\n\n  const suggestions = usePagesOrInfinite<\n    GetUserOrganizationSuggestionsParams,\n    ClerkPaginatedResponse<OrganizationSuggestionResource>\n  >(\n    {\n      ...userSuggestionsParams,\n    },\n    user?.getOrganizationSuggestions,\n    {\n      keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n      infinite: userSuggestionsSafeValues.infinite,\n      enabled: !!userSuggestionsParams,\n    },\n    {\n      type: 'userSuggestions',\n      userId: user?.id,\n    },\n  );\n\n  // TODO: Properly check for SSR user values\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      createOrganization: undefined,\n      setActive: undefined,\n      userMemberships: undefinedPaginatedResource,\n      userInvitations: undefinedPaginatedResource,\n      userSuggestions: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: isClerkLoaded,\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions,\n  };\n}\n", "import React from 'react';\n\n/**\n * @internal\n */\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import type { PendingSessionOptions, UseSessionReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useSessionContext } from '../contexts';\n\ntype UseSession = (options?: PendingSessionOptions) => UseSessionReturn;\n\nconst hookName = `useSession`;\n/**\n * The `useSession()` hook provides access to the current user's [`Session`](https://clerk.com/docs/references/javascript/session) object, as well as helpers for setting the active session.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in\"]\n *\n * @function\n *\n * @param [options] - An object containing options for the `useSession()` hook.\n *\n * @example\n * ### Access the `Session` object\n *\n * The following example uses the `useSession()` hook to access the `Session` object, which has the `lastActiveAt` property. The `lastActiveAt` property is a `Date` object used to show the time the session was last active.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useSession } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, session, isSignedIn } = useSession()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *   if (!isSignedIn) {\n *     // Handle signed out state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <p>This session has been active since {session.lastActiveAt.toLocaleString()}</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-session.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useSession: UseSession = (options = {}) => {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const session = useSessionContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (session === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, session: undefined };\n  }\n\n  const pendingAsSignedOut =\n    session?.status === 'pending' &&\n    (options.treatPendingAsSignedOut ?? clerk.__internal_getOption('treatPendingAsSignedOut'));\n  const isSignedOut = session === null || pendingAsSignedOut;\n  if (isSignedOut) {\n    return { isLoaded: true, isSignedIn: false, session: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, session };\n};\n", "import type { UseSessionListReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useClientContext } from '../contexts';\n\nconst hookName = 'useSessionList';\n/**\n * The `useSessionList()` hook returns an array of [`Session`](https://clerk.com/docs/references/javascript/session) objects that have been registered on the client device.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @function\n *\n * @example\n * ### Get a list of sessions\n *\n * The following example uses `useSessionList()` to get a list of sessions that have been registered on the client device. The `sessions` property is used to show the number of times the user has visited the page.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useSessionList } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, sessions } = useSessionList()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <p>Welcome back. You've been here {sessions.length} times before.</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-session-list.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useSessionList = (): UseSessionListReturn => {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const isomorphicClerk = useClerkInstanceContext();\n  const client = useClientContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (!client) {\n    return { isLoaded: false, sessions: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import type { UseUserReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useUserContext } from '../contexts';\n\nconst hookName = 'useUser';\n/**\n * The `useUser()` hook provides access to the current user's [`User`](https://clerk.com/docs/references/javascript/user) object, which contains all the data for a single user in your application and provides methods to manage their account. This hook also allows you to check if the user is signed in and if Clerk has loaded and initialized.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in\"]\n *\n * @example\n * ### Get the current user\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which contains the current user's data such as their full name. The `isLoaded` and `isSignedIn` properties are used to handle the loading state and to check if the user is signed in, respectively.\n *\n * ```tsx {{ filename: 'src/Example.tsx' }}\n * export default function Example() {\n *   const { isSignedIn, user, isLoaded } = useUser()\n *\n *   if (!isLoaded) {\n *     return <div>Loading...</div>\n *   }\n *\n *   if (!isSignedIn) {\n *     return <div>Sign in to view this page</div>\n *   }\n *\n *   return <div>Hello {user.firstName}!</div>\n * }\n * ```\n *\n * @example\n * ### Update user data\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which calls the [`update()`](https://clerk.com/docs/references/javascript/user#update) method to update the current user's information.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useUser } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, user } = useUser()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   if (!user) return null\n *\n *   const updateUser = async () => {\n *     await user.update({\n *       firstName: 'John',\n *       lastName: 'Doe',\n *     })\n *   }\n *\n *   return (\n *     <>\n *       <button onClick={updateUser}>Update your name</button>\n *       <p>user.firstName: {user?.firstName}</p>\n *       <p>user.lastName: {user?.lastName}</p>\n *     </>\n *   )\n * }\n * ```\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-user.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Reload user data\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which calls the [`reload()`](https://clerk.com/docs/references/javascript/user#reload) method to get the latest user's information.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useUser } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, user } = useUser()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   if (!user) return null\n *\n *   const updateUser = async () => {\n *     // Update data via an API endpoint\n *     const updateMetadata = await fetch('/api/updateMetadata')\n *\n *     // Check if the update was successful\n *     if (updateMetadata.message !== 'success') {\n *       throw new Error('Error updating')\n *     }\n *\n *     // If the update was successful, reload the user data\n *     await user.reload()\n *   }\n *\n *   return (\n *     <>\n *       <button onClick={updateUser}>Update your metadata</button>\n *       <p>user role: {user?.publicMetadata.role}</p>\n *     </>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-user.md#nextjs-02}\n *\n * </Tab>\n * </Tabs>\n */\nexport function useUser(): UseUserReturn {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const user = useUserContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (user === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, user: undefined };\n  }\n\n  if (user === null) {\n    return { isLoaded: true, isSignedIn: false, user: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, user };\n}\n", "import type { LoadedClerk } from '@clerk/types';\n\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext } from '../contexts';\n\n/**\n * > [!WARNING]\n * > This hook should only be used for advanced use cases, such as building a completely custom OAuth flow or as an escape hatch to access to the `Clerk` object.\n *\n * The `useClerk()` hook provides access to the [`Clerk`](https://clerk.com/docs/references/javascript/clerk) object, allowing you to build alternatives to any Clerk Component.\n *\n * @function\n *\n * @returns The `useClerk()` hook returns the `Clerk` object, which includes all the methods and properties listed in the [`Clerk` reference](https://clerk.com/docs/references/javascript/clerk).\n *\n * @example\n *\n * The following example uses the `useClerk()` hook to access the `clerk` object. The `clerk` object is used to call the [`openSignIn()`](https://clerk.com/docs/references/javascript/clerk#sign-in) method to open the sign-in modal.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useClerk } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const clerk = useClerk()\n *\n *   return <button onClick={() => clerk.openSignIn({})}>Sign in</button>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-clerk.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useClerk = (): LoadedClerk => {\n  useAssertWrappedByClerkProvider('useClerk');\n  return useClerkInstanceContext();\n};\n", "import { dequal as deepEqual } from 'dequal';\nimport React from 'react';\n\ntype UseMemoFactory<T> = () => T;\ntype UseMemoDependencyArray = Exclude<Parameters<typeof React.useMemo>[1], 'undefined'>;\ntype UseDeepEqualMemo = <T>(factory: UseMemoFactory<T>, dependencyArray: UseMemoDependencyArray) => T;\n\nconst useDeepEqualMemoize = <T>(value: T) => {\n  const ref = React.useRef<T>(value);\n  if (!deepEqual(value, ref.current)) {\n    ref.current = value;\n  }\n  return React.useMemo(() => ref.current, [ref.current]);\n};\n\n/**\n * @internal\n */\nexport const useDeepEqualMemo: UseDeepEqualMemo = (factory, dependencyArray) => {\n  return React.useMemo(factory, useDeepEqualMemoize(dependencyArray));\n};\n\n/**\n * @internal\n */\nexport const isDeeplyEqual = deepEqual;\n", "import type { Clerk, SessionVerificationLevel } from '@clerk/types';\nimport { useCallback, useRef } from 'react';\n\nimport { validateReverificationConfig } from '../../authorization';\nimport { isReverificationHint, reverificationError } from '../../authorization-errors';\nimport { ClerkRuntimeError, isClerkAPIResponseError } from '../../error';\nimport { eventMethodCalled } from '../../telemetry';\nimport { createDeferredPromise } from '../../utils/createDeferredPromise';\nimport { useClerk } from './useClerk';\nimport { useSafeLayoutEffect } from './useSafeLayoutEffect';\n\nconst CLERK_API_REVERIFICATION_ERROR_CODE = 'session_reverification_required';\n\nasync function resolveResult<T>(result: Promise<T> | T): Promise<T | ReturnType<typeof reverificationError>> {\n  try {\n    const r = await result;\n    if (r instanceof Response) {\n      return r.json();\n    }\n    return r;\n  } catch (e) {\n    // Treat fapi assurance as an assurance hint\n    if (isClerkAPIResponseError(e) && e.errors.find(({ code }) => code === CLERK_API_REVERIFICATION_ERROR_CODE)) {\n      return reverificationError();\n    }\n\n    // rethrow\n    throw e;\n  }\n}\n\ntype ExcludeClerkError<T> = T extends { clerk_error: any } ? never : T;\n\n/**\n * @interface\n */\ntype NeedsReverificationParameters = {\n  cancel: () => void;\n  complete: () => void;\n  level: SessionVerificationLevel | undefined;\n};\n\n/**\n * The optional options object.\n * @interface\n */\ntype UseReverificationOptions = {\n  /**\n   * A handler that is called when reverification is needed, this will opt-out of using the default UI when provided.\n   *\n   * @param cancel - A function that will cancel the reverification process.\n   * @param complete - A function that will retry the original request after reverification.\n   * @param level - The level returned with the reverification hint.\n   *\n   */\n  onNeedsReverification?: (properties: NeedsReverificationParameters) => void;\n};\n\n/**\n * @interface\n */\ntype UseReverificationResult<Fetcher extends (...args: any[]) => Promise<any> | undefined> = (\n  ...args: Parameters<Fetcher>\n) => Promise<ExcludeClerkError<Awaited<ReturnType<Fetcher>>>>;\n\n/**\n * @interface\n */\ntype UseReverification = <\n  Fetcher extends (...args: any[]) => Promise<any> | undefined,\n  Options extends UseReverificationOptions = UseReverificationOptions,\n>(\n  fetcher: Fetcher,\n  options?: Options,\n) => UseReverificationResult<Fetcher>;\n\ntype CreateReverificationHandlerParams = UseReverificationOptions & {\n  openUIComponent: Clerk['__internal_openReverification'];\n  telemetry: Clerk['telemetry'];\n};\n\nfunction createReverificationHandler(params: CreateReverificationHandlerParams) {\n  function assertReverification<Fetcher extends (...args: any[]) => Promise<any> | undefined>(\n    fetcher: Fetcher,\n  ): (...args: Parameters<Fetcher>) => Promise<ExcludeClerkError<Awaited<ReturnType<Fetcher>>>> {\n    return (async (...args: Parameters<Fetcher>) => {\n      let result = await resolveResult(fetcher(...args));\n\n      if (isReverificationHint(result)) {\n        /**\n         * Create a promise\n         */\n        const resolvers = createDeferredPromise();\n\n        const isValidMetadata = validateReverificationConfig(result.clerk_error.metadata?.reverification);\n\n        const level = isValidMetadata ? isValidMetadata().level : undefined;\n\n        const cancel = () => {\n          resolvers.reject(\n            new ClerkRuntimeError('User cancelled attempted verification', {\n              code: 'reverification_cancelled',\n            }),\n          );\n        };\n\n        const complete = () => {\n          resolvers.resolve(true);\n        };\n\n        if (params.onNeedsReverification === undefined) {\n          /**\n           * On success resolve the pending promise\n           * On cancel reject the pending promise\n           */\n          params.openUIComponent?.({\n            level: level,\n            afterVerification: complete,\n            afterVerificationCancelled: cancel,\n          });\n        } else {\n          params.onNeedsReverification({\n            cancel,\n            complete,\n            level,\n          });\n        }\n\n        /**\n         * Wait until the promise from above have been resolved or rejected\n         */\n        await resolvers.promise;\n\n        /**\n         * After the promise resolved successfully try the original request one more time\n         */\n        result = await resolveResult(fetcher(...args));\n      }\n\n      return result;\n    }) as ExcludeClerkError<Awaited<ReturnType<Fetcher>>>;\n  }\n\n  return assertReverification;\n}\n\n/**\n * > [!WARNING]\n * >\n * > Depending on the SDK you're using, this feature requires `@clerk/nextjs@6.12.7` or later, `@clerk/clerk-react@5.25.1` or later, and `@clerk/clerk-js@5.57.1` or later.\n *\n * The `useReverification()` hook is used to handle a session's reverification flow. If a request requires reverification, a modal will display, prompting the user to verify their credentials. Upon successful verification, the original request will automatically retry.\n *\n * @function\n *\n * @returns The `useReverification()` hook returns an array with the \"enhanced\" fetcher.\n *\n * @example\n * ### Handle cancellation of the reverification process\n *\n * The following example demonstrates how to handle scenarios where a user cancels the reverification flow, such as closing the modal, which might result in `myData` being `null`.\n *\n * In the following example, `myFetcher` would be a function in your backend that fetches data from the route that requires reverification. See the [guide on how to require reverification](https://clerk.com/docs/guides/reverification) for more information.\n *\n * ```tsx {{ filename: 'src/components/MyButton.tsx' }}\n * import { useReverification } from '@clerk/clerk-react'\n * import { isReverificationCancelledError } from '@clerk/clerk-react/error'\n *\n * type MyData = {\n *   balance: number\n * }\n *\n * export function MyButton() {\n *   const fetchMyData = () => fetch('/api/balance').then(res=> res.json() as Promise<MyData>)\n *   const enhancedFetcher = useReverification(fetchMyData);\n *\n *   const handleClick = async () => {\n *     try {\n *       const myData = await enhancedFetcher()\n *       //     ^ is types as `MyData`\n *     } catch (e) {\n *       // Handle error returned from the fetcher here\n *\n *       // You can also handle cancellation with the following\n *       if (isReverificationCancelledError(err)) {\n *         // Handle the cancellation error here\n *       }\n *     }\n *   }\n *\n *   return <button onClick={handleClick}>Update User</button>\n * }\n * ```\n *\n */\nexport const useReverification: UseReverification = (fetcher, options) => {\n  const { __internal_openReverification, telemetry } = useClerk();\n  const fetcherRef = useRef(fetcher);\n  const optionsRef = useRef(options);\n\n  telemetry?.record(\n    eventMethodCalled('useReverification', {\n      onNeedsReverification: Boolean(options?.onNeedsReverification),\n    }),\n  );\n\n  // Keep fetcher and options ref in sync\n  useSafeLayoutEffect(() => {\n    fetcherRef.current = fetcher;\n    optionsRef.current = options;\n  });\n\n  return useCallback(\n    (...args) => {\n      const handler = createReverificationHandler({\n        openUIComponent: __internal_openReverification,\n        telemetry,\n        ...optionsRef.current,\n      })(fetcherRef.current);\n      return handler(...args);\n    },\n    [__internal_openReverification, telemetry],\n  );\n};\n", "import type { ClerkPaginatedResponse, ClerkResource } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport {\n  useAssertWrappedByClerkProvider,\n  useClerkInstanceContext,\n  useOrganizationContext,\n  useUserContext,\n} from '../contexts';\nimport type { PagesOrInfiniteOptions, PaginatedHookConfig, PaginatedResources } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @internal\n */\ntype CommerceHookConfig<TResource extends ClerkResource, TParams extends PagesOrInfiniteOptions> = {\n  hookName: string;\n  resourceType: string;\n  useFetcher: (\n    param: 'organization' | 'user',\n  ) => ((params: TParams) => Promise<ClerkPaginatedResponse<TResource>>) | undefined;\n};\n\n/**\n * A hook factory that creates paginated data fetching hooks for commerce-related resources.\n * It provides a standardized way to create hooks that can fetch either user or organization resources\n * with built-in pagination support.\n *\n * The generated hooks handle:\n * - Clerk authentication context\n * - Resource-specific data fetching\n * - Pagination (both traditional and infinite scroll)\n * - Telemetry tracking\n * - Type safety for the specific resource.\n *\n * @internal\n */\nexport function createCommerceHook<TResource extends ClerkResource, TParams extends PagesOrInfiniteOptions>({\n  hookName,\n  resourceType,\n  useFetcher,\n}: CommerceHookConfig<TResource, TParams>) {\n  type HookParams = PaginatedHookConfig<PagesOrInfiniteOptions> & {\n    for: 'organization' | 'user';\n  };\n\n  return function useCommerceHook<T extends HookParams>(\n    params: T,\n  ): PaginatedResources<TResource, T extends { infinite: true } ? true : false> {\n    const { for: _for, ...paginationParams } = params;\n\n    useAssertWrappedByClerkProvider(hookName);\n\n    const fetchFn = useFetcher(_for);\n\n    const safeValues = useWithSafeValues(paginationParams, {\n      initialPage: 1,\n      pageSize: 10,\n      keepPreviousData: false,\n      infinite: false,\n      __experimental_mode: undefined,\n    } as unknown as T);\n\n    const clerk = useClerkInstanceContext();\n    const user = useUserContext();\n    const { organization } = useOrganizationContext();\n\n    clerk.telemetry?.record(eventMethodCalled(hookName));\n\n    const hookParams =\n      typeof paginationParams === 'undefined'\n        ? undefined\n        : ({\n            initialPage: safeValues.initialPage,\n            pageSize: safeValues.pageSize,\n            ...(_for === 'organization' ? { orgId: organization?.id } : {}),\n          } as TParams);\n\n    const isClerkLoaded = !!(clerk.loaded && user);\n\n    const isEnabled = !!hookParams && isClerkLoaded;\n\n    const result = usePagesOrInfinite<TParams, ClerkPaginatedResponse<TResource>>(\n      (hookParams || {}) as TParams,\n      fetchFn,\n      {\n        keepPreviousData: safeValues.keepPreviousData,\n        infinite: safeValues.infinite,\n        enabled: isEnabled,\n        __experimental_mode: safeValues.__experimental_mode,\n      },\n      {\n        type: resourceType,\n        userId: user?.id,\n        ...(_for === 'organization' ? { orgId: organization?.id } : {}),\n      },\n    );\n\n    return result;\n  };\n}\n", "import type { CommerceStatementResource, GetStatementsParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommerceHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const useStatements = createCommerceHook<CommerceStatementResource, GetStatementsParams>({\n  hookName: 'useStatements',\n  resourceType: 'commerce-statements',\n  useFetcher: () => {\n    const clerk = useClerkInstanceContext();\n    return clerk.billing.getStatements;\n  },\n});\n", "import type { CommercePaymentResource, GetPaymentAttemptsParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommerceHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const usePaymentAttempts = createCommerceHook<CommercePaymentResource, GetPaymentAttemptsParams>({\n  hookName: 'usePaymentAttempts',\n  resourceType: 'commerce-payment-attempts',\n  useFetcher: () => {\n    const clerk = useClerkInstanceContext();\n    return clerk.billing.getPaymentAttempts;\n  },\n});\n", "import type { CommercePaymentSourceResource, GetPaymentSourcesParams } from '@clerk/types';\n\nimport { useOrganizationContext, useUserContext } from '../contexts';\nimport { createCommerceHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const usePaymentMethods = createCommerceHook<CommercePaymentSourceResource, GetPaymentSourcesParams>({\n  hookName: 'usePaymentMethods',\n  resourceType: 'commerce-payment-methods',\n  useFetcher: resource => {\n    const { organization } = useOrganizationContext();\n    const user = useUserContext();\n\n    if (resource === 'organization') {\n      return organization?.getPaymentSources;\n    }\n    return user?.getPaymentSources;\n  },\n});\n", "import type { CommerceSubscriptionResource, GetSubscriptionsParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommerceHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const useSubscriptionItems = createCommerceHook<CommerceSubscriptionResource, GetSubscriptionsParams>({\n  hookName: 'useSubscriptionItems',\n  resourceType: 'commerce-subscription-items',\n  useFetcher: () => {\n    const clerk = useClerkInstanceContext();\n    return clerk.billing.getSubscriptions;\n  },\n});\n", "import React, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { OBJECT as OBJECT$1, SWRConfig as SWRConfig$1, defaultConfig, with<PERSON>rgs, SWRGlobalState, serialize as serialize$1, createCacheHel<PERSON>, isUndefined as isUndefined$1, UNDEFINED as UNDEFINED$1, isPromiseLike, getTimestamp, isFunction as isFunction$1, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from '../_internal/index.mjs';\nexport { mutate, preload, useSWRConfig } from '../_internal/index.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = React.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize$1(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = isUndefined$1(fallbackData) ? isUndefined$1(config.fallback) ? UNDEFINED$1 : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined$1(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined$1(cachedData) ? fallback && isPromiseLike(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined$1(cachedData) ? isUndefined$1(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined$1(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined$1(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined$1(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined$1(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined$1(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined$1(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined$1(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED$1;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined$1(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction$1(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined$1(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED$1, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if (isUndefined$1(data) || IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                rAF(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction$1(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined$1(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined$1(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined$1(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined$1(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = OBJECT$1.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n", "'use client';\nimport React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement } from 'react';\nimport * as revalidateEvents from './events.mjs';\nimport { dequal } from 'dequal/lite';\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](revalidateEvents.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nexport { noop as A, isPromiseLike as B, IS_REACT_LEGACY as I, OBJECT as O, SWRConfigContext as S, UNDEFINED as U, isFunction as a, SWRGlobalState as b, cache as c, defaultConfig as d, isUndefined as e, mergeConfigs as f, SWRConfig as g, initCache as h, isWindowDefined as i, mutate as j, compare as k, stableHash as l, mergeObjects as m, internalMutate as n, getTimestamp as o, preset as p, defaultConfigOptions as q, IS_SERVER as r, serialize as s, rAF as t, useIsomorphicLayoutEffect as u, slowConnection as v, isDocumentDefined as w, isLegacyDeno as x, hasRequestAnimationFrame as y, createCacheHelper as z };\n", "const FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nexport { ERROR_REVALIDATE_EVENT, FOCUS_EVENT, MUTATE_EVENT, RECONNECT_EVENT };\n", "var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "const INFINITE_PREFIX = '$inf$';\n\nexport { INFINITE_PREFIX };\n", "import { i as isWindowDefined, a as isFunction, m as mergeObjects, S as SWRConfigContext, d as defaultConfig, s as serialize, b as SWRGlobalState, c as cache, e as isUndefined, f as mergeConfigs } from './config-context-client-v7VOFo66.mjs';\nexport { I as IS_REACT_LEGACY, r as IS_SERVER, O as OBJECT, g as SWRConfig, U as UNDEFINED, k as compare, z as createCacheHelper, q as defaultConfigOptions, o as getTimestamp, y as hasRequestAnimationFrame, h as initCache, n as internalMutate, w as isDocumentDefined, x as isLegacyDeno, B as isPromiseLike, j as mutate, A as noop, p as preset, t as rAF, v as slowConnection, l as stableHash, u as useIsomorphicLayoutEffect } from './config-context-client-v7VOFo66.mjs';\nimport * as revalidateEvents from './events.mjs';\nexport { revalidateEvents };\nimport { INFINITE_PREFIX } from './constants.mjs';\nexport { INFINITE_PREFIX } from './constants.mjs';\nimport React, { useContext } from 'react';\nexport * from './types.mjs';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { SWRGlobalState, cache, defaultConfig, isFunction, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, normalize, preload, serialize, subscribeCallback, useSWRConfig, withArgs, withMiddleware };\n", "import { useRef, useCallback } from 'react';\nimport useS<PERSON> from '../index/index.mjs';\nimport { withMiddleware, SWRGlobalState, cache, INFINITE_PREFIX as INFINITE_PREFIX$1, createCacheHelper, isUndefined as isUndefined$1, useIsomorphicLayoutEffect, UNDEFINED as UNDEFINED$1, serialize as serialize$1, isFunction as isFunction$1 } from '../_internal/index.mjs';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { INFINITE_PREFIX } from '../_internal/constants.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\nconst EMPTY_PROMISE = Promise.resolve();\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = useRef(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = SWRGlobalState.get(cache);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = INFINITE_PREFIX$1 + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = createCacheHelper(cache$1, infiniteKey);\n        const getSnapshot = useCallback(()=>{\n            const size = isUndefined$1(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        useSyncExternalStore(useCallback((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = useCallback(()=>{\n            const cachedPageSize = get()._l;\n            return isUndefined$1(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = useRef(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        useIsomorphicLayoutEffect(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: UNDEFINED$1\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = createCacheHelper(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = serialize$1(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = createCacheHelper(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || isUndefined$1(pageData) || revalidateFirstPage && !i && !isUndefined$1(cacheData) || shouldRevalidateOnMount || cacheData && !isUndefined$1(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: UNDEFINED$1\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = useCallback(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!isUndefined$1(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = useCallback((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = createCacheHelper(cache$1, infiniteKey);\n            let size;\n            if (isFunction$1(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = createCacheHelper(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = serialize$1(getKey(i, previousPageData));\n                const [getCache] = createCacheHelper(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : UNDEFINED$1;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if (isUndefined$1(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = withMiddleware(useSWR, infinite);\n\nexport { useSWRInfinite as default, infinite, unstable_serialize };\n", "var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "var __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\nexport {\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod\n};\n//# sourceMappingURL=chunk-OANWQR3B.mjs.map", "/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease\n * 3. Use the prerelease tag of `@clerk/clerk-js` or the packageVersion provided\n * 4. Fallback to the major version of `@clerk/clerk-js` or the packageVersion provided\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @param packageVersion - The version of `@clerk/clerk-js` that will be used if an explicit version is not provided\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined, packageVersion = JS_PACKAGE_VERSION) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(packageVersion);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(packageVersion);\n};\n\nconst getPrereleaseTag = (packageVersion: string) =>\n  packageVersion\n    .trim()\n    .replace(/^v/, '')\n    .match(/-(.+?)(\\.|$)/)?.[1];\n\nexport const getMajorVersion = (packageVersion: string) => packageVersion.trim().replace(/^v/, '').split('.')[0];\n", "export function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n", "import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n", "type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n", "import { retry } from './retry';\n\nconst NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  nonce?: string;\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin, nonce } = opts || {};\n\n  const load = () => {\n    return new Promise<HTMLScriptElement>((resolve, reject) => {\n      if (!src) {\n        reject(new Error(NO_SRC_ERROR));\n      }\n\n      if (!document || !document.body) {\n        reject(NO_DOCUMENT_ERROR);\n      }\n\n      const script = document.createElement('script');\n\n      if (crossOrigin) script.setAttribute('crossorigin', crossOrigin);\n      script.async = async || false;\n      script.defer = defer || false;\n\n      script.addEventListener('load', () => {\n        script.remove();\n        resolve(script);\n      });\n\n      script.addEventListener('error', () => {\n        script.remove();\n        reject();\n      });\n\n      script.src = src;\n      script.nonce = nonce;\n      beforeLoad?.(script);\n      document.body.appendChild(script);\n    });\n  };\n\n  return retry(load, { shouldRetry: (_, iterations) => iterations <= 5 });\n}\n", "import type { ClerkOptions, SDKMetadata, Without } from '@clerk/types';\n\nimport { buildErrorThrower } from './error';\nimport { createDevOrStagingUrlCache, parsePublishableKey } from './keys';\nimport { loadScript } from './loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy';\nimport { addClerkPrefix } from './url';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/shared' });\n\n/**\n * Sets the package name for error messages during ClerkJS script loading.\n *\n * @example\n * setClerkJsLoadingErrorPackageName('@clerk/clerk-react');\n */\nexport function setClerkJsLoadingErrorPackageName(packageName: string) {\n  errorThrower.setPackageName({ packageName });\n}\n\ntype LoadClerkJsScriptOptions = Without<ClerkOptions, 'isSatellite'> & {\n  publishableKey: string;\n  clerkJSUrl?: string;\n  clerkJSVariant?: 'headless' | '';\n  clerkJSVersion?: string;\n  sdkMetadata?: SDKMetadata;\n  proxyUrl?: string;\n  domain?: string;\n  nonce?: string;\n};\n\n/**\n * Hotloads the Clerk JS script.\n *\n * Checks for an existing Clerk JS script. If found, it returns a promise\n * that resolves when the script loads. If not found, it uses the provided options to\n * build the Clerk JS script URL and load the script.\n *\n * @param opts - The options used to build the Clerk JS script URL and load the script.\n *               Must include a `publishableKey` if no existing script is found.\n *\n * @example\n * loadClerkJsScript({ publishableKey: 'pk_' });\n */\nconst loadClerkJsScript = async (opts?: LoadClerkJsScriptOptions) => {\n  const existingScript = document.querySelector<HTMLScriptElement>('script[data-clerk-js-script]');\n\n  if (existingScript) {\n    return new Promise((resolve, reject) => {\n      existingScript.addEventListener('load', () => {\n        resolve(existingScript);\n      });\n\n      existingScript.addEventListener('error', () => {\n        reject(FAILED_TO_LOAD_ERROR);\n      });\n    });\n  }\n\n  if (!opts?.publishableKey) {\n    errorThrower.throwMissingPublishableKeyError();\n    return;\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    nonce: opts.nonce,\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\n/**\n * Generates a Clerk JS script URL.\n *\n * @param opts - The options to use when building the Clerk JS script URL.\n *\n * @example\n * clerkJsScriptUrl({ publishableKey: 'pk_' });\n */\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\n/**\n * Builds an object of Clerk JS script attributes.\n */\nconst buildClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => {\n  const obj: Record<string, string> = {};\n\n  if (options.publishableKey) {\n    obj['data-clerk-publishable-key'] = options.publishableKey;\n  }\n\n  if (options.proxyUrl) {\n    obj['data-clerk-proxy-url'] = options.proxyUrl;\n  }\n\n  if (options.domain) {\n    obj['data-clerk-domain'] = options.domain;\n  }\n\n  if (options.nonce) {\n    obj.nonce = options.nonce;\n  }\n\n  return obj;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const attributes = buildClerkJsScriptAttributes(options);\n  for (const attribute in attributes) {\n    script.setAttribute(attribute, attributes[attribute]);\n  }\n};\n\nexport { loadClerkJsScript, buildClerkJsScriptAttributes, clerkJsScriptUrl };\nexport type { LoadClerkJsScriptOptions };\n", "/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n", "/**\n * Vite does not define `global` by default\n * One workaround is to use the `define` config prop\n * https://vitejs.dev/config/#define\n * We are solving this in the SDK level to reduce setup steps.\n */\nif (typeof window !== 'undefined' && !window.global) {\n  window.global = typeof global === 'undefined' ? window : global;\n}\n\nexport {};\n", "import './polyfills';\n\nimport { setClerkJsLoadingErrorPackageName } from '@clerk/shared/loadClerkJsScript';\n\nimport { setErrorThrowerOptions } from './errors/errorThrower';\n\nexport * from './components';\nexport * from './contexts';\n\nexport * from './hooks';\nexport type { BrowserClerk, ClerkProp, HeadlessBrowserClerk, ClerkProviderProps } from './types';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type {\n  APIKeysProps,\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  Without,\n} from '@clerk/types';\nimport type { PropsWithChildren, ReactNode } from 'react';\nimport React, { createContext, createElement, useContext } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userButtonMenuActionRenderedError,\n  userButtonMenuItemsRenderedError,\n  userButtonMenuLinkRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors/messages';\nimport type {\n  CustomPortalsRendererProps,\n  MountProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserButtonActionProps,\n  UserButtonLinkProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport {\n  useOrganizationProfileCustomPages,\n  useSanitizedChildren,\n  useUserButtonCustomMenuItems,\n  useUserProfileCustomPages,\n} from '../utils';\nimport { useWaitForComponentMount } from '../utils/useWaitForComponentMount';\nimport { ClerkHostRenderer } from './ClerkHostRenderer';\nimport { withClerk } from './withClerk';\n\ntype FallbackProp = {\n  /**\n   * An optional element to render while the component is mounting.\n   */\n  fallback?: ReactNode;\n};\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n  MenuItems: typeof MenuItems;\n  Action: typeof MenuAction;\n  Link: typeof MenuLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<UserButton />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof UserButtonOutlet;\n};\n\ntype UserButtonPropsWithoutCustomPages = Without<\n  UserButtonProps,\n  'userProfileProps' | '__experimental_asStandalone'\n> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<OrganizationSwitcher />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof OrganizationSwitcherOutlet;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Without<\n  OrganizationSwitcherProps,\n  'organizationProfileProps' | '__experimental_asStandalone'\n> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\nconst CustomPortalsRenderer = (props: CustomPortalsRendererProps) => {\n  return (\n    <>\n      {props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      {props?.customMenuItemsPortals?.map((portal, index) => createElement(portal, { key: index }))}\n    </>\n  );\n};\n\nexport const SignIn = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignInProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignIn}\n            unmount={clerk.unmountSignIn}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignIn', renderWhileLoading: true },\n);\n\nexport const SignUp = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignUpProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignUp}\n            unmount={clerk.unmountSignUp}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignUp', renderWhileLoading: true },\n);\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<UserProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        <ClerkHostRenderer\n          component={component}\n          mount={clerk.mountUserProfile}\n          unmount={clerk.unmountUserProfile}\n          updateProps={(clerk as any).__unstable__updateProps}\n          props={{ ...props, customPages }}\n          rootProps={rendererRootProps}\n        >\n          <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n        </ClerkHostRenderer>\n      </>\n    );\n  },\n  { component: 'UserProfile', renderWhileLoading: true },\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst UserButtonContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _UserButton = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    const { customMenuItems, customMenuItemsPortals } = useUserButtonCustomMenuItems(props.children);\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountUserButton,\n      unmount: clerk.unmountUserButton,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, userProfileProps, customMenuItems },\n    };\n    const portalProps = {\n      customPagesPortals: customPagesPortals,\n      customMenuItemsPortals: customMenuItemsPortals,\n    };\n\n    return (\n      <UserButtonContext.Provider value={passableProps}>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            {...passableProps}\n            hideRootHtmlElement={!!props.__experimental_asProvider}\n            rootProps={rendererRootProps}\n          >\n            {/*This mimics the previous behaviour before asProvider existed*/}\n            {props.__experimental_asProvider ? sanitizedChildren : null}\n            <CustomPortalsRenderer {...portalProps} />\n          </ClerkHostRenderer>\n        )}\n      </UserButtonContext.Provider>\n    );\n  },\n  { component: 'UserButton', renderWhileLoading: true },\n);\n\nexport function MenuItems({ children }: PropsWithChildren) {\n  logErrorInDevMode(userButtonMenuItemsRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuAction({ children }: PropsWithChildren<UserButtonActionProps>) {\n  logErrorInDevMode(userButtonMenuActionRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuLink({ children }: PropsWithChildren<UserButtonLinkProps>) {\n  logErrorInDevMode(userButtonMenuLinkRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserButtonOutlet(outletProps: Without<UserButtonProps, 'userProfileProps'>) {\n  const providerProps = useContext(UserButtonContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n  MenuItems,\n  Action: MenuAction,\n  Link: MenuLink,\n  __experimental_Outlet: UserButtonOutlet,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<OrganizationProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationProfile}\n            unmount={clerk.unmountOrganizationProfile}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={{ ...props, customPages }}\n            rootProps={rendererRootProps}\n          >\n            <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n          </ClerkHostRenderer>\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationProfile', renderWhileLoading: true },\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<CreateOrganizationProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountCreateOrganization}\n            unmount={clerk.unmountCreateOrganization}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'CreateOrganization', renderWhileLoading: true },\n);\n\nconst OrganizationSwitcherContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _OrganizationSwitcher = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountOrganizationSwitcher,\n      unmount: clerk.unmountOrganizationSwitcher,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, organizationProfileProps },\n      rootProps: rendererRootProps,\n      component,\n    };\n\n    /**\n     * Prefetch organization list\n     */\n    clerk.__experimental_prefetchOrganizationSwitcher();\n\n    return (\n      <OrganizationSwitcherContext.Provider value={passableProps}>\n        <>\n          {shouldShowFallback && fallback}\n          {clerk.loaded && (\n            <ClerkHostRenderer\n              {...passableProps}\n              hideRootHtmlElement={!!props.__experimental_asProvider}\n            >\n              {/*This mimics the previous behaviour before asProvider existed*/}\n              {props.__experimental_asProvider ? sanitizedChildren : null}\n              <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n            </ClerkHostRenderer>\n          )}\n        </>\n      </OrganizationSwitcherContext.Provider>\n    );\n  },\n  { component: 'OrganizationSwitcher', renderWhileLoading: true },\n);\n\nexport function OrganizationSwitcherOutlet(\n  outletProps: Without<OrganizationSwitcherProps, 'organizationProfileProps'>,\n) {\n  const providerProps = useContext(OrganizationSwitcherContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n  __experimental_Outlet: OrganizationSwitcherOutlet,\n});\n\nexport const OrganizationList = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<OrganizationListProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationList}\n            unmount={clerk.unmountOrganizationList}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationList', renderWhileLoading: true },\n);\n\nexport const GoogleOneTap = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<GoogleOneTapProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            open={clerk.openGoogleOneTap}\n            close={clerk.closeGoogleOneTap}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'GoogleOneTap', renderWhileLoading: true },\n);\n\nexport const Waitlist = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<WaitlistProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountWaitlist}\n            unmount={clerk.unmountWaitlist}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'Waitlist', renderWhileLoading: true },\n);\n\nexport const PricingTable = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<PricingTableProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountPricingTable}\n            unmount={clerk.unmountPricingTable}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'PricingTable', renderWhileLoading: true },\n);\n\n/**\n * @experimental\n * This component is in early access and may change in future releases.\n */\nexport const APIKeys = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<APIKeysProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountApiKeys}\n            unmount={clerk.unmountApiKeys}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'ApiKeys', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleChildrenInButtonComponent } from '../errors/messages';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch {\n      return errorThrower.throw(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n", "export function isConstructor<T>(f: any): f is T {\n  return typeof f === 'function';\n}\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      return errorThrower.throw(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n", "import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => React.JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index]) : null}</>,\n  }));\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  MenuItems,\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors/messages';\nimport type { UserProfilePageProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: UserProfileLink,\n      PageComponent: UserProfilePage,\n      MenuItemsComponent: MenuItems,\n      componentName: 'UserProfile',\n    },\n    options,\n  );\n};\n\nexport const useOrganizationProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['general', 'members'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: OrganizationProfileLink,\n      PageComponent: OrganizationProfilePage,\n      componentName: 'OrganizationProfile',\n    },\n    options,\n  );\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  MenuItemsComponent?: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype UseCustomPagesOptions = {\n  allowForAnyChildren: boolean;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\n/**\n * Exclude any children that is used for identifying Custom Pages or Custom Items.\n * Passing:\n * ```tsx\n *  <UserProfile.Page/>\n *  <OrganizationProfile.Link/>\n *  <MyComponent>\n *  <UserButton.MenuItems/>\n * ```\n * Gives back\n * ```tsx\n * <MyComponent>\n * ````\n */\nexport const useSanitizedChildren = (children: React.ReactNode) => {\n  const sanitizedChildren: React.ReactNode[] = [];\n\n  const excludedComponents: any[] = [\n    OrganizationProfileLink,\n    OrganizationProfilePage,\n    MenuItems,\n    UserProfilePage,\n    UserProfileLink,\n  ];\n\n  React.Children.forEach(children, child => {\n    if (!excludedComponents.some(component => isThatComponent(child, component))) {\n      sanitizedChildren.push(child);\n    }\n  });\n\n  return sanitizedChildren;\n};\n\nconst useCustomPages = (params: UseCustomPagesParams, options?: UseCustomPagesOptions) => {\n  const { children, LinkComponent, PageComponent, MenuItemsComponent, reorderItemsLabels, componentName } = params;\n  const { allowForAnyChildren = false } = options || {};\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, PageComponent) &&\n      !isThatComponent(child, LinkComponent) &&\n      !isThatComponent(child, MenuItemsComponent)\n    ) {\n      if (child && !allowForAnyChildren) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n", "import React from 'react';\n\nexport const isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomMenuItem } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport { MenuAction, MenuItems, MenuLink, UserProfileLink, UserProfilePage } from '../components/uiComponents';\nimport {\n  customMenuItemsIgnoredComponent,\n  userButtonIgnoredComponent,\n  userButtonMenuItemLinkWrongProps,\n  userButtonMenuItemsActionWrongsProps,\n} from '../errors/messages';\nimport type { UserButtonActionProps, UserButtonLinkProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserButtonCustomMenuItems = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['manageAccount', 'signOut'];\n  return useCustomMenuItems({\n    children,\n    reorderItemsLabels,\n    MenuItemsComponent: MenuItems,\n    MenuActionComponent: MenuAction,\n    MenuLinkComponent: MenuLink,\n    UserProfileLinkComponent: UserProfileLink,\n    UserProfilePageComponent: UserProfilePage,\n  });\n};\n\ntype UseCustomMenuItemsParams = {\n  children: React.ReactNode | React.ReactNode[];\n  MenuItemsComponent?: any;\n  MenuActionComponent?: any;\n  MenuLinkComponent?: any;\n  UserProfileLinkComponent?: any;\n  UserProfilePageComponent?: any;\n  reorderItemsLabels: string[];\n};\n\ntype CustomMenuItemType = UserButtonActionProps | UserButtonLinkProps;\n\nconst useCustomMenuItems = ({\n  children,\n  MenuItemsComponent,\n  MenuActionComponent,\n  MenuLinkComponent,\n  UserProfileLinkComponent,\n  UserProfilePageComponent,\n  reorderItemsLabels,\n}: UseCustomMenuItemsParams) => {\n  const validChildren: CustomMenuItemType[] = [];\n  const customMenuItems: CustomMenuItem[] = [];\n  const customMenuItemsPortals: React.ComponentType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, MenuItemsComponent) &&\n      !isThatComponent(child, UserProfileLinkComponent) &&\n      !isThatComponent(child, UserProfilePageComponent)\n    ) {\n      if (child) {\n        logErrorInDevMode(userButtonIgnoredComponent);\n      }\n      return;\n    }\n\n    // Ignore UserProfileLinkComponent and UserProfilePageComponent\n    if (isThatComponent(child, UserProfileLinkComponent) || isThatComponent(child, UserProfilePageComponent)) {\n      return;\n    }\n\n    // Menu items children\n    const { props } = child as ReactElement;\n\n    React.Children.forEach(props.children, child => {\n      if (!isThatComponent(child, MenuActionComponent) && !isThatComponent(child, MenuLinkComponent)) {\n        if (child) {\n          logErrorInDevMode(customMenuItemsIgnoredComponent);\n        }\n\n        return;\n      }\n\n      const { props } = child as ReactElement;\n\n      const { label, labelIcon, href, onClick, open } = props;\n\n      if (isThatComponent(child, MenuActionComponent)) {\n        if (isReorderItem(props, reorderItemsLabels)) {\n          // This is a reordering item\n          validChildren.push({ label });\n        } else if (isCustomMenuItem(props)) {\n          const baseItem = {\n            label,\n            labelIcon,\n          };\n\n          if (onClick !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              onClick,\n            });\n          } else if (open !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              open: open.startsWith('/') ? open : `/${open}`,\n            });\n          } else {\n            // Handle the case where neither onClick nor open is defined\n            logErrorInDevMode('Custom menu item must have either onClick or open property');\n            return;\n          }\n        } else {\n          logErrorInDevMode(userButtonMenuItemsActionWrongsProps);\n          return;\n        }\n      }\n\n      if (isThatComponent(child, MenuLinkComponent)) {\n        if (isExternalLink(props)) {\n          validChildren.push({ label, labelIcon, href });\n        } else {\n          logErrorInDevMode(userButtonMenuItemLinkWrongProps);\n          return;\n        }\n      }\n    });\n  });\n\n  const customMenuItemLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n  validChildren.forEach((mi, index) => {\n    if (isCustomMenuItem(mi)) {\n      customMenuItemLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n    if (isExternalLink(mi)) {\n      customLinkLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n  });\n\n  const customMenuItemLabelIconsPortals = useCustomElementPortal(customMenuItemLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  validChildren.forEach((mi, index) => {\n    if (isReorderItem(mi, reorderItemsLabels)) {\n      customMenuItems.push({\n        label: mi.label,\n      });\n    }\n    if (isCustomMenuItem(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customMenuItemLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const menuItem: CustomMenuItem = {\n        label: mi.label,\n        mountIcon,\n        unmountIcon,\n      };\n\n      if ('onClick' in mi) {\n        menuItem.onClick = mi.onClick;\n      } else if ('open' in mi) {\n        menuItem.open = mi.open;\n      }\n      customMenuItems.push(menuItem);\n      customMenuItemsPortals.push(iconPortal);\n    }\n    if (isExternalLink(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customMenuItems.push({\n        label: mi.label,\n        href: mi.href,\n        mountIcon,\n        unmountIcon,\n      });\n      customMenuItemsPortals.push(iconPortal);\n    }\n  });\n\n  return { customMenuItems, customMenuItemsPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, onClick, labelIcon } = childProps;\n  return !children && !onClick && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomMenuItem = (childProps: any): childProps is UserButtonActionProps => {\n  const { label, labelIcon, onClick, open } = childProps;\n  return !!labelIcon && !!label && (typeof onClick === 'function' || typeof open === 'string');\n};\n\nconst isExternalLink = (childProps: any): childProps is UserButtonLinkProps => {\n  const { label, href, labelIcon } = childProps;\n  return !!href && !!labelIcon && !!label;\n};\n", "import { useEffect, useRef, useState } from 'react';\n\n/**\n * Used to detect when a Clerk component has been added to the DOM.\n */\nfunction waitForElementChildren(options: { selector?: string; root?: HTMLElement | null; timeout?: number }) {\n  const { root = document?.body, selector, timeout = 0 } = options;\n\n  return new Promise<void>((resolve, reject) => {\n    if (!root) {\n      reject(new Error('No root element provided'));\n      return;\n    }\n\n    let elementToWatch: HTMLElement | null = root;\n    if (selector) {\n      elementToWatch = root?.querySelector(selector);\n    }\n\n    // Check if the element already has child nodes\n    const isElementAlreadyPresent = elementToWatch?.childElementCount && elementToWatch.childElementCount > 0;\n    if (isElementAlreadyPresent) {\n      resolve();\n      return;\n    }\n\n    // Set up a MutationObserver to detect when the element has children\n    const observer = new MutationObserver(mutationsList => {\n      for (const mutation of mutationsList) {\n        if (mutation.type === 'childList') {\n          if (!elementToWatch && selector) {\n            elementToWatch = root?.querySelector(selector);\n          }\n\n          if (elementToWatch?.childElementCount && elementToWatch.childElementCount > 0) {\n            observer.disconnect();\n            resolve();\n            return;\n          }\n        }\n      }\n    });\n\n    observer.observe(root, { childList: true, subtree: true });\n\n    // Set up an optional timeout to reject the promise if the element never gets child nodes\n    if (timeout > 0) {\n      setTimeout(() => {\n        observer.disconnect();\n        reject(new Error(`Timeout waiting for element children`));\n      }, timeout);\n    }\n  });\n}\n\n/**\n * Detect when a Clerk component has mounted by watching DOM updates to an element with a `data-clerk-component=\"${component}\"` property.\n */\nexport function useWaitForComponentMount(component?: string) {\n  const watcherRef = useRef<Promise<void>>();\n  const [status, setStatus] = useState<'rendering' | 'rendered' | 'error'>('rendering');\n\n  useEffect(() => {\n    if (!component) {\n      throw new Error('Clerk: no component name provided, unable to detect mount.');\n    }\n\n    if (typeof window !== 'undefined' && !watcherRef.current) {\n      watcherRef.current = waitForElementChildren({ selector: `[data-clerk-component=\"${component}\"]` })\n        .then(() => {\n          setStatus('rendered');\n        })\n        .catch(() => {\n          setStatus('error');\n        });\n    }\n  }, [component]);\n\n  return status;\n}\n", "import { without } from '@clerk/shared/object';\nimport { isDeeplyEqual } from '@clerk/shared/react';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport type { MountProps, OpenProps } from '../types';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nconst stripMenuItemIconHandlers = (\n  menuItems?: Array<{\n    mountIcon?: (el: HTMLDivElement) => void;\n    unmountIcon?: (el: HTMLDivElement) => void;\n    [key: string]: any;\n  }>,\n) => {\n  return menuItems?.map(({ mountIcon, unmountIcon, ...rest }) => rest);\n};\n\n// README: <ClerkHostRenderer/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerk.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\n/**\n * Used to orchestrate mounting of Clerk components in a host React application.\n * Components are rendered into a specific DOM node using mount/unmount methods provided by the Clerk class.\n */\nexport class ClerkHostRenderer extends React.PureComponent<\n  PropsWithChildren<\n    (MountProps | OpenProps) & {\n      component?: string;\n      hideRootHtmlElement?: boolean;\n      rootProps?: JSX.IntrinsicElements['div'];\n    }\n  >\n> {\n  private rootRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(_prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(_prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    // Remove children and customPages from props before comparing\n    // children might hold circular references which deepEqual can't handle\n    // and the implementation of customPages relies on props getting new references\n    const prevProps = without(_prevProps.props, 'customPages', 'customMenuItems', 'children');\n    const newProps = without(this.props.props, 'customPages', 'customMenuItems', 'children');\n\n    // instead, we simply use the length of customPages to determine if it changed or not\n    const customPagesChanged = prevProps.customPages?.length !== newProps.customPages?.length;\n    const customMenuItemsChanged = prevProps.customMenuItems?.length !== newProps.customMenuItems?.length;\n\n    // Strip out mountIcon and unmountIcon handlers since they're always generated as new function references,\n    // which would cause unnecessary re-renders in deep equality checks\n    const prevMenuItemsWithoutHandlers = stripMenuItemIconHandlers(_prevProps.props.customMenuItems);\n    const newMenuItemsWithoutHandlers = stripMenuItemIconHandlers(this.props.props.customMenuItems);\n\n    if (\n      !isDeeplyEqual(prevProps, newProps) ||\n      !isDeeplyEqual(prevMenuItemsWithoutHandlers, newMenuItemsWithoutHandlers) ||\n      customPagesChanged ||\n      customMenuItemsChanged\n    ) {\n      if (this.rootRef.current) {\n        this.props.updateProps({ node: this.rootRef.current, props: this.props.props });\n      }\n    }\n  }\n\n  componentDidMount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.rootRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.rootRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    const { hideRootHtmlElement = false } = this.props;\n    const rootAttributes = {\n      ref: this.rootRef,\n      ...this.props.rootProps,\n      ...(this.props.component && { 'data-clerk-component': this.props.component }),\n    };\n\n    return (\n      <>\n        {!hideRootHtmlElement && <div {...rootAttributes} />}\n        {this.props.children}\n      </>\n    );\n  }\n}\n", "import type { SignInButtonProps, SignInProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignInButtonProps>>) => {\n    const {\n      signUpFallbackRedirectUrl,\n      forceRedirectUrl,\n      fallbackRedirectUrl,\n      signUpForceRedirectUrl,\n      mode,\n      initialValues,\n      withSignUp,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = normalizeWithDefaultValue(children, 'Sign in');\n    const child = assertSingleChild(children)('SignInButton');\n\n    const clickHandler = () => {\n      const opts: SignInProps = {\n        forceRedirectUrl,\n        fallbackRedirectUrl,\n        signUpFallbackRedirectUrl,\n        signUpForceRedirectUrl,\n        initialValues,\n        withSignUp,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignIn({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignIn({\n        ...opts,\n        signInFallbackRedirectUrl: fallbackRedirectUrl,\n        signInForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInButton', renderWhileLoading: true },\n);\n", "import type { SignUpButtonProps, SignUpProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignUpButtonProps>>) => {\n    const {\n      fallbackRedirectUrl,\n      forceRedirectUrl,\n      signInFallbackRedirectUrl,\n      signInForceRedirectUrl,\n      mode,\n      unsafeMetadata,\n      initialValues,\n      oauthFlow,\n      ...rest\n    } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign up');\n    const child = assertSingleChild(children)('SignUpButton');\n\n    const clickHandler = () => {\n      const opts: SignUpProps = {\n        fallbackRedirectUrl,\n        forceRedirectUrl,\n        signInFallbackRedirectUrl,\n        signInForceRedirectUrl,\n        unsafeMetadata,\n        initialValues,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignUp({ ...opts, appearance: props.appearance });\n      }\n\n      return clerk.redirectToSignUp({\n        ...opts,\n        signUpFallbackRedirectUrl: fallbackRedirectUrl,\n        signUpForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignUpButton', renderWhileLoading: true },\n);\n", "import type { SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  redirectUrl?: string;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { redirectUrl = '/', signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => clerk.signOut({ redirectUrl, ...signOutOptions });\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignOutButton', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport type { SignInWithMetamaskButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInWithMetamaskButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<SignInWithMetamaskButtonProps>) => {\n    const { redirectUrl, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign in with Metamask');\n    const child = assertSingleChild(children)('SignInWithMetamaskButton');\n\n    // TODO: Properly fix this code\n    // eslint-disable-next-line @typescript-eslint/require-await\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl: redirectUrl || undefined });\n      }\n      void authenticate();\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInWithMetamask', renderWhileLoading: true },\n);\n", "import { isPublishable<PERSON><PERSON> } from '@clerk/shared/keys';\nimport React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleClerkProvidersError } from '../errors/messages';\nimport type { ClerkProviderProps } from '../types';\nimport { withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\n\nfunction ClerkProviderBase(props: ClerkProviderProps) {\n  const { initialState, children, __internal_bypassMissingPublishableKey, ...restIsomorphicClerkOptions } = props;\n  const { publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk && !__internal_bypassMissingPublishableKey) {\n    if (!publishableKey) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    }\n  }\n\n  return (\n    <ClerkContextProvider\n      initialState={initialState}\n      isomorphicClerkOptions={restIsomorphicClerkOptions}\n    >\n      {children}\n    </ClerkContextProvider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider };\n", "import { deriveState } from '@clerk/shared/deriveState';\nimport { ClientContext, OrganizationProvider, SessionContext, UserContext } from '@clerk/shared/react';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport { IsomorphicClerk } from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { AuthContext } from './AuthContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider) {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, clerkStatus } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerk.loaded, state, initialState);\n  const clerkCtx = React.useMemo(\n    () => ({ value: clerk }),\n    [\n      // Only update the clerk reference on status change\n      clerkStatus,\n    ],\n  );\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    sessionStatus,\n    sessionClaims,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = {\n      sessionId,\n      sessionStatus,\n      sessionClaims,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions,\n      factorVerificationAge,\n    };\n    return { value };\n  }, [sessionId, sessionStatus, userId, actor, orgId, orgRole, orgSlug, factorVerificationAge, sessionClaims?.__raw]);\n\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n    };\n    return { value };\n  }, [orgId, organization]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const isomorphicClerkRef = React.useRef(IsomorphicClerk.getOrCreateInstance(options));\n  const [clerkStatus, setClerkStatus] = React.useState(isomorphicClerkRef.current.status);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerkRef.current.on('status', setClerkStatus);\n    return () => {\n      if (isomorphicClerkRef.current) {\n        isomorphicClerkRef.current.off('status', setClerkStatus);\n      }\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk: isomorphicClerkRef.current, clerkStatus };\n};\n", "import { inBrowser } from '@clerk/shared/browser';\nimport { clerkEvents, createClerkEventBus } from '@clerk/shared/clerkEventBus';\nimport { loadClerkJsScript } from '@clerk/shared/loadClerkJsScript';\nimport { handleValueOrFn } from '@clerk/shared/utils';\nimport type {\n  __internal_CheckoutProps,\n  __internal_OAuthConsentProps,\n  __internal_PlanDetailsProps,\n  __internal_UserVerificationModalProps,\n  __internal_UserVerificationProps,\n  APIKeysNamespace,\n  APIKeysProps,\n  AuthenticateWithCoinbaseWalletParams,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  AuthenticateWithOKXWalletParams,\n  Clerk,\n  ClerkAuthenticateWithWeb3Params,\n  ClerkOptions,\n  ClerkStatus,\n  ClientResource,\n  CommerceBillingNamespace,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  JoinWaitlistParams,\n  ListenerCallback,\n  LoadedClerk,\n  NextTaskParams,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  RedirectOptions,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  WaitlistResource,\n  Without,\n} from '@clerk/types';\n\nimport { errorThrower } from './errors/errorThrower';\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors/messages';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstructor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor } from './utils';\n\nif (typeof globalThis.__BUILD_DISABLE_RHC__ === 'undefined') {\n  globalThis.__BUILD_DISABLE_RHC__ = false;\n}\n\nconst SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype WithVoidReturn<F extends (...args: any) => any> = (\n  ...args: Parameters<F>\n) => ReturnType<F> extends Promise<infer T> ? Promise<T | void> : ReturnType<F> | void;\ntype WithVoidReturnFunctions<T> = {\n  [K in keyof T]: T[K] extends (...args: any) => any ? WithVoidReturn<T[K]> : T[K];\n};\n\ntype IsomorphicLoadedClerk = Without<\n  WithVoidReturnFunctions<LoadedClerk>,\n  | 'client'\n  | '__internal_addNavigationListener'\n  | '__internal_getCachedResources'\n  | '__internal_reloadInitialResources'\n  | 'billing'\n  | 'apiKeys'\n  | '__internal_setComponentNavigationContext'\n  | '__internal_setActiveInProgress'\n> & {\n  client: ClientResource | undefined;\n  billing: CommerceBillingNamespace | undefined;\n  apiKeys: APIKeysNamespace | undefined;\n};\n\nexport class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenUserVerification?: null | __internal_UserVerificationProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenCheckout?: null | __internal_CheckoutProps = null;\n  private preopenPlanDetails?: null | __internal_PlanDetailsProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private preOpenWaitlist?: null | WaitlistProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps | undefined>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps | undefined>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps | undefined>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps | undefined>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps | undefined>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps | undefined>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps | undefined>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps | undefined>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private premountWaitlistNodes = new Map<HTMLDivElement, WaitlistProps | undefined>();\n  private premountPricingTableNodes = new Map<HTMLDivElement, PricingTableProps | undefined>();\n  private premountApiKeysNodes = new Map<HTMLDivElement, APIKeysProps | undefined>();\n  private premountOAuthConsentNodes = new Map<HTMLDivElement, __internal_OAuthConsentProps | undefined>();\n  // A separate Map of `addListener` method calls to handle multiple listeners.\n  private premountAddListenerCalls = new Map<\n    ListenerCallback,\n    {\n      unsubscribe: UnsubscribeCallback;\n      nativeUnsubscribe?: UnsubscribeCallback;\n    }\n  >();\n  private loadedListeners: Array<() => void> = [];\n\n  #status: ClerkStatus = 'loading';\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #publishableKey: string;\n  #eventBus = createClerkEventBus();\n\n  get publishableKey(): string {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.clerkjs?.loaded || false;\n  }\n\n  get status(): ClerkStatus {\n    /**\n     * If clerk-js is not available the returned value can either be \"loading\" or \"error\".\n     */\n    if (!this.clerkjs) {\n      return this.#status;\n    }\n    return (\n      this.clerkjs?.status ||\n      /**\n       * Support older clerk-js versions.\n       * If clerk-js is available but `.status` is missing it we need to fallback to `.loaded`.\n       * Since \"degraded\" an \"error\" did not exist before,\n       * map \"loaded\" to \"ready\" and \"not loaded\" to \"loading\".\n       */\n      (this.clerkjs.loaded ? 'ready' : 'loading')\n    );\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (\n      !inBrowser() ||\n      !this.#instance ||\n      (options.Clerk && this.#instance.Clerk !== options.Clerk) ||\n      // Allow hot swapping PKs on the client\n      this.#instance.publishableKey !== options.publishableKey\n    ) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  /**\n   * Accesses private options from the `Clerk` instance and defaults to\n   * `IsomorphicClerk` options when in SSR context.\n   *  @internal\n   */\n  public __internal_getOption<K extends keyof ClerkOptions>(key: K): ClerkOptions[K] | undefined {\n    return this.clerkjs?.__internal_getOption ? this.clerkjs?.__internal_getOption(key) : this.options[key];\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, publishableKey } = options || {};\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n\n    if (!this.options.sdkMetadata) {\n      this.options.sdkMetadata = SDK_METADATA;\n    }\n    this.#eventBus.emit(clerkEvents.Status, 'loading');\n    this.#eventBus.prioritizedOn(clerkEvents.Status, status => (this.#status = status));\n\n    if (this.#publishableKey) {\n      void this.loadClerkJS();\n    }\n  }\n\n  get sdkMetadata() {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType() {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi() {\n    return this.clerkjs?.frontendApi || '';\n  }\n\n  get isStandardBrowser() {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite() {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignInUrl = (...args: Parameters<Clerk['buildAfterSignInUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignInUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignInUrl', callback);\n    }\n  };\n\n  buildAfterSignUpUrl = (...args: Parameters<Clerk['buildAfterSignUpUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignUpUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignOutUrl', callback);\n    }\n  };\n\n  buildNewSubscriptionRedirectUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildNewSubscriptionRedirectUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildNewSubscriptionRedirectUrl', callback);\n    }\n  };\n\n  buildAfterMultiSessionSingleSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterMultiSessionSingleSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterMultiSessionSingleSignOutUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildWaitlistUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildWaitlistUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildWaitlistUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = async () => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_publishable_key = this.#publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstructor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.#publishableKey, {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n\n          this.beforeLoad(c);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n          if (!c.loaded) {\n            this.beforeLoad(c);\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else if (!__BUILD_DISABLE_RHC__) {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            publishableKey: this.#publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n            nonce: this.options.nonce,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        this.beforeLoad(global.Clerk);\n        await global.Clerk.load(this.options);\n      }\n\n      if (global.Clerk?.loaded) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      this.#eventBus.emit(clerkEvents.Status, 'error');\n      console.error(error.stack || error.message || error);\n      return;\n    }\n  }\n\n  public on: Clerk['on'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.on) {\n      return this.clerkjs.on(...args);\n    } else {\n      this.#eventBus.on(...args);\n    }\n  };\n\n  public off: Clerk['off'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.off) {\n      return this.clerkjs.off(...args);\n    } else {\n      this.#eventBus.off(...args);\n    }\n  };\n\n  /**\n   * @deprecated Please use `addStatusListener`. This api will be removed in the next major.\n   */\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  /**\n   * @deprecated Please use `__internal_setStatus`. This api will be removed in the next major.\n   */\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private beforeLoad = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n    this.premountAddListenerCalls.forEach((listenerHandlers, listener) => {\n      listenerHandlers.nativeUnsubscribe = clerkjs.addListener(listener);\n    });\n\n    this.#eventBus.internal.retrieveListeners('status')?.forEach(listener => {\n      // Since clerkjs exists it will call `this.clerkjs.on('status', listener)`\n      this.on('status', listener, { notify: true });\n    });\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenCheckout !== null) {\n      clerkjs.__internal_openCheckout(this.preopenCheckout);\n    }\n\n    if (this.preopenPlanDetails !== null) {\n      clerkjs.__internal_openPlanDetails(this.preopenPlanDetails);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenUserVerification !== null) {\n      clerkjs.__internal_openReverification(this.preopenUserVerification);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    if (this.preOpenWaitlist !== null) {\n      clerkjs.openWaitlist(this.preOpenWaitlist);\n    }\n\n    this.premountSignInNodes.forEach((props, node) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props, node) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props, node) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props, node) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props, node) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.premountWaitlistNodes.forEach((props, node) => {\n      clerkjs.mountWaitlist(node, props);\n    });\n\n    this.premountPricingTableNodes.forEach((props, node) => {\n      clerkjs.mountPricingTable(node, props);\n    });\n\n    this.premountApiKeysNodes.forEach((props, node) => {\n      clerkjs.mountApiKeys(node, props);\n    });\n\n    this.premountOAuthConsentNodes.forEach((props, node) => {\n      clerkjs.__internal_mountOAuthConsent(node, props);\n    });\n\n    /**\n     * Only update status in case `clerk.status` is missing. In any other case, `clerk-js` should be the orchestrator.\n     */\n    if (typeof this.clerkjs.status === 'undefined') {\n      this.#eventBus.emit(clerkEvents.Status, 'ready');\n    }\n\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version() {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get telemetry() {\n    if (this.clerkjs) {\n      return this.clerkjs.telemetry;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get isSignedIn(): boolean {\n    if (this.clerkjs) {\n      return this.clerkjs.isSignedIn;\n    } else {\n      return false;\n    }\n  }\n\n  get billing(): CommerceBillingNamespace | undefined {\n    return this.clerkjs?.billing;\n  }\n\n  get apiKeys(): APIKeysNamespace | undefined {\n    return this.clerkjs?.apiKeys;\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = async (props: any): Promise<void> => {\n    const clerkjs = await this.#waitForClerkJS();\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (clerkjs && '__unstable__updateProps' in clerkjs) {\n      return (clerkjs as any).__unstable__updateProps(props);\n    }\n  };\n\n  __experimental_navigateToTask = async (params?: NextTaskParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.__experimental_navigateToTask(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = (params: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  openSignIn = (props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  __internal_openCheckout = (props?: __internal_CheckoutProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openCheckout(props);\n    } else {\n      this.preopenCheckout = props;\n    }\n  };\n\n  __internal_closeCheckout = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeCheckout();\n    } else {\n      this.preopenCheckout = null;\n    }\n  };\n\n  __internal_openPlanDetails = (props?: __internal_PlanDetailsProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openPlanDetails(props);\n    } else {\n      this.preopenPlanDetails = props;\n    }\n  };\n\n  __internal_closePlanDetails = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closePlanDetails();\n    } else {\n      this.preopenPlanDetails = null;\n    }\n  };\n\n  __internal_openReverification = (props?: __internal_UserVerificationModalProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openReverification(props);\n    } else {\n      this.preopenUserVerification = props;\n    }\n  };\n\n  __internal_closeReverification = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeReverification();\n    } else {\n      this.preopenUserVerification = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openWaitlist = (props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openWaitlist(props);\n    } else {\n      this.preOpenWaitlist = props;\n    }\n  };\n\n  closeWaitlist = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeWaitlist();\n    } else {\n      this.preOpenWaitlist = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props?: OrganizationSwitcherProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  __experimental_prefetchOrganizationSwitcher = () => {\n    const callback = () => this.clerkjs?.__experimental_prefetchOrganizationSwitcher();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('__experimental_prefetchOrganizationSwitcher', callback);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props?: OrganizationListProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps?: UserButtonProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  mountWaitlist = (node: HTMLDivElement, props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountWaitlist(node, props);\n    } else {\n      this.premountWaitlistNodes.set(node, props);\n    }\n  };\n\n  unmountWaitlist = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountWaitlist(node);\n    } else {\n      this.premountWaitlistNodes.delete(node);\n    }\n  };\n\n  mountPricingTable = (node: HTMLDivElement, props?: PricingTableProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountPricingTable(node, props);\n    } else {\n      this.premountPricingTableNodes.set(node, props);\n    }\n  };\n\n  unmountPricingTable = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountPricingTable(node);\n    } else {\n      this.premountPricingTableNodes.delete(node);\n    }\n  };\n\n  mountApiKeys = (node: HTMLDivElement, props?: APIKeysProps): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountApiKeys(node, props);\n    } else {\n      this.premountApiKeysNodes.set(node, props);\n    }\n  };\n\n  unmountApiKeys = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountApiKeys(node);\n    } else {\n      this.premountApiKeysNodes.delete(node);\n    }\n  };\n\n  __internal_mountOAuthConsent = (node: HTMLDivElement, props?: __internal_OAuthConsentProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_mountOAuthConsent(node, props);\n    } else {\n      this.premountOAuthConsentNodes.set(node, props);\n    }\n  };\n\n  __internal_unmountOAuthConsent = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_unmountOAuthConsent(node);\n    } else {\n      this.premountOAuthConsentNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    if (this.clerkjs) {\n      return this.clerkjs.addListener(listener);\n    } else {\n      const unsubscribe = () => {\n        const listenerHandlers = this.premountAddListenerCalls.get(listener);\n        if (listenerHandlers) {\n          listenerHandlers.nativeUnsubscribe?.();\n          this.premountAddListenerCalls.delete(listener);\n        }\n      };\n      this.premountAddListenerCalls.set(listener, { unsubscribe, nativeUnsubscribe: undefined });\n      return unsubscribe;\n    }\n  };\n\n  navigate = (to: string) => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = async (...args: Parameters<Clerk['redirectWithAuth']>) => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n      return;\n    }\n  };\n\n  redirectToSignIn = async (opts?: SignInRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n      return;\n    }\n  };\n\n  redirectToSignUp = async (opts?: SignUpRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n      return;\n    }\n  };\n\n  redirectToUserProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n      return;\n    }\n  };\n\n  redirectToAfterSignUp = (): void => {\n    const callback = () => this.clerkjs?.redirectToAfterSignUp();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignUp', callback);\n    }\n  };\n\n  redirectToAfterSignIn = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignIn();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignIn', callback);\n    }\n  };\n\n  redirectToAfterSignOut = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignOut();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignOut', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n      return;\n    }\n  };\n\n  redirectToCreateOrganization = async () => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n      return;\n    }\n  };\n\n  redirectToWaitlist = async () => {\n    const callback = () => this.clerkjs?.redirectToWaitlist();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToWaitlist', callback);\n      return;\n    }\n  };\n\n  handleRedirectCallback = async (params: HandleOAuthCallbackParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = async (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams) => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params?: AuthenticateWithMetamaskParams) => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithCoinbaseWallet = async (params?: AuthenticateWithCoinbaseWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithCoinbaseWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithCoinbaseWallet', callback);\n    }\n  };\n\n  authenticateWithOKXWallet = async (params?: AuthenticateWithOKXWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithOKXWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithOKXWallet', callback);\n    }\n  };\n\n  authenticateWithWeb3 = async (params: ClerkAuthenticateWithWeb3Params) => {\n    const callback = () => this.clerkjs?.authenticateWithWeb3(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithWeb3', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (params: AuthenticateWithGoogleOneTapParams) => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  joinWaitlist = async (params: JoinWaitlistParams): Promise<WaitlistResource | void> => {\n    const callback = () => this.clerkjs?.joinWaitlist(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<WaitlistResource>;\n    } else {\n      this.premountMethodCalls.set('joinWaitlist', callback);\n    }\n  };\n\n  signOut = async (...args: Parameters<Clerk['signOut']>) => {\n    const callback = () => this.clerkjs?.signOut(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n", "export const without = <T extends object, P extends keyof T>(obj: T, ...props: P[]): Omit<T, P> => {\n  const copy = { ...obj };\n  for (const prop of props) {\n    delete copy[prop];\n  }\n  return copy;\n};\n\nexport const removeUndefined = <T extends object>(obj: T): Partial<T> => {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined && value !== null) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n};\n\nexport const applyFunctionToObj = <T extends Record<string, any>, R>(\n  obj: T,\n  fn: (val: any, key: string) => R,\n): Record<string, R> => {\n  const result = {} as Record<string, R>;\n  for (const key in obj) {\n    result[key] = fn(obj[key], key);\n  }\n  return result;\n};\n\nexport const filterProps = <T extends Record<string, any>>(obj: T, filter: (a: any) => boolean): T => {\n  const result = {} as T;\n  for (const key in obj) {\n    if (obj[key] && filter(obj[key])) {\n      result[key] = obj[key];\n    }\n  }\n  return result;\n};\n", "import type {\n  InitialState,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  OrganizationResource,\n  Resources,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\n\n/**\n * Derives authentication state based on the current rendering context (SSR or client-side).\n */\nexport const deriveState = (clerkOperational: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkOperational && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const sessionStatus = initialState.sessionStatus;\n  const sessionClaims = initialState.sessionClaims;\n  const session = initialState.session as SignedInSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as OrganizationCustomRoleKey;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n  const factorVerificationAge = initialState.factorVerificationAge;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    factorVerificationAge,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const sessionStatus = state.session?.status;\n  const sessionClaims: JwtPayload | null | undefined = state.session\n    ? state.session.lastActiveToken?.jwt?.claims\n    : null;\n  const factorVerificationAge: [number, number] | null = state.session ? state.session.factorVerificationAge : null;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    factorVerificationAge,\n  };\n};\n", "/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n", "/**\n * Type definition for event handler functions\n */\ntype EventHandler<Events extends Record<string, unknown>, Key extends keyof Events> = (payload: Events[Key]) => void;\n\n/**\n * @interface\n * Strongly-typed event bus interface that enables type-safe publish/subscribe patterns\n */\ntype EventBus<Events extends Record<string, unknown>> = {\n  /**\n   * Subscribe to an event\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @param opts - Optional configuration\n   * @param opts.notify - If true and the event was previously dispatched, handler will be called immediately with the latest payload\n   * @returns void\n   */\n  on: <Event extends keyof Events>(\n    event: Event,\n    handler: EventHandler<Events, Event>,\n    opts?: { notify?: boolean },\n  ) => void;\n\n  /**\n   * Subscribe to an event with pre-dispatch priority\n   * Pre-dispatch handlers are called before regular event handlers when an event is dispatched\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @returns void\n   */\n  prioritizedOn: <Event extends keyof Events>(event: Event, handler: EventHandler<Events, Event>) => void;\n\n  /**\n   * Publish an event with a payload\n   * Triggers all registered handlers for the event\n   *\n   * @param event - The event name to publish\n   * @param payload - The data to pass to event handlers\n   * @returns void\n   */\n  emit: <Event extends keyof Events>(event: Event, payload: Events[Event]) => void;\n\n  /**\n   * Unsubscribe from an event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all handlers for the event are removed\n   * @returns void\n   */\n  off: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Unsubscribe from a pre-dispatch event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all pre-dispatch handlers for the event are removed\n   * @returns void\n   */\n  prioritizedOff: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Internal utilities for the event bus\n   */\n  internal: {\n    /**\n     * Retrieve all listeners for a specific event\n     *\n     * @param event - The event name to get listeners for\n     * @returns Array of handler functions\n     */\n    retrieveListeners: <Event extends keyof Events>(event: Event) => Array<(...args: any[]) => void>;\n  };\n};\n\n/**\n * @internal\n */\ntype InternalOn = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  latestPayloadMap: Map<keyof Events, any>,\n  event: Event,\n  handler: EventHandler<Events, Event>,\n  opts?: { notify?: boolean },\n) => void;\n\n/**\n * @internal\n */\ntype InternalOff = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  handler?: EventHandler<Events, Event>,\n) => void;\n\n/**\n * @internal\n */\ntype InternalDispatch = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  payload: Events[Event],\n) => void;\n\n/**\n * @internal\n */\nconst _on: InternalOn = (eventToHandlersMap, latestPayloadMap, event, handler, opts) => {\n  const { notify } = opts || {};\n  let handlers = eventToHandlersMap.get(event);\n\n  if (!handlers) {\n    handlers = [];\n    eventToHandlersMap.set(event, handlers);\n  }\n\n  handlers.push(handler);\n\n  if (notify && latestPayloadMap.has(event)) {\n    handler(latestPayloadMap.get(event));\n  }\n};\n\n/**\n * @internal\n */\nconst _dispatch: InternalDispatch = (eventToHandlersMap, event, payload) =>\n  (eventToHandlersMap.get(event) || []).map(h => h(payload));\n\n/**\n * @internal\n */\nconst _off: InternalOff = (eventToHandlersMap, event, handler) => {\n  const handlers = eventToHandlersMap.get(event);\n  if (handlers) {\n    if (handler) {\n      handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n    } else {\n      eventToHandlersMap.set(event, []);\n    }\n  }\n};\n\n/**\n * A ES6/2015 compatible 300 byte event bus\n *\n * Creates a strongly-typed event bus that enables publish/subscribe communication between components.\n *\n * @template Events - A record type that maps event names to their payload types\n * @returns An EventBus instance with the following methods:\n * - `on`: Subscribe to an event\n * - `onPreDispatch`: Subscribe to an event, triggered before regular subscribers\n * - `emit`: Publish an event with payload\n * - `off`: Unsubscribe from an event\n * - `offPreDispatch`: Unsubscribe from a pre-dispatch event\n *\n * @example\n * // Define event types\n * const eventBus = createEventBus<{\n *   'user-login': { userId: string; timestamp: number };\n *   'data-updated': { records: any[] };\n *   'error': Error;\n * }>();\n *\n * // Subscribe to events\n * eventBus.on('user-login', ({ userId, timestamp }) => {\n *   console.log(`User ${userId} logged in at ${timestamp}`);\n * });\n *\n * // Subscribe with immediate notification if event was already dispatched\n * eventBus.on('user-login', (payload) => {\n *   // This will be called immediately if 'user-login' was previously dispatched\n * }, { notify: true });\n *\n * // Publish an event\n * eventBus.emit('user-login', { userId: 'abc123', timestamp: Date.now() });\n *\n * // Unsubscribe from event\n * const handler = (payload) => console.log(payload);\n * eventBus.on('error', handler);\n * // Later...\n * eventBus.off('error', handler);\n *\n * // Unsubscribe all handlers for an event\n * eventBus.off('data-updated');\n */\nexport const createEventBus = <Events extends Record<string, unknown>>(): EventBus<Events> => {\n  const eventToHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n  const latestPayloadMap = new Map<keyof Events, any>();\n  const eventToPredispatchHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n\n  const emit: EventBus<Events>['emit'] = (event, payload) => {\n    latestPayloadMap.set(event, payload);\n    _dispatch(eventToPredispatchHandlersMap, event, payload);\n    _dispatch(eventToHandlersMap, event, payload);\n  };\n\n  return {\n    // Subscribe to an event\n    on: (...args) => _on(eventToHandlersMap, latestPayloadMap, ...args),\n    // Subscribe to an event with priority\n    // Registered handlers with `prioritizedOn` will be called before handlers registered with `on`\n    prioritizedOn: (...args) => _on(eventToPredispatchHandlersMap, latestPayloadMap, ...args),\n    // Dispatch an event\n    emit,\n    // Unsubscribe from an event\n    off: (...args) => _off(eventToHandlersMap, ...args),\n    // Unsubscribe from an event with priority\n    // Unsubscribes handlers only registered with `prioritizedOn`\n    prioritizedOff: (...args) => _off(eventToPredispatchHandlersMap, ...args),\n\n    // Internal utilities\n    internal: {\n      retrieveListeners: event => eventToHandlersMap.get(event) || [],\n    },\n  };\n};\n", "import type { ClerkEventPayload } from '@clerk/types';\n\nimport { createEventBus } from './eventBus';\n\nexport const clerkEvents = {\n  Status: 'status',\n} satisfies Record<string, keyof ClerkEventPayload>;\n\nexport const createClerkEventBus = () => {\n  return createEventBus<ClerkEventPayload>();\n};\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAWA,QAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAcC,UAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B,QAAAC;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAC;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,QAAAC,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIJ,UAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,YAAWD,QAAM,UACjBG,aAAYH,QAAM,WAClBE,mBAAkBF,QAAM,iBACxBI,iBAAgBJ,QAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAWA,QAAM,uBAAuBA,QAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACoCO,SAAS,wBAAwB,KAAwC;AAC9E,SAAO,gBAAgB;AACzB;AAqHO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;EAiB3C,YAAY,SAAiB,EAAE,KAAK,GAAqB;AACvD,UAAM,SAAS;AACf,UAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,MAAM,GAAG,GAAG;AACzD,UAAM,YAAY,QAAQ,QAAQ,OAAO,EAAE;AAC3C,UAAM,WAAW,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC;;SAAc,IAAI;;AAChE,UAAM,QAAQ;AAehB,SAAO,WAAW,MAAM;AACtB,aAAO,IAAI,KAAK,IAAI;UAAc,KAAK,OAAO;IAChD;AAfE,WAAO,eAAe,MAAM,mBAAkB,SAAS;AAEvD,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO;EACd;AAUF;AAkCA,IAAM,kBAAkB,OAAO,OAAO;EACpC,6BAA6B;EAC7B,mCAAmC;EACnC,mCAAmC;EACnC,8BAA8B;EAC9B,sBAAsB;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,aAAa,eAAe,GAAsC;AACpG,MAAI,MAAM;AAEV,QAAM,WAAW;IACf,GAAG;IACH,GAAG;EACL;AAEA,WAAS,aAAa,YAAoB,cAAgD;AACxF,QAAI,CAAC,cAAc;AACjB,aAAO,GAAG,GAAG,KAAK,UAAU;IAC9B;AAEA,QAAI,MAAM;AACV,UAAM,UAAU,WAAW,SAAS,uBAAuB;AAE3D,eAAW,SAAS,SAAS;AAC3B,YAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS;AAC5D,YAAM,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,WAAW;IAClD;AAEA,WAAO,GAAG,GAAG,KAAK,GAAG;EACvB;AAEA,SAAO;IACL,eAAe,EAAE,aAAAK,aAAY,GAAsC;AACjE,UAAI,OAAOA,iBAAgB,UAAU;AACnC,cAAMA;MACR;AACA,aAAO;IACT;IAEA,YAAY,EAAE,gBAAAC,gBAAe,GAAsC;AACjE,aAAO,OAAO,UAAUA,mBAAkB,CAAC,CAAC;AAC5C,aAAO;IACT;IAEA,gCAAgC,QAAiC;AAC/D,YAAM,IAAI,MAAM,aAAa,SAAS,mCAAmC,MAAM,CAAC;IAClF;IAEA,qBAAqB,QAAiC;AACpD,YAAM,IAAI,MAAM,aAAa,SAAS,6BAA6B,MAAM,CAAC;IAC5E;IAEA,kCAAyC;AACvC,YAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;IAC1E;IAEA,6BAAoC;AAClC,YAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;IACrE;IAEA,+BAA+B,QAAoC;AACjE,YAAM,IAAI,MAAM,aAAa,SAAS,sBAAsB,MAAM,CAAC;IACrE;IAEA,MAAM,SAAwB;AAC5B,YAAM,IAAI,MAAM,aAAa,OAAO,CAAC;IACvC;EACF;AACF;;;AC7UA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,QAAQ;AACzB,QAAM,UAAU,GAAG;AACrB;AACA,IAAIC,YAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ;AACf,cAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAChE;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAC9I,IAAI,gBAAgB,CAAC,KAAK,QAAQ,QAAQ,OAAO,IAAI,GAAG,KAAK,YAAY,YAAY,GAAG;AACxF,IAAI,eAAe,CAAC,KAAK,QAAQ,YAAY,cAAc,KAAK,QAAQ,yBAAyB,GAAG,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AAE9I,IAAI,eAAe,CAAC,KAAK,QAAQ,OAAO,YAAY,cAAc,KAAK,QAAQ,wBAAwB,GAAG,SAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACrK,IAAI,kBAAkB,CAAC,KAAK,QAAQ,YAAY,cAAc,KAAK,QAAQ,uBAAuB,GAAG;;;ACoBrG,IAAM,mBAAkC;EACtC,YAAY;IACV,cAAc;IACd,OAAO;EACT;EACA,QAAQ;IACN,cAAc;IACd,OAAO;EACT;EACA,UAAU;IACR,cAAc;IACd,OAAO;EACT;EACA,KAAK;IACH,cAAc;IACd,OAAO;EACT;AACF;AAEA,IAAM,iBAAiB,oBAAI,IAA8B,CAAC,gBAAgB,iBAAiB,cAAc,CAAC;AAE1G,IAAM,gBAAgB,oBAAI,IAA8B,CAAC,cAAc,UAAU,YAAY,KAAK,CAAC;AAGnG,IAAM,gBAAgB,CAAC,WAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,UAAe,eAAe,IAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,SAAc,cAAc,IAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,UAAkB,MAAM,QAAQ,YAAY,MAAM;AAOzE,IAAM,wBAA+C,CAAC,QAAQ,YAAY;AACxE,QAAM,EAAE,OAAO,SAAS,eAAe,IAAI;AAC3C,MAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,YAAY;AACtC,WAAO;EACT;AAEA,MAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;AACzC,WAAO;EACT;AAEA,MAAI,OAAO,YAAY;AACrB,WAAO,eAAe,SAAS,cAAc,OAAO,UAAU,CAAC;EACjE;AAEA,MAAI,OAAO,MAAM;AACf,WAAO,cAAc,OAAO,MAAM,cAAc,OAAO,IAAI;EAC7D;AACA,SAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;AACtE,QAAM,EAAE,KAAK,aAAa,MAAM,aAAa,IAAI,aAAa,KAAK;AACnE,QAAM,CAAC,OAAO,GAAG,IAAI,cAAc,MAAM,GAAG;AAC5C,QAAM,KAAK,OAAO;AAElB,MAAI,UAAU,OAAO;AACnB,WAAO,YAAY,SAAS,EAAE;EAChC,WAAW,UAAU,QAAQ;AAC3B,WAAO,aAAa,SAAS,EAAE;EACjC,OAAO;AAEL,WAAO,CAAC,GAAG,aAAa,GAAG,YAAY,EAAE,SAAS,EAAE;EACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;AAChF,QAAM,EAAE,UAAU,MAAM,IAAI;AAE5B,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO,sBAAsB,UAAU,OAAO,OAAO;EACvD;AAEA,MAAI,OAAO,QAAQ,OAAO;AACxB,WAAO,sBAAsB,OAAO,OAAO,IAAI;EACjD;AACA,SAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;AACvD,QAAM,WAAW,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI,CAAA,MAAK,EAAE,KAAK,CAAC,IAAI,CAAC;AAG5D,SAAO;IACL,KAAK,SAAS,OAAO,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;IACjF,MAAM,SAAS,OAAO,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAA,MAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;EACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;AACxF,MAAI,CAAC,QAAQ;AACX,WAAO;EACT;AAEA,QAAM,wBAAwB,CAACC,YAAiC;AAC9D,QAAI,OAAOA,YAAW,UAAU;AAC9B,aAAO,iBAAiBA,OAAM;IAChC;AACA,WAAOA;EACT;AAEA,QAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;AACvF,QAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;AAE/F,MAAI,sBAAsB,oBAAoB;AAC5C,WAAO,sBAAsB,KAAK,MAAM,MAAM;EAChD;AAEA,SAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,sBAAsB,MAAM;AAChH,MAAI,CAAC,OAAO,kBAAkB,CAAC,uBAAuB;AACpD,WAAO;EACT;AAEA,QAAM,wBAAwB,6BAA6B,OAAO,cAAc;AAChF,MAAI,CAAC,uBAAuB;AAC1B,WAAO;EACT;AAEA,QAAM,EAAE,OAAO,aAAa,IAAI,sBAAsB;AACtD,QAAM,CAAC,YAAY,UAAU,IAAI;AAIjC,QAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AACvE,QAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AAEvE,UAAQ,OAAO;IACb,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO,eAAe,KAAK,iBAAiB;IAC9C,KAAK;AACH,aAAO,eAAe,KAAK,iBAAiB,kBAAkB;EAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;AAC3G,SAAO,CAAC,WAAoB;AAC1B,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO;IACT;AAEA,UAAM,uBAAuB,0BAA0B,QAAQ,OAAO;AACtE,UAAM,mBAAmB,sBAAsB,QAAQ,OAAO;AAC9D,UAAM,8BAA8B,iCAAiC,QAAQ,OAAO;AAEpF,QAAI,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,CAAA,MAAK,MAAM,IAAI,GAAG;AACjG,aAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,CAAA,MAAK,MAAM,IAAI;IACrG;AAEA,WAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,MAAM,CAAA,MAAK,MAAM,IAAI;EACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC;EACxB,YAAY;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAAC;IACA;EACF;EACA,SAAS,EAAE,0BAA0B,KAAK;AAC5C,MAAmD;AACjD,MAAI,cAAc,UAAa,WAAW,QAAW;AACnD,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA,eAAe;MACf;MACA,OAAO;MACP,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK;MACL;MACA;IACF;EACF;AAEA,MAAI,cAAc,QAAQ,WAAW,MAAM;AACzC,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA,eAAe;MACf,OAAO;MACP,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK,MAAM;MACX;MACA;IACF;EACF;AAEA,MAAI,2BAA2B,kBAAkB,WAAW;AAC1D,WAAO;MACL,UAAU;MACV,YAAY;MACZ,WAAW;MACX,QAAQ;MACR,eAAe;MACf,OAAO;MACP,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK,MAAM;MACX;MACA;IACF;EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;AACtE,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA;MACA,OAAO,SAAS;MAChB;MACA;MACA,SAAS,WAAW;MACpB,KAAAA;MACA;MACA;IACF;EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;AACxD,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA;MACA,OAAO,SAAS;MAChB,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAAA;MACA;MACA;IACF;EACF;AACF;;;ACnSO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,gBAAgB,CAAA,UAAS,MAAM,YAAY,EAAE,QAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,UAAU,CAAA,WAAU,IAAI,OAAO,YAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;AACtD,QAAM,gBAAgB,CAAC,QAAkB;AACvC,QAAI,CAAC,KAAK;AACR,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,IAAI,CAAA,OAAM;AACnB,YAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,GAAG;AAC/C,iBAAO,cAAc,EAAE;QACzB;AACA,eAAO;MACT,CAAC;IACH;AAEA,UAAM,OAAO,EAAE,GAAG,IAAI;AACtB,UAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,eAAW,WAAW,MAAM;AAC1B,YAAM,UAAU,UAAU,QAAQ,SAAS,CAAC;AAC5C,UAAI,YAAY,SAAS;AACvB,aAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,eAAO,KAAK,OAAO;MACrB;AACA,UAAI,OAAO,KAAK,OAAO,MAAM,UAAU;AACrC,aAAK,OAAO,IAAI,cAAc,KAAK,OAAO,CAAC;MAC7C;IACF;AACA,WAAO;EACT;AAEA,SAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;;;AC1GjE,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;EACpD;AACA,SAAO;AACT;;;ACTO,IAAM,0BAA0B;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;;;ACCA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAqB7B,SAAS,oBACd,KACA,UAA0F,CAAC,GACpE;AACvB,QAAM,OAAO;AAEb,MAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;AAClC,QAAI,QAAQ,SAAS,CAAC,KAAK;AACzB,YAAM,IAAI;QACR;MACF;IACF;AACA,QAAI,QAAQ,SAAS,CAAC,iBAAiB,GAAG,GAAG;AAC3C,YAAM,IAAI,MAAM,4BAA4B;IAC9C;AACA,WAAO;EACT;AAEA,QAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,MAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAGlD,gBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,MAAI,QAAQ,UAAU;AACpB,kBAAc,QAAQ;EACxB,WAAW,iBAAiB,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAClF,kBAAc,SAAS,QAAQ,MAAM;EACvC;AAEA,SAAO;IACL;IACA;EACF;AACF;AAQO,SAAS,iBAAiB,MAAc,IAAI;AACjD,MAAI;AACF,UAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,UAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,WAAO,kBAAkB;EAC3B,QAAQ;AACN,WAAO;EACT;AACF;AAEO,SAAS,6BAA6B;AAC3C,QAAM,uBAAuB,oBAAI,IAAqB;AAEtD,SAAO;IACL,mBAAmB,CAAC,QAA+B;AACjD,UAAI,CAAC,KAAK;AACR,eAAO;MACT;AAEA,YAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,UAAI,QAAQ,QAAW;AACrB,cAAM,wBAAwB,KAAK,CAAA,MAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,6BAAqB,IAAI,UAAU,GAAG;MACxC;AACA,aAAO;IACT;EACF;AACF;;;AC5GA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAWE,cAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AAFK,qCAAA,oBAAA,QAAA;AAqCL,iBAAY,SAAC,OAA+B;AAC1C,QAAM,EAAE,IAAI,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,IAAI;AAE/C,QAAM,iBAA4F;IAChG,GAAG;IACH,GAAG;EACL;AAEA,SAAO,KAAK;IACV,OAAO,KAAK;MACV,GAAG;MACH,GAAG;IACL,CAAC,EACE,KAAK,EACL,IAAI,CAAA,QAAO,eAAe,GAAG,CAAC;EACnC;AACF;AAEI,YAAM,WAAkD;AAC1D,QAAM,cAAc,aAAa,QAAQ,aAAA,MAAK,WAAA,CAAW;AAEzD,MAAI,CAAC,aAAa;AAChB,WAAO,CAAC;EACV;AAEA,SAAO,KAAK,MAAM,WAAW;AAC/B;AASI,qBAAe,WAAY;AAC7B,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;EACT;AAEA,QAAM,UAAU,OAAO;AACvB,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,MAAI;AACF,UAAM,UAAU;AAChB,YAAQ,QAAQ,SAAS,OAAO;AAChC,YAAQ,WAAW,OAAO;AAE1B,WAAO;EACT,SAAS,KAAc;AACrB,UAAM,uBACJ,eAAe;KAEd,IAAI,SAAS,wBAAwB,IAAI,SAAS;AAErD,QAAI,wBAAwB,QAAQ,SAAS,GAAG;AAC9C,cAAQ,WAAW,aAAA,MAAK,WAAA,CAAW;IACrC;AAEA,WAAO;EACT;AACF;AC9GF,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAkDE,UAAA,oBAAA,QAAA;AACA,kBAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AACA,UAAA,oBAAA,QAAA;AACA,gBAAA,oBAAA,QAAA;AALK,gCAAA,oBAAA,QAAA;AAmFL,kBAAa,SAAC,iBAAiC,mBAA4B;AACzE,SAAO,KAAK,aAAa,CAAC,KAAK,WAAW,gBAAA,MAAK,+BAAA,kBAAA,EAAL,KAAA,MAAsB,iBAAiB,iBAAA;AACnF;AAEA,qBAAgB,SAAC,iBAAiC,mBAA4B;AAC5E,QAAM,aAAa,KAAK,OAAO;AAE/B,QAAM,cACJ,cAAc,aAAA,MAAK,OAAA,EAAQ,iBAC1B,OAAO,sBAAsB,eAAe,cAAc;AAE7D,MAAI,CAAC,aAAa;AAChB,WAAO;EACT;AAEA,SAAO,CAAC,aAAA,MAAK,eAAA,EAAgB,iBAAiB,eAAe;AAC/D;AAEA,mBAAc,WAAS;AAErB,MAAI,OAAO,WAAW,aAAa;AACjC,oBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;AACA;EACF;AAEA,QAAM,eAAe,aAAA,MAAK,OAAA,EAAQ,UAAU,aAAA,MAAK,OAAA,EAAQ;AACzD,MAAI,cAAc;AAGhB,QAAI,aAAA,MAAK,aAAA,GAAe;AACtB,YAAM,SAAS,OAAO,uBAAuB,cAAc,qBAAqB;AAChF,aAAO,aAAA,MAAK,aAAA,CAAa;IAC3B;AACA,oBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;AACA;EACF;AAGA,MAAI,aAAA,MAAK,aAAA,GAAe;AACtB;EACF;AAEA,MAAI,yBAAyB,QAAQ;AACnC,iBAAA,MAAK,eAAgB,oBAAoB,MAAM;AAC7C,sBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;IACF,CAAC,CAAA;EACH,OAAO;AAEL,iBAAA,MAAK,eAAgB,WAAW,MAAM;AACpC,sBAAA,MAAK,+BAAA,QAAA,EAAL,KAAA,IAAA;IACF,GAAG,CAAC,CAAA;EACN;AACF;AAEA,WAAM,WAAS;AACb,QAAM,IAAI,IAAI,aAAa,aAAA,MAAK,OAAA,EAAQ,QAAQ,GAAG;IACjD,QAAQ;;IAER,MAAM,KAAK,UAAU;MACnB,QAAQ,aAAA,MAAK,OAAA;IACf,CAAC;IACD,SAAS;MACP,gBAAgB;IAClB;EACF,CAAC,EACE,MAAM,MAAM,MAAM,EAClB,KAAK,MAAM;AACV,iBAAA,MAAK,SAAU,CAAC,CAAA;EAClB,CAAC,EACA,MAAM,MAAM,MAAM;AACvB;AAKA,cAAS,SAAC,OAAgC,SAA8B;AACtE,MAAI,CAAC,KAAK,SAAS;AACjB;EACF;AAEA,MAAI,OAAO,QAAQ,mBAAmB,aAAa;AACjD,YAAQ,eAAe,qBAAqB,KAAK;AACjD,YAAQ,IAAI,OAAO;AACnB,YAAQ,SAAS;EACnB,OAAO;AACL,YAAQ,IAAI,qBAAqB,OAAO,OAAO;EACjD;AACF;AAOA,oBAAe,WAAG;AAChB,MAAI,cAAc;IAChB,MAAM,aAAA,MAAK,SAAA,EAAU;IACrB,SAAS,aAAA,MAAK,SAAA,EAAU;EAC1B;AAGA,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AAEjD,kBAAc,EAAE,GAAG,aAAa,GAAG,OAAO,MAAM,YAAY,YAAY;EAC1E;AAEA,SAAO;AACT;AAKA,oBAAe,SAAC,OAAgC,SAAoD;AAClG,QAAM,cAAc,gBAAA,MAAK,+BAAA,iBAAA,EAAL,KAAA,IAAA;AAEpB,SAAO;IACL;IACA,IAAI,aAAA,MAAK,SAAA,EAAU,gBAAgB;IACnC,IAAI,aAAA,MAAK,SAAA,EAAU,gBAAgB;IACnC,KAAK,YAAY;IACjB,MAAM,YAAY;IAClB,GAAI,aAAA,MAAK,SAAA,EAAU,iBAAiB,EAAE,IAAI,aAAA,MAAK,SAAA,EAAU,eAAe,IAAI,CAAC;IAC7E,GAAI,aAAA,MAAK,SAAA,EAAU,YAAY,EAAE,IAAI,aAAA,MAAK,SAAA,EAAU,UAAU,IAAI,CAAC;IACnE;EACF;AACF;AE/PF,IAAM,sBAAsB;AASrB,SAAS,kBACd,QACA,SACsC;AACtC,SAAO;IACL,OAAO;IACP,SAAS;MACP;MACA,GAAG;IACL;EACF;AACF;;;AGZA,IAAAC,iBAA4B;;;AaFrB,SAAS,iCACd,yBACA,gBACA;AACA,SAAO,wBAAwB;IAC7B,CAAA,2BAA0B,uBAAuB,aAAa,OAAO;EACvE;AACF;;;ACfO,IAAM,OAAO,IAAI,UAAuB;AAE/C;;;ACQO,IAAM,wBAAwB,MAAM;AACzC,MAAI,UAAoB;AACxB,MAAI,SAAmB;AACvB,QAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxC,cAAU;AACV,aAAS;EACX,CAAC;AACD,SAAO,EAAE,SAAS,SAAS,OAAO;AACpC;;;ACZA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,mBAKK;EACL,aAAa;IACX,MAAM;IACN,QAAQ;IACR,UAAU;MACR,gBAAgB;IAClB;EACF;AACF;AAOA,IAAM,uBAAuB,CAAC,WAAkE;;AAC9F,SACE,UACA,OAAO,WAAW,YAClB,iBAAiB,YACjB,YAAO,gBAAP,mBAAoB,UAAS,iBAC7B,YAAO,gBAAP,mBAAoB,YAAW;AAEnC;;;AC3CA,IAAAC,gBAAkB;ACUlB,IAAAA,gBAAkB;;;AiBXlB;AAAA;AAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC,gBAAmE;AACnE,kBAAqC;;;ACArC,mBAA6G;;;ACD7G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAM,cAAc;AACpB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,yBAAyB;;;ACH/B,IAAI,MAAM,OAAO,UAAU;AAEpB,SAAS,OAAO,KAAK,KAAK;AAChC,MAAI,MAAM;AACV,MAAI,QAAQ,IAAK,QAAO;AAExB,MAAI,OAAO,QAAQ,OAAK,IAAI,iBAAiB,IAAI,aAAa;AAC7D,QAAI,SAAS,KAAM,QAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ;AACxD,QAAI,SAAS,OAAQ,QAAO,IAAI,SAAS,MAAM,IAAI,SAAS;AAE5D,QAAI,SAAS,OAAO;AACnB,WAAK,MAAI,IAAI,YAAY,IAAI,QAAQ;AACpC,eAAO,SAAS,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,MAC5C;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;AACrC,YAAM;AACN,WAAK,QAAQ,KAAK;AACjB,YAAI,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,KAAK,IAAI,EAAG,QAAO;AACjE,YAAI,EAAE,QAAQ,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAG,QAAO;AAAA,MAC7D;AACA,aAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,IACpC;AAAA,EACD;AAEA,SAAO,QAAQ,OAAO,QAAQ;AAC/B;;;AFtBA,IAAM,iBAAiB,oBAAI,QAAQ;AAGnC,IAAMC,QAAO,MAAI;AAAC;AAKlB,IAAM;AAAA;AAAA,EAA8BA,MAAK;AAAA;AACzC,IAAM,SAAS;AACf,IAAM,cAAc,CAAC,MAAI,MAAM;AAC/B,IAAM,aAAa,CAAC,MAAI,OAAO,KAAK;AACpC,IAAM,eAAe,CAAC,GAAG,OAAK;AAAA,EACtB,GAAG;AAAA,EACH,GAAG;AACP;AACJ,IAAM,gBAAgB,CAAC,MAAI,WAAW,EAAE,IAAI;AAE5C,IAAM,cAAc,CAAC;AACrB,IAAM,gBAAgB,CAAC;AACvB,IAAM,gBAAgB;AAEtB,IAAM,kBAAkB,OAAO,UAAU;AACzC,IAAM,oBAAoB,OAAO,YAAY;AAC7C,IAAM,eAAe,mBAAmB,UAAU;AAClD,IAAM,2BAA2B,MAAI,mBAAmB,OAAO,OAAO,uBAAuB,KAAK;AAClG,IAAM,oBAAoB,CAACC,QAAO,QAAM;AACpC,QAAM,QAAQ,eAAe,IAAIA,MAAK;AACtC,SAAO;AAAA;AAAA,IAEH,MAAI,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA;AAAA,IAE3C,CAAC,SAAO;AACJ,UAAI,CAAC,YAAY,GAAG,GAAG;AACnB,cAAM,OAAOA,OAAM,IAAI,GAAG;AAG1B,YAAI,EAAE,OAAO,gBAAgB;AACzB,wBAAc,GAAG,IAAI;AAAA,QACzB;AACA,cAAM,CAAC,EAAE,KAAK,aAAa,MAAM,IAAI,GAAG,QAAQ,WAAW;AAAA,MAC/D;AAAA,IACJ;AAAA;AAAA,IAEA,MAAM,CAAC;AAAA;AAAA,IAEP,MAAI;AACA,UAAI,CAAC,YAAY,GAAG,GAAG;AAEnB,YAAI,OAAO,cAAe,QAAO,cAAc,GAAG;AAAA,MACtD;AAEA,aAAO,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA,IAClD;AAAA,EACJ;AACJ;AASI,IAAI,SAAS;AACjB,IAAM,WAAW,MAAI;AAErB,IAAM,CAAC,eAAe,cAAc,IAAI,mBAAmB,OAAO,mBAAmB;AAAA,EACjF,OAAO,iBAAiB,KAAK,MAAM;AAAA,EACnC,OAAO,oBAAoB,KAAK,MAAM;AAC1C,IAAI;AAAA,EACAD;AAAA,EACAA;AACJ;AACA,IAAM,YAAY,MAAI;AAClB,QAAM,kBAAkB,qBAAqB,SAAS;AACtD,SAAO,YAAY,eAAe,KAAK,oBAAoB;AAC/D;AACA,IAAM,YAAY,CAAC,aAAW;AAE1B,MAAI,mBAAmB;AACnB,aAAS,iBAAiB,oBAAoB,QAAQ;AAAA,EAC1D;AACA,gBAAc,SAAS,QAAQ;AAC/B,SAAO,MAAI;AACP,QAAI,mBAAmB;AACnB,eAAS,oBAAoB,oBAAoB,QAAQ;AAAA,IAC7D;AACA,mBAAe,SAAS,QAAQ;AAAA,EACpC;AACJ;AACA,IAAM,gBAAgB,CAAC,aAAW;AAE9B,QAAM,WAAW,MAAI;AACjB,aAAS;AACT,aAAS;AAAA,EACb;AAEA,QAAM,YAAY,MAAI;AAClB,aAAS;AAAA,EACb;AACA,gBAAc,UAAU,QAAQ;AAChC,gBAAc,WAAW,SAAS;AAClC,SAAO,MAAI;AACP,mBAAe,UAAU,QAAQ;AACjC,mBAAe,WAAW,SAAS;AAAA,EACvC;AACJ;AACA,IAAM,SAAS;AAAA,EACX;AAAA,EACA;AACJ;AACA,IAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AACJ;AAEA,IAAM,kBAAkB,CAAC,aAAAE,QAAM;AAC/B,IAAM,YAAY,CAAC,mBAAmB;AAEtC,IAAM,MAAM,CAAC,MAAI,yBAAyB,IAAI,OAAO,uBAAuB,EAAE,CAAC,IAAI,WAAW,GAAG,CAAC;AAIlG,IAAM,4BAA4B,YAAY,yBAAY;AAE1D,IAAM,sBAAsB,OAAO,cAAc,eAAe,UAAU;AAE1E,IAAM,iBAAiB,CAAC,aAAa,wBAAwB;AAAA,EACzD;AAAA,EACA;AACJ,EAAE,SAAS,oBAAoB,aAAa,KAAK,oBAAoB;AAMrE,IAAM,QAAQ,oBAAI,QAAQ;AAC1B,IAAM,eAAe,CAAC,OAAO,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,WAAW,IAAI;AAE7F,IAAI,UAAU;AASd,IAAM,aAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,SAAS,aAAa,KAAK,MAAM;AACvC,QAAM,UAAU,aAAa,KAAK,QAAQ;AAC1C,QAAM,gBAAgB,aAAa,KAAK,QAAQ;AAChD,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,SAAS;AAG5C,aAAS,MAAM,IAAI,GAAG;AACtB,QAAI,OAAQ,QAAO;AAInB,aAAS,EAAE,UAAU;AACrB,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAU,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AAEf,eAAS;AACT,YAAM,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAAC,YAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAAC,YAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAEA,IAAM,YAAY,CAAC,QAAM;AACrB,MAAI,WAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAO,WAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAGA,IAAI,cAAc;AAClB,IAAM,eAAe,MAAI,EAAE;AAE3B,eAAe,kBAAkB,MAAM;AACnC,QAAM,CAACD,QAAO,MAAM,OAAO,KAAK,IAAI;AAGpC,QAAM,UAAU,aAAa;AAAA,IACzB,eAAe;AAAA,IACf,cAAc;AAAA,EAClB,GAAG,OAAO,UAAU,YAAY;AAAA,IAC5B,YAAY;AAAA,EAChB,IAAI,SAAS,CAAC,CAAC;AACf,MAAI,gBAAgB,QAAQ;AAC5B,QAAM,wBAAwB,QAAQ;AACtC,MAAI,iBAAiB,QAAQ;AAC7B,QAAM,kBAAkB,CAAC,UAAQ;AAC7B,WAAO,OAAO,0BAA0B,aAAa,sBAAsB,KAAK,IAAI,0BAA0B;AAAA,EAClH;AACA,QAAM,eAAe,QAAQ;AAG7B,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,YAAY;AAClB,UAAM,cAAc,CAAC;AACrB,UAAM,KAAKA,OAAM,KAAK;AACtB,eAAW,OAAO,IAAG;AACjB;AAAA;AAAA,QACA,CAAC,iBAAiB,KAAK,GAAG,KAAK,UAAUA,OAAM,IAAI,GAAG,EAAE,EAAE;AAAA,QAAG;AACzD,oBAAY,KAAK,GAAG;AAAA,MACxB;AAAA,IACJ;AACA,WAAO,QAAQ,IAAI,YAAY,IAAI,WAAW,CAAC;AAAA,EACnD;AACA,SAAO,YAAY,IAAI;AACvB,iBAAe,YAAY,IAAI;AAE3B,UAAM,CAAC,GAAG,IAAI,UAAU,EAAE;AAC1B,QAAI,CAAC,IAAK;AACV,UAAM,CAAC,KAAK,GAAG,IAAI,kBAAkBA,QAAO,GAAG;AAC/C,UAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAIA,MAAK;AAC/E,UAAM,kBAAkB,MAAI;AACxB,YAAM,eAAe,mBAAmB,GAAG;AAC3C,YAAM,aAAa,WAAW,QAAQ,UAAU,IAAI,QAAQ,WAAW,IAAI,EAAE,MAAM,EAAE,IAAI,QAAQ,eAAe;AAChH,UAAI,YAAY;AAGZ,eAAO,MAAM,GAAG;AAChB,eAAO,QAAQ,GAAG;AAClB,YAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iBAAO,aAAa,CAAC,EAAmB,YAAY,EAAE,KAAK,MAAI,IAAI,EAAE,IAAI;AAAA,QAC7E;AAAA,MACJ;AACA,aAAO,IAAI,EAAE;AAAA,IACjB;AAEA,QAAI,KAAK,SAAS,GAAG;AAEjB,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,OAAO;AACX,QAAI;AAEJ,UAAM,mBAAmB,aAAa;AACtC,aAAS,GAAG,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,UAAM,oBAAoB,CAAC,YAAY,cAAc;AACrD,UAAM,QAAQ,IAAI;AAIlB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,cAAc,MAAM;AAC1B,UAAM,gBAAgB,YAAY,WAAW,IAAI,gBAAgB;AAEjE,QAAI,mBAAmB;AACnB,uBAAiB,WAAW,cAAc,IAAI,eAAe,eAAe,aAAa,IAAI;AAE7F,UAAI;AAAA,QACA,MAAM;AAAA,QACN,IAAI;AAAA,MACR,CAAC;AAAA,IACL;AACA,QAAI,WAAW,IAAI,GAAG;AAElB,UAAI;AACA,eAAO,KAAK,aAAa;AAAA,MAC7B,SAAS,KAAK;AAEV,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAEA,QAAI,QAAQ,cAAc,IAAI,GAAG;AAG7B,aAAO,MAAM,KAAK,MAAM,CAAC,QAAM;AAC3B,gBAAQ;AAAA,MACZ,CAAC;AAID,UAAI,qBAAqB,SAAS,GAAG,EAAE,CAAC,GAAG;AACvC,YAAI,MAAO,OAAM;AACjB,eAAO;AAAA,MACX,WAAW,SAAS,qBAAqB,gBAAgB,KAAK,GAAG;AAG7D,wBAAgB;AAEhB,YAAI;AAAA,UACA,MAAM;AAAA,UACN,IAAI;AAAA,QACR,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,QAAI,eAAe;AACf,UAAI,CAAC,OAAO;AAER,YAAI,WAAW,aAAa,GAAG;AAC3B,gBAAM,qBAAqB,cAAc,MAAM,aAAa;AAC5D,cAAI;AAAA,YACA,MAAM;AAAA,YACN,OAAO;AAAA,YACP,IAAI;AAAA,UACR,CAAC;AAAA,QACL,OAAO;AAEH,cAAI;AAAA,YACA;AAAA,YACA,OAAO;AAAA,YACP,IAAI;AAAA,UACR,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,GAAG,EAAE,CAAC,IAAI,aAAa;AAEhC,YAAQ,QAAQ,gBAAgB,CAAC,EAAE,KAAK,MAAI;AAGxC,UAAI;AAAA,QACA,IAAI;AAAA,MACR,CAAC;AAAA,IACL,CAAC;AAED,QAAI,OAAO;AACP,UAAI,aAAc,OAAM;AACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,oBAAoB,CAAC,cAAc,SAAO;AAC5C,aAAU,OAAO,cAAa;AAC1B,QAAI,aAAa,GAAG,EAAE,CAAC,EAAG,cAAa,GAAG,EAAE,CAAC,EAAE,IAAI;AAAA,EACvD;AACJ;AACA,IAAM,YAAY,CAAC,UAAU,YAAU;AAMnC,MAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,aAAa,sBAAsB,OAAO;AAGvD,UAAM,qBAAqB,uBAAO,OAAO,IAAI;AAC7C,UAAME,UAAS,eAAe,KAAK,WAAW,QAAQ;AACtD,QAAI,UAAUH;AACd,UAAM,gBAAgB,uBAAO,OAAO,IAAI;AACxC,UAAM,YAAY,CAAC,KAAK,aAAW;AAC/B,YAAM,OAAO,cAAc,GAAG,KAAK,CAAC;AACpC,oBAAc,GAAG,IAAI;AACrB,WAAK,KAAK,QAAQ;AAClB,aAAO,MAAI,KAAK,OAAO,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,IACpD;AACA,UAAM,SAAS,CAAC,KAAK,OAAO,SAAO;AAC/B,eAAS,IAAI,KAAK,KAAK;AACvB,YAAM,OAAO,cAAc,GAAG;AAC9B,UAAI,MAAM;AACN,mBAAW,MAAM,MAAK;AAClB,aAAG,OAAO,IAAI;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,eAAe,MAAI;AACrB,UAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAE/B,uBAAe,IAAI,UAAU;AAAA,UACzB;AAAA,UACA,uBAAO,OAAO,IAAI;AAAA,UAClB,uBAAO,OAAO,IAAI;AAAA,UAClB,uBAAO,OAAO,IAAI;AAAA,UAClBG;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,WAAW;AAOZ,gBAAM,eAAe,KAAK,UAAU,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAqC,WAAW,CAAC,CAAC;AACnJ,gBAAM,mBAAmB,KAAK,cAAc,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAqC,eAAe,CAAC,CAAC;AAC/J,oBAAU,MAAI;AACV,4BAAgB,aAAa;AAC7B,gCAAoB,iBAAiB;AAIrC,2BAAe,OAAO,QAAQ;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,iBAAa;AAMb,WAAO;AAAA,MACH;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,eAAe,IAAI,QAAQ,EAAE,CAAC;AAAA,EAClC;AACJ;AAGA,IAAM,eAAe,CAAC,GAAG,IAAI,QAAQ,YAAY,SAAO;AACpD,QAAM,gBAAgB,OAAO;AAC7B,QAAM,oBAAoB,KAAK;AAE/B,QAAM,UAAU,CAAC,GAAG,KAAK,OAAO,IAAI,QAAQ,MAAM,oBAAoB,IAAI,oBAAoB,OAAO,OAAO;AAC5G,MAAI,CAAC,YAAY,aAAa,KAAK,oBAAoB,eAAe;AAClE;AAAA,EACJ;AACA,aAAW,YAAY,SAAS,IAAI;AACxC;AACA,IAAM,UAAU;AAEhB,IAAM,CAAC,OAAO,MAAM,IAAI,UAAU,oBAAI,IAAI,CAAC;AAE3C,IAAM,gBAAgB;AAAA,EAAa;AAAA;AAAA,IAE/B,eAAeH;AAAA,IACf,WAAWA;AAAA,IACX,SAASA;AAAA,IACT;AAAA,IACA,aAAaA;AAAA;AAAA,IAEb,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA;AAAA,IAEpB,oBAAoB,iBAAiB,MAAQ;AAAA,IAC7C,uBAAuB,IAAI;AAAA,IAC3B,kBAAkB,IAAI;AAAA,IACtB,gBAAgB,iBAAiB,MAAO;AAAA;AAAA,IAExC;AAAA,IACA,UAAU,MAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA,UAAU,CAAC;AAAA,EACf;AAAA;AAAA,EACA;AAAM;AAEN,IAAM,eAAe,CAAC,GAAG,MAAI;AAEzB,QAAM,IAAI,aAAa,GAAG,CAAC;AAE3B,MAAI,GAAG;AACH,UAAM,EAAE,KAAK,IAAI,UAAU,GAAG,IAAI;AAClC,UAAM,EAAE,KAAK,IAAI,UAAU,GAAG,IAAI;AAClC,QAAI,MAAM,IAAI;AACV,QAAE,MAAM,GAAG,OAAO,EAAE;AAAA,IACxB;AACA,QAAI,MAAM,IAAI;AACV,QAAE,WAAW,aAAa,IAAI,EAAE;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,uBAAmB,4BAAc,CAAC,CAAC;AACzC,IAAM,YAAY,CAAC,UAAQ;AACvB,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,mBAAe,yBAAW,gBAAgB;AAChD,QAAM,qBAAqB,WAAW,KAAK;AAC3C,QAAM,aAAS,sBAAQ,MAAI,qBAAqB,MAAM,YAAY,IAAI,OAAO;AAAA,IACzE;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,qBAAiB,sBAAQ,MAAI,qBAAqB,SAAS,aAAa,cAAc,MAAM,GAAG;AAAA,IACjG;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,UAAU,OAAO;AAElC,QAAM,sBAAkB,qBAAO,SAAS;AACxC,MAAI,YAAY,CAAC,gBAAgB,SAAS;AACtC,oBAAgB,UAAU,UAAU,SAAS,eAAe,SAAS,KAAK,GAAG,MAAM;AAAA,EACvF;AACA,QAAM,eAAe,gBAAgB;AAErC,MAAI,cAAc;AACd,mBAAe,QAAQ,aAAa,CAAC;AACrC,mBAAe,SAAS,aAAa,CAAC;AAAA,EAC1C;AAEA,4BAA0B,MAAI;AAC1B,QAAI,cAAc;AACd,mBAAa,CAAC,KAAK,aAAa,CAAC,EAAE;AACnC,aAAO,aAAa,CAAC;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,aAAO,4BAAc,iBAAiB,UAAU,aAAa,OAAO;AAAA,IAChE,OAAO;AAAA,EACX,CAAC,CAAC;AACN;;;AG9iBA,IAAM,kBAAkB;;;ACMxB,IAAAI,gBAAkC;AAIlC,IAAM,iBAAiB,mBAAmB,OAAO;AACjD,IAAM,MAAM,iBAAiB,OAAO,uBAAuB,CAAC;AAC5D,IAAM,gBAAgB,MAAI;AACtB,MAAI,gBAAgB;AAEhB,WAAO,yBAAyB,cAAAC;AAAA,EACpC;AACJ;AAEA,IAAM,YAAY,CAAC,SAAO;AACtB,SAAO,WAAW,KAAK,CAAC,CAAC,IAAI;AAAA,IACzB,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,KAAK,CAAC,KAAK,CAAC;AAAA,EAChB,IAAI;AAAA,IACA,KAAK,CAAC;AAAA,IACN;AAAA,KACC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AAAA,EAC/C;AACJ;AAEA,IAAM,eAAe,MAAI;AACrB,SAAO,aAAa,mBAAe,0BAAW,gBAAgB,CAAC;AACnE;AAEA,IAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,QAAM,CAAC,KAAK,KAAK,IAAI,UAAU,IAAI;AACnC,QAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAEhD,MAAI,QAAQ,GAAG,EAAG,QAAO,QAAQ,GAAG;AACpC,QAAM,MAAM,QAAQ,KAAK;AACzB,UAAQ,GAAG,IAAI;AACf,SAAO;AACX;AACA,IAAM,aAAa,CAAC,eAAa,CAAC,MAAM,UAAU,WAAS;AAEnD,QAAM,UAAU,aAAa,IAAI,SAAO;AACpC,UAAM,CAAC,GAAG,IAAI,UAAU,IAAI;AAC5B,UAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAChD,QAAI,IAAI,WAAW,eAAe,GAAG;AAGjC,aAAO,SAAS,GAAG,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,YAAY,GAAG,EAAG,QAAO,SAAS,GAAG,IAAI;AAC7C,WAAO,QAAQ,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,WAAW,MAAM,SAAS,MAAM;AAC3C;AAEJ,IAAM,sBAAsB,IAAI,OAAO,UAAU;AAIjD,IAAM,WAAW,CAAC,SAAO;AACrB,SAAO,SAAS,cAAc,MAAM;AAEhC,UAAM,iBAAiB,aAAa;AAEpC,UAAM,CAAC,KAAK,IAAIC,QAAO,IAAI,UAAU,IAAI;AAEzC,UAAM,SAAS,aAAa,gBAAgBA,QAAO;AAEnD,QAAI,OAAO;AACX,UAAM,EAAE,KAAAC,KAAI,IAAI;AAChB,UAAMC,eAAcD,QAAO,CAAC,GAAG,OAAO,mBAAmB;AACzD,aAAQ,IAAIC,YAAW,QAAQ,OAAK;AAChC,aAAOA,YAAW,CAAC,EAAE,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,KAAK,MAAM,OAAO,WAAW,MAAM,MAAM;AAAA,EACzD;AACJ;AAIA,IAAM,oBAAoB,CAAC,KAAK,WAAW,aAAW;AAClD,QAAM,oBAAoB,UAAU,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC;AAC/D,oBAAkB,KAAK,QAAQ;AAC/B,SAAO,MAAI;AACP,UAAM,QAAQ,kBAAkB,QAAQ,QAAQ;AAChD,QAAI,SAAS,GAAG;AAEZ,wBAAkB,KAAK,IAAI,kBAAkB,kBAAkB,SAAS,CAAC;AACzE,wBAAkB,IAAI;AAAA,IAC1B;AAAA,EACJ;AACJ;AAGA,IAAM,iBAAiB,CAACC,SAAQD,gBAAa;AACzC,SAAO,IAAI,SAAO;AACd,UAAM,CAAC,KAAK,IAAI,MAAM,IAAI,UAAU,IAAI;AACxC,UAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,OAAOA,WAAU;AACjD,WAAOC,QAAO,KAAK,IAAI;AAAA,MACnB,GAAG;AAAA,MACH,KAAK;AAAA,IACT,CAAC;AAAA,EACL;AACJ;AAEA,cAAc;;;AL1Gd,IAAMC,QAAO,MAAI;AAAC;AAKlB,IAAMC;AAAA;AAAA,EAA8BD,MAAK;AAAA;AACzC,IAAME,UAAS;AACf,IAAMC,eAAc,CAAC,MAAI,MAAMF;AAC/B,IAAMG,cAAa,CAAC,MAAI,OAAO,KAAK;AAMpC,IAAMC,SAAQ,oBAAI,QAAQ;AAC1B,IAAMC,gBAAe,CAAC,OAAO,SAAOJ,QAAO,UAAU,SAAS,KAAK,KAAK,MAAM,WAAW,IAAI;AAE7F,IAAIK,WAAU;AASd,IAAMC,cAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,SAASF,cAAa,KAAK,MAAM;AACvC,QAAM,UAAUA,cAAa,KAAK,QAAQ;AAC1C,QAAM,gBAAgBA,cAAa,KAAK,QAAQ;AAChD,MAAI;AACJ,MAAI;AACJ,MAAIJ,QAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,SAAS;AAG5C,aAASG,OAAM,IAAI,GAAG;AACtB,QAAI,OAAQ,QAAO;AAInB,aAAS,EAAEE,WAAU;AACrB,IAAAF,OAAM,IAAI,KAAK,MAAM;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAUG,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,MAAAH,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AAEf,eAAS;AACT,YAAM,OAAOH,QAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAACC,aAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAACA,aAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAMK,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,MAAAH,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAEA,IAAMI,aAAY,CAAC,QAAM;AACrB,MAAIL,YAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAOI,YAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,qBAAqB,CAAC,QAAMC,WAAU,GAAG,EAAE,CAAC;AAGlD,IAAMC,OAAM,cAAAC,QAAM;AAAA;AAAA;AAAA;AAAA,CAIjB,CAAC,aAAW;AACT,UAAO,SAAS,QAAO;AAAA,IACnB,KAAK;AACD,YAAM;AAAA,IACV,KAAK;AACD,aAAO,SAAS;AAAA,IACpB,KAAK;AACD,YAAM,SAAS;AAAA,IACnB;AACI,eAAS,SAAS;AAClB,eAAS,KAAK,CAAC,MAAI;AACf,iBAAS,SAAS;AAClB,iBAAS,QAAQ;AAAA,MACrB,GAAG,CAAC,MAAI;AACJ,iBAAS,SAAS;AAClB,iBAAS,SAAS;AAAA,MACtB,CAAC;AACD,YAAM;AAAA,EACd;AACJ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AACZ;AACA,IAAM,gBAAgB,CAAC,MAAM,SAAS,WAAS;AAC3C,QAAM,EAAE,OAAAC,QAAO,SAAAC,UAAS,UAAU,cAAc,mBAAmB,mBAAmB,iBAAiB,mBAAmB,oBAAoB,iBAAiB,IAAI;AACnK,QAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAID,MAAK;AAK/E,QAAM,CAAC,KAAK,KAAK,IAAI,UAAY,IAAI;AAErC,QAAM,wBAAoB,sBAAO,KAAK;AAGtC,QAAM,mBAAe,sBAAO,KAAK;AAEjC,QAAM,aAAS,sBAAO,GAAG;AACzB,QAAM,iBAAa,sBAAO,OAAO;AACjC,QAAM,gBAAY,sBAAO,MAAM;AAC/B,QAAM,YAAY,MAAI,UAAU;AAChC,QAAM,WAAW,MAAI,UAAU,EAAE,UAAU,KAAK,UAAU,EAAE,SAAS;AACrE,QAAM,CAAC,UAAU,UAAU,gBAAgB,eAAe,IAAI,kBAAkBA,QAAO,GAAG;AAC1F,QAAM,wBAAoB,sBAAO,CAAC,CAAC,EAAE;AAGrC,QAAM,WAAW,YAAc,YAAY,IAAI,YAAc,OAAO,QAAQ,IAAI,YAAc,OAAO,SAAS,GAAG,IAAI;AACrH,QAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,eAAU,KAAK,mBAAkB;AAC7B,YAAM,IAAI;AACV,UAAI,MAAM,QAAQ;AACd,YAAI,CAACC,SAAQ,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG;AAC/B,cAAI,CAAC,YAAc,KAAK,CAAC,CAAC,GAAG;AACzB,mBAAO;AAAA,UACX;AACA,cAAI,CAACA,SAAQ,cAAc,QAAQ,CAAC,CAAC,GAAG;AACpC,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,YAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG;AACxB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,kBAAc,uBAAQ,MAAI;AAC5B,UAAM,sBAAsB,MAAI;AAC5B,UAAI,CAAC,IAAK,QAAO;AACjB,UAAI,CAAC,QAAS,QAAO;AAErB,UAAI,CAAC,YAAc,iBAAiB,EAAG,QAAO;AAE9C,UAAI,UAAU,EAAE,SAAS,EAAG,QAAO;AACnC,UAAI,SAAU,QAAO;AACrB,aAAO,sBAAsB;AAAA,IACjC,GAAG;AAEH,UAAM,mBAAmB,CAAC,UAAQ;AAE9B,YAAM,WAAW,aAAa,KAAK;AACnC,aAAO,SAAS;AAChB,UAAI,CAAC,oBAAoB;AACrB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,cAAc;AAAA,QACd,WAAW;AAAA,QACX,GAAG;AAAA,MACP;AAAA,IACJ;AACA,UAAMC,cAAa,SAAS;AAC5B,UAAM,cAAc,gBAAgB;AACpC,UAAM,iBAAiB,iBAAiBA,WAAU;AAClD,UAAM,iBAAiBA,gBAAe,cAAc,iBAAiB,iBAAiB,WAAW;AAIjG,QAAI,oBAAoB;AACxB,WAAO;AAAA,MACH,MAAI;AACA,cAAM,cAAc,iBAAiB,SAAS,CAAC;AAC/C,cAAM,gBAAgB,QAAQ,aAAa,iBAAiB;AAC5D,YAAI,eAAe;AAWf,4BAAkB,OAAO,YAAY;AACrC,4BAAkB,YAAY,YAAY;AAC1C,4BAAkB,eAAe,YAAY;AAC7C,4BAAkB,QAAQ,YAAY;AACtC,iBAAO;AAAA,QACX,OAAO;AACH,8BAAoB;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAI;AAAA,IACR;AAAA,EAEJ,GAAG;AAAA,IACCF;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,aAAS,sCAAqB;AAAA,IAAY,CAAC,aAAW,eAAe,KAAK,CAAC,SAAS,SAAO;AACzF,UAAI,CAAC,QAAQ,MAAM,OAAO,EAAG,UAAS;AAAA,IAC1C,CAAC;AAAA;AAAA,IACL;AAAA,MACIA;AAAA,MACA;AAAA,IACJ;AAAA,EAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAClC,QAAM,iBAAiB,CAAC,kBAAkB;AAC1C,QAAM,iBAAiB,mBAAmB,GAAG,KAAK,mBAAmB,GAAG,EAAE,SAAS;AACnF,QAAM,aAAa,OAAO;AAC1B,QAAM,OAAO,YAAc,UAAU,IAAI,YAAY,cAAc,QAAQ,IAAIF,KAAI,QAAQ,IAAI,WAAW;AAC1G,QAAM,QAAQ,OAAO;AAErB,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,eAAe,mBAAmB,YAAc,UAAU,IAAI,YAAc,aAAa,OAAO,IAAI,OAAO,aAAa,UAAU,aAAa;AAIrJ,QAAM,+BAA+B,MAAI;AAErC,QAAI,kBAAkB,CAAC,YAAc,KAAK,EAAG,QAAO;AAEpD,QAAI,kBAAkB,CAAC,YAAc,iBAAiB,EAAG,QAAO;AAEhE,QAAI,UAAU,EAAE,SAAS,EAAG,QAAO;AAInC,QAAI,SAAU,QAAO,YAAc,IAAI,IAAI,QAAQ;AAGnD,WAAO,YAAc,IAAI,KAAK;AAAA,EAClC,GAAG;AAGH,QAAM,yBAAyB,CAAC,EAAE,OAAO,WAAW,kBAAkB;AACtE,QAAM,eAAe,YAAc,OAAO,YAAY,IAAI,yBAAyB,OAAO;AAC1F,QAAM,YAAY,YAAc,OAAO,SAAS,IAAI,yBAAyB,OAAO;AAGpF,QAAM,iBAAa;AAAA,IAAY,OAAO,mBAAiB;AACnD,YAAM,iBAAiB,WAAW;AAClC,UAAI,CAAC,OAAO,CAAC,kBAAkB,aAAa,WAAW,UAAU,EAAE,SAAS,GAAG;AAC3E,eAAO;AAAA,MACX;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU;AACd,YAAM,OAAO,kBAAkB,CAAC;AAGhC,YAAM,wBAAwB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AAWlD,YAAM,oBAAoB,MAAI;AAC3B,YAAI,iBAAiB;AACjB,iBAAO,CAAC,aAAa,WAAW,QAAQ,OAAO,WAAW,kBAAkB;AAAA,QAChF;AACA,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAEA,YAAM,aAAa;AAAA,QACf,cAAc;AAAA,QACd,WAAW;AAAA,MACf;AACA,YAAM,8BAA8B,MAAI;AACpC,iBAAS,UAAU;AAAA,MACvB;AACA,YAAM,eAAe,MAAI;AAErB,cAAM,cAAc,MAAM,GAAG;AAC7B,YAAI,eAAe,YAAY,CAAC,MAAM,SAAS;AAC3C,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,MACJ;AAEA,YAAM,eAAe;AAAA,QACjB,cAAc;AAAA,MAClB;AAGA,UAAI,YAAc,SAAS,EAAE,IAAI,GAAG;AAChC,qBAAa,YAAY;AAAA,MAC7B;AACA,UAAI;AACA,YAAI,uBAAuB;AACvB,mBAAS,YAAY;AAGrB,cAAI,OAAO,kBAAkB,YAAc,SAAS,EAAE,IAAI,GAAG;AACzD,uBAAW,MAAI;AACX,kBAAI,WAAW,kBAAkB,GAAG;AAChC,0BAAU,EAAE,cAAc,KAAK,MAAM;AAAA,cACzC;AAAA,YACJ,GAAG,OAAO,cAAc;AAAA,UAC5B;AAGA,gBAAM,GAAG,IAAI;AAAA,YACT,eAAe,KAAK;AAAA,YACpB,aAAa;AAAA,UACjB;AAAA,QACJ;AAGA;AACA,SAAC,SAAS,OAAO,IAAI,MAAM,GAAG;AAC9B,kBAAU,MAAM;AAChB,YAAI,uBAAuB;AAGvB,qBAAW,cAAc,OAAO,gBAAgB;AAAA,QACpD;AAOA,YAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE,CAAC,MAAM,SAAS;AAC1C,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,mBAAW,QAAQ;AAanB,cAAM,eAAe,SAAS,GAAG;AACjC,YAAI,CAAC,YAAc,YAAY;AAAA,SAC9B,WAAW,aAAa,CAAC;AAAA,QAC1B,WAAW,aAAa,CAAC;AAAA,QACzB,aAAa,CAAC,MAAM,IAAI;AACpB,sCAA4B;AAC5B,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAGA,cAAM,YAAY,SAAS,EAAE;AAG7B,mBAAW,OAAOG,SAAQ,WAAW,OAAO,IAAI,YAAY;AAE5D,YAAI,uBAAuB;AACvB,cAAI,kBAAkB,GAAG;AACrB,sBAAU,EAAE,UAAU,SAAS,KAAK,MAAM;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ,SAAS,KAAK;AACV,qBAAa;AACb,cAAM,gBAAgB,UAAU;AAChC,cAAM,EAAE,mBAAmB,IAAI;AAE/B,YAAI,CAAC,cAAc,SAAS,GAAG;AAE3B,qBAAW,QAAQ;AAGnB,cAAI,yBAAyB,kBAAkB,GAAG;AAC9C,0BAAc,QAAQ,KAAK,KAAK,aAAa;AAC7C,gBAAI,uBAAuB,QAAQ,WAAa,kBAAkB,KAAK,mBAAmB,GAAG,GAAG;AAC5F,kBAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,UAAU,EAAE,yBAAyB,SAAS,GAAG;AAIpF,8BAAc,aAAa,KAAK,KAAK,eAAe,CAAC,UAAQ;AACzD,wBAAM,eAAe,mBAAmB,GAAG;AAC3C,sBAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iCAAa,CAAC,EAAE,eAAiB,wBAAwB,KAAK;AAAA,kBAClE;AAAA,gBACJ,GAAG;AAAA,kBACC,aAAa,KAAK,cAAc,KAAK;AAAA,kBACrC,QAAQ;AAAA,gBACZ,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,gBAAU;AAEV,kCAA4B;AAC5B,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA;AAAA,MACI;AAAA,MACAD;AAAA,IACJ;AAAA,EAAC;AAGD,QAAM,kBAAc;AAAA;AAAA,IACpB,IAAI,SAAO;AACP,aAAO,eAAeA,QAAO,OAAO,SAAS,GAAG,IAAI;AAAA,IACxD;AAAA;AAAA,IACA,CAAC;AAAA,EAAC;AAEF,4BAA0B,MAAI;AAC1B,eAAW,UAAU;AACrB,cAAU,UAAU;AAGpB,QAAI,CAAC,YAAc,UAAU,GAAG;AAC5B,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI,CAAC,IAAK;AACV,UAAM,iBAAiB,WAAW,KAAK,WAAa,WAAW;AAC/D,QAAI,yBAAyB;AAC7B,QAAI,UAAU,EAAE,mBAAmB;AAC/B,YAAM,UAAU,KAAK,IAAI;AACzB,+BAAyB,UAAU,UAAU,EAAE;AAAA,IACnD;AAGA,UAAM,eAAe,CAAC,MAAM,OAAO,CAAC,MAAI;AACpC,UAAI,QAAQ,eAAiB,aAAa;AACtC,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,UAAU,EAAE,qBAAqB,MAAM,0BAA0B,SAAS,GAAG;AAC7E,mCAAyB,MAAM,UAAU,EAAE;AAC3C,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,eAAiB,iBAAiB;AACjD,YAAI,UAAU,EAAE,yBAAyB,SAAS,GAAG;AACjD,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,eAAiB,cAAc;AAC9C,eAAO,WAAW;AAAA,MACtB,WAAW,QAAQ,eAAiB,wBAAwB;AACxD,eAAO,WAAW,IAAI;AAAA,MAC1B;AACA;AAAA,IACJ;AACA,UAAM,cAAc,kBAAkB,KAAK,oBAAoB,YAAY;AAE3E,iBAAa,UAAU;AACvB,WAAO,UAAU;AACjB,sBAAkB,UAAU;AAE5B,aAAS;AAAA,MACL,IAAI;AAAA,IACR,CAAC;AAED,QAAI,6BAA6B;AAC7B,UAAI,YAAc,IAAI,KAAK,WAAW;AAElC,uBAAe;AAAA,MACnB,OAAO;AAGH,YAAI,cAAc;AAAA,MACtB;AAAA,IACJ;AACA,WAAO,MAAI;AAEP,mBAAa,UAAU;AACvB,kBAAY;AAAA,IAChB;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI;AACJ,aAAS,OAAO;AAGZ,YAAM,WAAW,WAAa,eAAe,IAAI,gBAAgB,SAAS,EAAE,IAAI,IAAI;AAIpF,UAAI,YAAY,UAAU,IAAI;AAC1B,gBAAQ,WAAW,SAAS,QAAQ;AAAA,MACxC;AAAA,IACJ;AACA,aAAS,UAAU;AAGf,UAAI,CAAC,SAAS,EAAE,UAAU,qBAAqB,UAAU,EAAE,UAAU,OAAO,sBAAsB,UAAU,EAAE,SAAS,IAAI;AACvH,mBAAW,WAAW,EAAE,KAAK,IAAI;AAAA,MACrC,OAAO;AAEH,aAAK;AAAA,MACT;AAAA,IACJ;AACA,SAAK;AACL,WAAO,MAAI;AACP,UAAI,OAAO;AACP,qBAAa,KAAK;AAClB,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,mCAAc,YAAY;AAK1B,MAAI,YAAY,YAAc,IAAI,KAAK,KAAK;AAIxC,QAAI,CAAC,mBAAmB,WAAW;AAC/B,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AAEA,eAAW,UAAU;AACrB,cAAU,UAAU;AACpB,iBAAa,UAAU;AACvB,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,CAAC,YAAc,GAAG,GAAG;AACrB,YAAM,UAAU,YAAY,GAAG;AAC/B,MAAAF,KAAI,OAAO;AAAA,IACf;AACA,QAAI,YAAc,KAAK,GAAG;AACtB,YAAM,UAAU,WAAW,WAAW;AACtC,UAAI,CAAC,YAAc,YAAY,GAAG;AAC9B,gBAAQ,SAAS;AACjB,gBAAQ,QAAQ;AAAA,MACpB;AACA,MAAAA,KAAI,OAAO;AAAA,IACf,OAAO;AACH,YAAM;AAAA,IACV;AAAA,EACJ;AACA,QAAM,cAAc;AAAA,IAChB,QAAQ;AAAA,IACR,IAAI,OAAQ;AACR,wBAAkB,OAAO;AACzB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,QAAS;AACT,wBAAkB,QAAQ;AAC1B,aAAO;AAAA,IACX;AAAA,IACA,IAAI,eAAgB;AAChB,wBAAkB,eAAe;AACjC,aAAO;AAAA,IACX;AAAA,IACA,IAAI,YAAa;AACb,wBAAkB,YAAY;AAC9B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAMK,aAAY,OAAS,eAAe,WAAa,gBAAgB;AAAA,EACnE,OAAO;AACX,CAAC;AAeG,IAAM,SAAS,SAAS,aAAa;;;AMjoBzC,IAAAC,gBAAoC;AAGpC,IAAAC,eAAqC;AAIrC,IAAMC,QAAO,MAAI;AAAC;AAKlB,IAAMC;AAAA;AAAA,EAA8BD,MAAK;AAAA;AACzC,IAAME,UAAS;AACf,IAAMC,eAAc,CAAC,MAAI,MAAMF;AAC/B,IAAMG,cAAa,CAAC,MAAI,OAAO,KAAK;AAMpC,IAAMC,SAAQ,oBAAI,QAAQ;AAC1B,IAAMC,gBAAe,CAAC,OAAO,SAAOJ,QAAO,UAAU,SAAS,KAAK,KAAK,MAAM,WAAW,IAAI;AAE7F,IAAIK,WAAU;AASd,IAAMC,cAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,SAASF,cAAa,KAAK,MAAM;AACvC,QAAM,UAAUA,cAAa,KAAK,QAAQ;AAC1C,QAAM,gBAAgBA,cAAa,KAAK,QAAQ;AAChD,MAAI;AACJ,MAAI;AACJ,MAAIJ,QAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,SAAS;AAG5C,aAASG,OAAM,IAAI,GAAG;AACtB,QAAI,OAAQ,QAAO;AAInB,aAAS,EAAEE,WAAU;AACrB,IAAAF,OAAM,IAAI,KAAK,MAAM;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAUG,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,MAAAH,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AAEf,eAAS;AACT,YAAM,OAAOH,QAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAACC,aAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAACA,aAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAMK,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,MAAAH,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAEA,IAAMI,aAAY,CAAC,QAAM;AACrB,MAAIL,YAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAOI,YAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,kBAAkB,CAAC,WAAS;AAC9B,SAAOC,WAAU,SAAS,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;AACvD;AAOA,IAAM,gBAAgB,QAAQ,QAAQ;AACtC,IAAM,WAAW,CAAC,eAAa,CAAC,QAAQ,IAAI,WAAS;AAC7C,QAAM,kBAAc,sBAAO,KAAK;AAChC,QAAM,EAAE,OAAO,SAAS,cAAc,GAAG,gBAAgB,OAAO,cAAc,OAAO,sBAAsB,MAAM,oBAAoB,OAAO,WAAW,MAAM,IAAI;AACjK,QAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAGhD,MAAI;AACJ,MAAI;AACA,kBAAc,gBAAgB,MAAM;AACpC,QAAI,YAAa,eAAc,kBAAoB;AAAA,EACvD,SAAS,KAAK;AAAA,EAEd;AACA,QAAM,CAAC,KAAK,KAAK,cAAc,IAAI,kBAAkB,SAAS,WAAW;AACzE,QAAM,kBAAc,2BAAY,MAAI;AAChC,UAAM,OAAO,YAAc,IAAI,EAAE,EAAE,IAAI,cAAc,IAAI,EAAE;AAC3D,WAAO;AAAA,EAEX,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,6CAAqB;AAAA,IAAY,CAAC,aAAW;AACzC,UAAI,YAAa,QAAO,eAAe,aAAa,MAAI;AACpD,iBAAS;AAAA,MACb,CAAC;AACD,aAAO,MAAI;AAAA,MAAC;AAAA,IAChB;AAAA;AAAA,IACA;AAAA,MACI;AAAA,MACA;AAAA,IACJ;AAAA,EAAC,GAAG,aAAa,WAAW;AAC5B,QAAM,sBAAkB,2BAAY,MAAI;AACpC,UAAM,iBAAiB,IAAI,EAAE;AAC7B,WAAO,YAAc,cAAc,IAAI,cAAc;AAAA,EAGzD,GAAG;AAAA,IACC;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,sBAAkB,sBAAO,gBAAgB,CAAC;AAEhD,4BAA0B,MAAI;AAC1B,QAAI,CAAC,YAAY,SAAS;AACtB,kBAAY,UAAU;AACtB;AAAA,IACJ;AACA,QAAI,aAAa;AAGb,UAAI;AAAA,QACA,IAAI,cAAc,gBAAgB,UAAU,gBAAgB;AAAA,MAChE,CAAC;AAAA,IACL;AAAA,EAGJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,0BAA0B,qBAAqB,CAAC,YAAY;AAElE,QAAM,MAAM,WAAW,aAAa,OAAO,QAAM;AAE7C,UAAM,qBAAqB,IAAI,EAAE;AACjC,UAAM,uBAAuB,IAAI,EAAE;AACnC,QAAI;AAAA,MACA,IAAI;AAAA,IACR,CAAC;AAED,UAAM,OAAO,CAAC;AACd,UAAM,WAAW,gBAAgB;AACjC,UAAM,CAAC,QAAQ,IAAI,kBAAkB,SAAS,GAAG;AACjD,UAAM,YAAY,SAAS,EAAE;AAC7B,UAAM,eAAe,CAAC;AACtB,QAAI,mBAAmB;AACvB,aAAQ,IAAI,GAAG,IAAI,UAAU,EAAE,GAAE;AAC7B,YAAM,CAAC,SAAS,OAAO,IAAI,UAAY,OAAO,GAAG,WAAW,OAAO,gBAAgB,CAAC;AACpF,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,YAAM,CAAC,aAAa,WAAW,IAAI,kBAAkB,SAAS,OAAO;AAErE,UAAI,WAAW,YAAY,EAAE;AAQ7B,YAAM,kBAAkB,iBAAiB,sBAAsB,YAAc,QAAQ,KAAK,uBAAuB,CAAC,KAAK,CAAC,YAAc,SAAS,KAAK,2BAA2B,aAAa,CAAC,YAAc,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,QAAQ,UAAU,CAAC,GAAG,QAAQ;AAClQ,UAAI,OAAO,OAAO,yBAAyB,aAAa,qBAAqB,UAAU,OAAO,IAAI,kBAAkB;AAChH,cAAM,aAAa,YAAU;AACzB,gBAAM,sBAAsB,WAAW;AACvC,cAAI,CAAC,qBAAqB;AACtB,uBAAW,MAAM,GAAG,OAAO;AAAA,UAC/B,OAAO;AACH,kBAAM,MAAM,QAAQ,OAAO;AAG3B,mBAAO,QAAQ,OAAO;AAEtB,uBAAW,MAAM;AAAA,UACrB;AACA,sBAAY;AAAA,YACR,MAAM;AAAA,YACN,IAAI;AAAA,UACR,CAAC;AACD,eAAK,CAAC,IAAI;AAAA,QACd;AACA,YAAI,UAAU;AACV,uBAAa,KAAK,UAAU;AAAA,QAChC,OAAO;AACH,gBAAM,WAAW;AAAA,QACrB;AAAA,MACJ,OAAO;AACH,aAAK,CAAC,IAAI;AAAA,MACd;AACA,UAAI,CAAC,UAAU;AACX,2BAAmB;AAAA,MACvB;AAAA,IACJ;AAEA,QAAI,UAAU;AACV,YAAM,QAAQ,IAAI,aAAa,IAAI,CAAC,MAAI,EAAE,CAAC,CAAC;AAAA,IAChD;AAEA,QAAI;AAAA,MACA,IAAI;AAAA,IACR,CAAC;AAED,WAAO;AAAA,EACX,GAAG,MAAM;AACT,QAAMC,cAAS;AAAA;AAAA,IACf,SAAS,MAAM,MAAM;AAGjB,YAAM,UAAU,OAAO,SAAS,YAAY;AAAA,QACxC,YAAY;AAAA,MAChB,IAAI,QAAQ,CAAC;AAEb,YAAM,mBAAmB,QAAQ,eAAe;AAEhD,UAAI,CAAC,YAAa,QAAO;AACzB,UAAI,kBAAkB;AAClB,YAAI,CAAC,YAAc,IAAI,GAAG;AAEtB,cAAI;AAAA,YACA,IAAI;AAAA,YACJ,IAAI,QAAQ;AAAA,UAChB,CAAC;AAAA,QACL,OAAO;AAEH,cAAI;AAAA,YACA,IAAI;AAAA,YACJ,IAAI,QAAQ;AAAA,UAChB,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO,UAAU,SAAS,IAAI,OAAO,MAAM;AAAA,QACvC,GAAG;AAAA,QACH,YAAY;AAAA,MAChB,CAAC,IAAI,IAAI,OAAO;AAAA,IACpB;AAAA;AAAA;AAAA,IAEA;AAAA,MACI;AAAA,MACA;AAAA,IACJ;AAAA,EAAC;AAED,QAAM,cAAU;AAAA,IAAY,CAAC,QAAM;AAE/B,UAAI,CAAC,YAAa,QAAO;AACzB,YAAM,CAAC,EAAE,UAAU,IAAI,kBAAkB,SAAS,WAAW;AAC7D,UAAI;AACJ,UAAI,WAAa,GAAG,GAAG;AACnB,eAAO,IAAI,gBAAgB,CAAC;AAAA,MAChC,WAAW,OAAO,OAAO,UAAU;AAC/B,eAAO;AAAA,MACX;AACA,UAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,iBAAW;AAAA,QACP,IAAI;AAAA,MACR,CAAC;AACD,sBAAgB,UAAU;AAE1B,YAAM,OAAO,CAAC;AACd,YAAM,CAAC,gBAAgB,IAAI,kBAAkB,SAAS,WAAW;AACjE,UAAI,mBAAmB;AACvB,eAAQ,IAAI,GAAG,IAAI,MAAM,EAAE,GAAE;AACzB,cAAM,CAAC,OAAO,IAAI,UAAY,OAAO,GAAG,gBAAgB,CAAC;AACzD,cAAM,CAAC,QAAQ,IAAI,kBAAkB,SAAS,OAAO;AAErD,cAAM,WAAW,UAAU,SAAS,EAAE,OAAO;AAE7C,YAAI,YAAc,QAAQ,GAAG;AACzB,iBAAOA,QAAO,iBAAiB,EAAE,IAAI;AAAA,QACzC;AACA,aAAK,KAAK,QAAQ;AAClB,2BAAmB;AAAA,MACvB;AACA,aAAOA,QAAO,IAAI;AAAA,IACtB;AAAA;AAAA;AAAA,IAEA;AAAA,MACI;AAAA,MACA;AAAA,MACAA;AAAA,MACA;AAAA,IACJ;AAAA,EAAC;AAGD,SAAO;AAAA,IACH,MAAM,gBAAgB;AAAA,IACtB;AAAA,IACA,QAAAA;AAAA,IACA,IAAI,OAAQ;AACR,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,QAAS;AACT,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,eAAgB;AAChB,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,YAAa;AACb,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AACJ;AACJ,IAAM,iBAAiB,eAAe,QAAQ,QAAQ;;;ArBlVtD,IAAAC,gBAAuD;AGFvD,IAAAA,gBAAkB;;;AmBAlB,IAAIC,OAAM,OAAO,UAAU;AAE3B,SAAS,KAAK,MAAM,KAAK,KAAK;AAC7B,OAAK,OAAO,KAAK,KAAK,GAAG;AACxB,QAAIC,QAAO,KAAK,GAAG,EAAG,QAAO;AAAA,EAC9B;AACD;AAEO,SAASA,QAAO,KAAK,KAAK;AAChC,MAAI,MAAM,KAAK;AACf,MAAI,QAAQ,IAAK,QAAO;AAExB,MAAI,OAAO,QAAQ,OAAK,IAAI,iBAAiB,IAAI,aAAa;AAC7D,QAAI,SAAS,KAAM,QAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ;AACxD,QAAI,SAAS,OAAQ,QAAO,IAAI,SAAS,MAAM,IAAI,SAAS;AAE5D,QAAI,SAAS,OAAO;AACnB,WAAK,MAAI,IAAI,YAAY,IAAI,QAAQ;AACpC,eAAO,SAASA,QAAO,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,MAC5C;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,SAAS,KAAK;AACjB,UAAI,IAAI,SAAS,IAAI,MAAM;AAC1B,eAAO;AAAA,MACR;AACA,WAAK,OAAO,KAAK;AAChB,cAAM;AACN,YAAI,OAAO,OAAO,QAAQ,UAAU;AACnC,gBAAM,KAAK,KAAK,GAAG;AACnB,cAAI,CAAC,IAAK,QAAO;AAAA,QAClB;AACA,YAAI,CAAC,IAAI,IAAI,GAAG,EAAG,QAAO;AAAA,MAC3B;AACA,aAAO;AAAA,IACR;AAEA,QAAI,SAAS,KAAK;AACjB,UAAI,IAAI,SAAS,IAAI,MAAM;AAC1B,eAAO;AAAA,MACR;AACA,WAAK,OAAO,KAAK;AAChB,cAAM,IAAI,CAAC;AACX,YAAI,OAAO,OAAO,QAAQ,UAAU;AACnC,gBAAM,KAAK,KAAK,GAAG;AACnB,cAAI,CAAC,IAAK,QAAO;AAAA,QAClB;AACA,YAAI,CAACA,QAAO,IAAI,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG;AAClC,iBAAO;AAAA,QACR;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,SAAS,aAAa;AACzB,YAAM,IAAI,WAAW,GAAG;AACxB,YAAM,IAAI,WAAW,GAAG;AAAA,IACzB,WAAW,SAAS,UAAU;AAC7B,WAAK,MAAI,IAAI,gBAAgB,IAAI,YAAY;AAC5C,eAAO,SAAS,IAAI,QAAQ,GAAG,MAAM,IAAI,QAAQ,GAAG,EAAE;AAAA,MACvD;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,YAAY,OAAO,GAAG,GAAG;AAC5B,WAAK,MAAI,IAAI,gBAAgB,IAAI,YAAY;AAC5C,eAAO,SAAS,IAAI,GAAG,MAAM,IAAI,GAAG,EAAE;AAAA,MACvC;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;AACrC,YAAM;AACN,WAAK,QAAQ,KAAK;AACjB,YAAID,KAAI,KAAK,KAAK,IAAI,KAAK,EAAE,OAAO,CAACA,KAAI,KAAK,KAAK,IAAI,EAAG,QAAO;AACjE,YAAI,EAAE,QAAQ,QAAQ,CAACC,QAAO,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAG,QAAO;AAAA,MAC7D;AACA,aAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,IACpC;AAAA,EACD;AAEA,SAAO,QAAQ,OAAO,QAAQ;AAC/B;;;AdlFA,IAAAC,gBAAkB;ACAlB,IAAAA,iBAAoC;AZO7B,SAAS,oBAAoB,YAAqB,UAA2D;AAClH,MAAI,CAAC,YAAY;AACf,UAAM,OAAO,aAAa,WAAW,IAAI,MAAM,QAAQ,IAAI,IAAI,MAAM,GAAG,SAAS,WAAW,YAAY;EAC1G;AACF;AAeO,IAAM,uBAAuB,CAClC,aACA,YAC8E;AAC9E,QAAM,EAAE,cAAc,oBAAoB,IAAI,WAAW,CAAC;AAC1D,QAAM,MAAM,cAAAC,QAAM,cAA6C,MAAS;AACxE,MAAI,cAAc;AAElB,QAAM,SAAS,MAAM;AACnB,UAAM,MAAM,cAAAA,QAAM,WAAW,GAAG;AAChC,gBAAY,KAAK,GAAG,WAAW,YAAY;AAC3C,WAAQ,IAAY;EACtB;AAEA,QAAM,yBAAyB,MAAM;AACnC,UAAM,MAAM,cAAAA,QAAM,WAAW,GAAG;AAChC,WAAO,MAAM,IAAI,QAAQ,CAAC;EAC5B;AAEA,SAAO,CAAC,KAAK,QAAQ,sBAAsB;AAC7C;AE/CA,IAAA,oBAAA,CAAA;AAAAC,UAAA,mBAAA;EAAA,QAAA,MAAAC;EAAA,gBAAA,MAAAA;AAAA,CAAA;AAEA,WAAA,mBAAA,aAAA;ADcA,IAAM,CAAC,sBAAsB,uBAAuB,IAAI,qBAAkC,sBAAsB;AAChH,IAAM,CAAC,aAAa,cAAc,IAAI,qBAAsD,aAAa;AACzG,IAAM,CAAC,eAAe,gBAAgB,IAAI,qBAAwD,eAAe;AACjH,IAAM,CAAC,gBAAgB,iBAAiB,IAAI;EAC1C;AACF;AAEA,IAAM,iBAAiBF,cAAAA,QAAM,cAA4B,CAAC,CAAC;AAa3D,IAAM,CAAC,6BAA6B,sBAAsB,IAAI,qBAE3D,qBAAqB;AAExB,IAAM,uBAAuB,CAAC;EAC5B;EACA;EACA;AACF,MAKM;AACJ,SACEG,cAAAA,QAAA,cAAC,kBAAA,WAAA,EAAU,OAAO,UAAA,GAChBA,cAAAA,QAAA;IAAC,4BAA4B;IAA5B;MACC,OAAO;QACL,OAAO,EAAE,aAAa;MACxB;IAAA;IAEC;EACH,CACF;AAEJ;AAEA,SAAS,gCAAgC,iBAA8C;AACrF,QAAM,MAAMA,cAAAA,QAAM,WAAW,oBAAoB;AAEjD,MAAI,CAAC,KAAK;AACR,QAAI,OAAO,oBAAoB,YAAY;AACzC,sBAAgB;AAChB;IACF;AAEA,UAAM,IAAI;MACR,GAAG,eAAe;;;;;;8DAMsC,KAAK;IAC/D;EACF;AACF;AElDA,SAAS,iBAAiB,MAA+B,MAAwD;AAC/G,QAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC;AACzC,QAAM,sBAA+C,CAAC;AAEtD,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AACpC,QAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AACtB,0BAAoB,IAAI,IAAI,KAAK,IAAI;IACvC;EACF;AAEA,SAAO;AACT;AA6BO,IAAM,oBAAoB,CAAmC,QAA8B,kBAAqB;AACrH,QAAM,oBAAoB,OAAO,WAAW,aAAa;AAGzD,QAAM,qBAAiB;IACrB,oBAAoB,cAAc,eAAe,iCAAQ,gBAAe,cAAc;EACxF;AACA,QAAM,kBAAc,sBAAO,oBAAoB,cAAc,YAAY,iCAAQ,aAAY,cAAc,QAAS;AAEpH,QAAM,SAAkC,CAAC;AACzC,aAAW,OAAO,OAAO,KAAK,aAAa,GAAG;AAE5C,WAAO,GAAG,IAAI,oBAAoB,cAAc,GAAG,KAAK,iCAAS,SAAQ,cAAc,GAAG;EAC5F;AAEA,SAAO;IACL,GAAG;IACH,aAAa,eAAe;IAC5B,UAAU,YAAY;EACxB;AACF;AAEA,IAAM,oBAAoB;EACxB,kBAAkB,MAAO;EACzB,uBAAuB,MAAO,KAAK;AACrC;AA0CO,IAAM,qBAAyC,CAAC,QAAQ,SAAS,QAAQ,cAAc;AAC5F,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS,OAAO,eAAe,CAAC;AAG1E,QAAM,qBAAiB,sBAAO,OAAO,eAAe,CAAC;AACrD,QAAM,kBAAc,sBAAO,OAAO,YAAY,EAAE;AAEhD,QAAM,UAAU,OAAO,WAAW;AAClC,QAAM,YAAY,OAAO,wBAAwB;AACjD,QAAM,kBAAkB,OAAO,YAAY;AAC3C,QAAM,mBAAmB,OAAO,oBAAoB;AAEpD,QAAM,gBAAgB;IACpB,GAAG;IACH,GAAG;IACH,aAAa;IACb,UAAU,YAAY;EACxB;AAIA,QAAM,cAAc,CAAC,mBAAmB,YAAY,CAAC,YAAY,CAAC,CAAC,UAAU;AAC7E,QAAM,SAAS,cAAc,gBAAgB;AAC7C,QAAM,aACJ,CAAC,aAAa,CAAC,CAAC,UACZ,CAAC,mBAA4C;AAC3C,UAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAChE,WAAO,QAAQ,EAAE,GAAG,QAAQ,GAAG,cAAc,CAAC;EAChD,IACA;AAEN,QAAM;IACJ,MAAM;IACN,cAAc;IACd,WAAW;IACX,OAAO;IACP,QAAQ;EACV,IAAIC,OAAO,QAAQ,YAAY,EAAE,kBAAkB,GAAG,kBAAkB,CAAC;AAEzE,QAAM;IACJ,MAAM;IACN,WAAW;IACX,cAAc;IACd,OAAO;IACP;IACA;IACA,QAAQ;EACV,IAAIA;IACF,CAAA,cAAa;AACX,UAAI,CAAC,mBAAmB,CAAC,SAAS;AAChC,eAAO;MACT;AAEA,aAAO;QACL,GAAG;QACH,GAAG;QACH,aAAa,eAAe,UAAU;QACtC,UAAU,YAAY;MACxB;IACF;IACA,CAAA,mBAAkB;AAEhB,YAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAEhE,aAAO,mCAAU;IACnB;IACA;EACF;AAEA,QAAM,WAAO,uBAAQ,MAAM;AACzB,QAAI,iBAAiB;AACnB,aAAO;IACT;AACA,WAAO;EACT,GAAG,CAAC,iBAAiB,MAAM,aAAa,CAAC;AAEzC,QAAM,gBAAmC;IACvC,CAAA,gBAAe;AACb,UAAI,iBAAiB;AACnB,aAAK,QAAQ,WAAW;AACxB;MACF;AACA,aAAO,iBAAiB,WAAW;IACrC;IACA,CAAC,OAAO;EACV;AAEA,QAAM,WAAO,uBAAQ,MAAM;AACzB,QAAI,iBAAiB;AACnB,cAAO,mDAAiB,IAAI,CAAA,MAAK,uBAAG,MAAM,WAAU,CAAC;IACvD;AACA,YAAO,mCAAS,SAAQ,CAAC;EAC3B,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAQ,uBAAQ,MAAM;;AAC1B,QAAI,iBAAiB;AACnB,eAAO,yDAAkB,mDAAiB,UAAS,OAA5C,mBAAgD,gBAAe;IACxE;AACA,YAAO,mCAAS,gBAAe;EACjC,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAY,kBAAkB,uBAAuB;AAC3D,QAAM,aAAa,kBAAkB,0BAA0B;AAC/D,QAAM,SAAS,kBAAkB,mBAAmB,aAAa;AACjE,QAAM,UAAU,CAAC,CAAC;AAIlB,QAAM,gBAAY,2BAAY,MAAM;AAClC,cAAU,CAAA,MAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,oBAAgB,2BAAY,MAAM;AACtC,cAAU,CAAA,MAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,eAAe,eAAe,UAAU,KAAK,YAAY;AAE/D,QAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,YAAY,OAAO;AACvE,QAAM,cAAc,QAAQ,cAAc,YAAY,UAAU,OAAO,YAAY;AACnF,QAAM,mBAAmB,OAAO,KAAK,YAAY,UAAU,cAAc,YAAY;AAErF,QAAM,UAAuB,kBACzB,CAAA,UACE,kBAAkB,OAAO;IACvB,YAAY;EACd,CAAC,IACH,CAAA,UACE,UAAU,OAAO;IACf,YAAY;EACd,CAAC;AAEP,QAAM,aAAa,kBAAkB,MAAM,kBAAkB,IAAI,MAAM,UAAU;AAEjF,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IAEA;EACF;AACF;AC3IA,IAAM,6BAA6B;EACjC,MAAM;EACN,OAAO;EACP,OAAO;EACP,WAAW;EACX,YAAY;EACZ,SAAS;EACT,MAAM;EACN,WAAW;EACX,WAAW;EACX,WAAW;EACX,eAAe;EACf,aAAa;EACb,iBAAiB;EACjB,YAAY;EACZ,SAAS;AACX;AA6HO,SAAS,gBAAiD,QAAsC;;AACrG,QAAM;IACJ,SAAS;IACT,oBAAoB;IACpB,aAAa;IACb,aAAa;IACb,eAAe;EACjB,IAAI,UAAU,CAAC;AAEf,kCAAgC,iBAAiB;AAEjD,QAAM,EAAE,aAAa,IAAI,uBAAuB;AAChD,QAAM,UAAU,kBAAkB;AAElC,QAAM,mBAAmB,kBAAkB,kBAAkB;IAC3D,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,UAAU;IACV,gBAAgB;EAClB,CAAC;AAED,QAAM,8BAA8B,kBAAkB,8BAA8B;IAClF,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,oBAAoB,kBAAkB,mBAAmB;IAC7D,aAAa;IACb,UAAU;IACV,MAAM;IACN,kBAAkB;IAClB,UAAU;IACV,OAAO;EACT,CAAC;AAED,QAAM,wBAAwB,kBAAkB,uBAAuB;IACrE,aAAa;IACb,UAAU;IACV,QAAQ,CAAC,SAAS;IAClB,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,0BAA0B,kBAAkB,yBAAyB;IACzE,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AAEtC,cAAM,cAAN,mBAAiB,OAAO,kBAAkB,iBAAiB;AAE3D,QAAM,eACJ,OAAO,qBAAqB,cACxB,SACA;IACE,aAAa,iBAAiB;IAC9B,UAAU,iBAAiB;IAC3B,gBAAgB,iBAAiB;EACnC;AAEN,QAAM,0BACJ,OAAO,iCAAiC,cACpC,SACA;IACE,aAAa,4BAA4B;IACzC,UAAU,4BAA4B;IACtC,QAAQ,4BAA4B;EACtC;AAEN,QAAM,gBACJ,OAAO,sBAAsB,cACzB,SACA;IACE,aAAa,kBAAkB;IAC/B,UAAU,kBAAkB;IAC5B,MAAM,kBAAkB;IACxB,OAAO,kBAAkB;EAC3B;AAEN,QAAM,oBACJ,OAAO,0BAA0B,cAC7B,SACA;IACE,aAAa,sBAAsB;IACnC,UAAU,sBAAsB;IAChC,QAAQ,sBAAsB;EAChC;AAEN,QAAM,sBACJ,OAAO,4BAA4B,cAC/B,SACA;IACE,aAAa,wBAAwB;IACrC,UAAU,wBAAwB;IAClC,OAAO,6CAAc;EACvB;AAEN,QAAM,UAAU;IACd;MACE,GAAG;IACL;IACA,6CAAc;IACd;MACE,kBAAkB,iBAAiB;MACnC,UAAU,iBAAiB;MAC3B,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,6CAAc;IAChC;EACF;AAEA,QAAM,qBAAqB;IAIzB;MACE,GAAG;IACL;IACA,6CAAc;IACd;MACE,kBAAkB,4BAA4B;MAC9C,UAAU,4BAA4B;MACtC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,6CAAc;IAChC;EACF;AAEA,QAAM,cAAc;IAClB,iBAAiB,CAAC;IAClB,6CAAc;IACd;MACE,kBAAkB,kBAAkB;MACpC,UAAU,kBAAkB;MAC5B,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,6CAAc;IAChC;EACF;AAEA,QAAM,cAAc;IAClB;MACE,GAAG;IACL;IACA,6CAAc;IACd;MACE,kBAAkB,sBAAsB;MACxC,UAAU,sBAAsB;MAChC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,6CAAc;IAChC;EACF;AAEA,QAAM,gBAAgB;IAIpB;MACE,GAAG;IACL;IACA,6CAAc;IACd;MACE,kBAAkB,wBAAwB;MAC1C,UAAU,wBAAwB;MAClC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,6CAAc;IAChC;EACF;AAEA,MAAI,iBAAiB,QAAW;AAC9B,WAAO;MACL,UAAU;MACV,cAAc;MACd,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;MACb,eAAe;IACjB;EACF;AAEA,MAAI,iBAAiB,MAAM;AACzB,WAAO;MACL,UAAU;MACV,cAAc;MACd,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;MACb,eAAe;IACjB;EACF;AAGA,MAAI,CAAC,MAAM,UAAU,cAAc;AACjC,WAAO;MACL,UAAU;MACV;MACA,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;MACb,eAAe;IACjB;EACF;AAEA,SAAO;IACL,UAAU,MAAM;IAChB;;IAEA,YAAY,iCAAiC,QAAS,KAAK,yBAAyB,aAAa,EAAE;;IACnG;IACA;IACA;IACA;IACA;EACF;AACF;AC/dA,IAAMC,8BAA6B;EACjC,MAAM;EACN,OAAO;EACP,OAAO;EACP,WAAW;EACX,YAAY;EACZ,SAAS;EACT,MAAM;EACN,WAAW;EACX,WAAW;EACX,WAAW;EACX,eAAe;EACf,aAAa;EACb,iBAAiB;EACjB,YAAY;EACZ,SAAS;AACX;AAoLO,SAAS,oBAAyD,QAA0C;;AACjH,QAAM,EAAE,iBAAiB,iBAAiB,gBAAgB,IAAI,UAAU,CAAC;AAEzE,kCAAgC,qBAAqB;AAErD,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AACtC,QAAM,OAAO,eAAe;AAE5B,cAAM,cAAN,mBAAiB,OAAO,kBAAkB,qBAAqB;AAE/D,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;EACtC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;IACpC,QAAQ,0BAA0B;EACpC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;IACpC,QAAQ,0BAA0B;EACpC;AAEN,QAAM,gBAAgB,CAAC,EAAE,MAAM,UAAU;AAEzC,QAAM,cAAc;IAIlB,yBAAyB,CAAC;IAC1B,6BAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,6BAAM;IAChB;EACF;AAEA,QAAM,cAAc;IAIlB;MACE,GAAG;IACL;IACA,6BAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,6BAAM;IAChB;EACF;AAEA,QAAM,cAAc;IAIlB;MACE,GAAG;IACL;IACA,6BAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,6BAAM;IAChB;EACF;AAGA,MAAI,CAAC,eAAe;AAClB,WAAO;MACL,UAAU;MACV,oBAAoB;MACpB,WAAW;MACX,iBAAiBA;MACjB,iBAAiBA;MACjB,iBAAiBA;IACnB;EACF;AAEA,SAAO;IACL,UAAU;IACV,WAAW,MAAM;IACjB,oBAAoB,MAAM;IAC1B,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;EACnB;AACF;AC7XO,IAAM,sBAAsB,OAAO,WAAW,cAAcF,cAAAA,QAAM,kBAAkBA,cAAAA,QAAM;ACEjG,IAAM,WAAW;AAkDV,IAAM,aAAyB,CAAC,UAAU,CAAC,MAAM;;AACtD,kCAAgC,QAAQ;AAExC,QAAM,UAAU,kBAAkB;AAClC,QAAM,QAAQ,wBAAwB;AAEtC,cAAM,cAAN,mBAAiB,OAAO,kBAAkB,QAAQ;AAElD,MAAI,YAAY,QAAW;AACzB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,SAAS,OAAU;EACtE;AAEA,QAAM,sBACJ,mCAAS,YAAW,cACnB,QAAQ,2BAA2B,MAAM,qBAAqB,yBAAyB;AAC1F,QAAM,cAAc,YAAY,QAAQ;AACxC,MAAI,aAAa;AACf,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,SAAS,KAAK;EAC5D;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,QAAQ;AACrD;ACzEA,IAAMG,YAAW;AA4CV,IAAM,iBAAiB,MAA4B;;AACxD,kCAAgCA,SAAQ;AAExC,QAAM,kBAAkB,wBAAwB;AAChD,QAAM,SAAS,iBAAiB;AAChC,QAAM,QAAQ,wBAAwB;AAEtC,cAAM,cAAN,mBAAiB,OAAO,kBAAkBA,SAAQ;AAElD,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,UAAU,QAAW,WAAW,OAAU;EACtE;AAEA,SAAO;IACL,UAAU;IACV,UAAU,OAAO;IACjB,WAAW,gBAAgB;EAC7B;AACF;AC9DA,IAAMA,YAAW;AA4HV,SAAS,UAAyB;;AACvC,kCAAgCA,SAAQ;AAExC,QAAM,OAAO,eAAe;AAC5B,QAAM,QAAQ,wBAAwB;AAEtC,cAAM,cAAN,mBAAiB,OAAO,kBAAkBA,SAAQ;AAElD,MAAI,SAAS,QAAW;AACtB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,MAAM,OAAU;EACnE;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,MAAM,KAAK;EACzD;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,KAAK;AAClD;AC3GO,IAAM,WAAW,MAAmB;AACzC,kCAAgC,UAAU;AAC1C,SAAO,wBAAwB;AACjC;ACjBO,IAAM,gBAAgBC;ACd7B,IAAM,sCAAsC;AAE5C,eAAe,cAAiB,QAA6E;AAC3G,MAAI;AACF,UAAM,IAAI,MAAM;AAChB,QAAI,aAAa,UAAU;AACzB,aAAO,EAAE,KAAK;IAChB;AACA,WAAO;EACT,SAAS,GAAG;AAEV,QAAI,wBAAwB,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,mCAAmC,GAAG;AAC3G,aAAO,oBAAoB;IAC7B;AAGA,UAAM;EACR;AACF;AAoDA,SAAS,4BAA4B,QAA2C;AAC9E,WAAS,qBACP,SAC4F;AAC5F,WAAQ,UAAU,SAA8B;;AAC9C,UAAI,SAAS,MAAM,cAAc,QAAQ,GAAG,IAAI,CAAC;AAEjD,UAAI,qBAAqB,MAAM,GAAG;AAIhC,cAAM,YAAY,sBAAsB;AAExC,cAAM,kBAAkB,8BAA6B,YAAO,YAAY,aAAnB,mBAA6B,cAAc;AAEhG,cAAM,QAAQ,kBAAkB,gBAAgB,EAAE,QAAQ;AAE1D,cAAM,SAAS,MAAM;AACnB,oBAAU;YACR,IAAI,kBAAkB,yCAAyC;cAC7D,MAAM;YACR,CAAC;UACH;QACF;AAEA,cAAM,WAAW,MAAM;AACrB,oBAAU,QAAQ,IAAI;QACxB;AAEA,YAAI,OAAO,0BAA0B,QAAW;AAK9C,uBAAO,oBAAP,gCAAyB;YACvB;YACA,mBAAmB;YACnB,4BAA4B;UAC9B;QACF,OAAO;AACL,iBAAO,sBAAsB;YAC3B;YACA;YACA;UACF,CAAC;QACH;AAKA,cAAM,UAAU;AAKhB,iBAAS,MAAM,cAAc,QAAQ,GAAG,IAAI,CAAC;MAC/C;AAEA,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAmDO,IAAM,oBAAuC,CAAC,SAAS,YAAY;AACxE,QAAM,EAAE,+BAA+B,UAAU,IAAI,SAAS;AAC9D,QAAM,iBAAaC,eAAAA,QAAO,OAAO;AACjC,QAAM,iBAAaA,eAAAA,QAAO,OAAO;AAEjC,yCAAW;IACT,kBAAkB,qBAAqB;MACrC,uBAAuB,QAAQ,mCAAS,qBAAqB;IAC/D,CAAC;;AAIH,sBAAoB,MAAM;AACxB,eAAW,UAAU;AACrB,eAAW,UAAU;EACvB,CAAC;AAED,aAAOC,eAAAA;IACL,IAAI,SAAS;AACX,YAAM,UAAU,4BAA4B;QAC1C,iBAAiB;QACjB;QACA,GAAG,WAAW;MAChB,CAAC,EAAE,WAAW,OAAO;AACrB,aAAO,QAAQ,GAAG,IAAI;IACxB;IACA,CAAC,+BAA+B,SAAS;EAC3C;AACF;AC1LO,SAAS,mBAA4F;EAC1G,UAAAC;EACA;EACA;AACF,GAA2C;AAKzC,SAAO,SAAS,gBACd,QAC4E;;AAC5E,UAAM,EAAE,KAAK,MAAM,GAAG,iBAAiB,IAAI;AAE3C,oCAAgCA,SAAQ;AAExC,UAAM,UAAU,WAAW,IAAI;AAE/B,UAAM,aAAa,kBAAkB,kBAAkB;MACrD,aAAa;MACb,UAAU;MACV,kBAAkB;MAClB,UAAU;MACV,qBAAqB;IACvB,CAAiB;AAEjB,UAAM,QAAQ,wBAAwB;AACtC,UAAM,OAAO,eAAe;AAC5B,UAAM,EAAE,aAAa,IAAI,uBAAuB;AAEhD,gBAAM,cAAN,mBAAiB,OAAO,kBAAkBA,SAAQ;AAElD,UAAM,aACJ,OAAO,qBAAqB,cACxB,SACC;MACC,aAAa,WAAW;MACxB,UAAU,WAAW;MACrB,GAAI,SAAS,iBAAiB,EAAE,OAAO,6CAAc,GAAG,IAAI,CAAC;IAC/D;AAEN,UAAM,gBAAgB,CAAC,EAAE,MAAM,UAAU;AAEzC,UAAM,YAAY,CAAC,CAAC,cAAc;AAElC,UAAM,SAAS;MACZ,cAAc,CAAC;MAChB;MACA;QACE,kBAAkB,WAAW;QAC7B,UAAU,WAAW;QACrB,SAAS;QACT,qBAAqB,WAAW;MAClC;MACA;QACE,MAAM;QACN,QAAQ,6BAAM;QACd,GAAI,SAAS,iBAAiB,EAAE,OAAO,6CAAc,GAAG,IAAI,CAAC;MAC/D;IACF;AAEA,WAAO;EACT;AACF;AC5FO,IAAM,gBAAgB,mBAAmE;EAC9F,UAAU;EACV,cAAc;EACd,YAAY,MAAM;AAChB,UAAM,QAAQ,wBAAwB;AACtC,WAAO,MAAM,QAAQ;EACvB;AACF,CAAC;ACPM,IAAM,qBAAqB,mBAAsE;EACtG,UAAU;EACV,cAAc;EACd,YAAY,MAAM;AAChB,UAAM,QAAQ,wBAAwB;AACtC,WAAO,MAAM,QAAQ;EACvB;AACF,CAAC;ACPM,IAAM,oBAAoB,mBAA2E;EAC1G,UAAU;EACV,cAAc;EACd,YAAY,CAAA,aAAY;AACtB,UAAM,EAAE,aAAa,IAAI,uBAAuB;AAChD,UAAM,OAAO,eAAe;AAE5B,QAAI,aAAa,gBAAgB;AAC/B,aAAO,6CAAc;IACvB;AACA,WAAO,6BAAM;EACf;AACF,CAAC;ACZM,IAAM,uBAAuB,mBAAyE;EAC3G,UAAU;EACV,cAAc;EACd,YAAY,MAAM;AAChB,UAAM,QAAQ,wBAAwB;AACtC,WAAO,MAAM,QAAQ;EACvB;AACF,CAAC;;;A5BPD,IAAAC,iBAAkB;;;AqCRX,IAAM,2BAA2B,MAAe;AACrD,MAAI;AACF,WAAO;EAET,QAAQ;EAAC;AAIT,SAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO;EAET,QAAQ;EAAC;AAGT,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO;EAET,QAAQ;EAAC;AAGT,SAAO;AACT;;;ACRA,IAAM,oBAAoB,oBAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,QAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,QAAM,YAAY,OAAO;AACzB,MAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;EACF;AACA,oBAAkB,IAAI,SAAS;AAE/B,UAAQ;IACN,iCAAiC,MAAM;EAAmE,OAAO;EACnH;AACF;;;AlC/BA,IAAAC,iBAAkB;AEDlB,IAAAC,iBAAkB;AbElB,IAAM,eAAe,kBAAkB,EAAE,aAAa,qBAAqB,CAAC;AASrE,SAAS,uBAAuB,SAA8B;AACnE,eAAa,YAAY,OAAO,EAAE,eAAe,OAAO;AAC1D;AEQO,IAAM,CAAC,aAAa,cAAc,IAAI,qBAAuC,aAAa;AClB1F,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B;ACHlC,IAAM,8BACX;AAEK,IAAM,oCAAoC,CAAC,SAChD,kDAAkD,IAAI;AAEjD,IAAM,oBACX;AAEK,IAAM,gDACX;AAEK,IAAM,+BACX;AACK,IAAM,+BACX;AAEK,IAAM,uCACX;AACK,IAAM,uCACX;AAEK,IAAM,8BAA8B,CAAC,kBAC1C,IAAI,aAAa,wBAAwB,aAAa,iBAAiB,aAAa;AAE/E,IAAM,uBAAuB,CAAC,kBACnC,mBAAmB,aAAa;AAE3B,IAAM,uBAAuB,CAAC,kBACnC,mBAAmB,aAAa;AAW3B,IAAM,6BAA6B;AAEnC,IAAM,kCACX;AAEK,IAAM,mCACX;AAEK,IAAM,oCACX;AAEK,IAAM,kCACX;AAEK,IAAM,mCACX;AAEK,IAAM,uCACX;ACxDK,IAAMC,mCAAkC,CAAC,WAAyB;AACvE,kCAAsC,MAAM;AAC1C,iBAAa,+BAA+B,EAAE,OAAO,CAAC;EACxD,CAAC;AACH;ACHA,IAAM,cAAc,CAAC,oBAAqC;AACxD,SAAO,IAAI,QAAc,CAAA,YAAW;AAClC,UAAM,UAAU,CAAC,WAAmB;AAClC,UAAI,CAAC,SAAS,UAAU,EAAE,SAAS,MAAM,GAAG;AAC1C,gBAAQ;AACR,wBAAgB,IAAI,UAAU,OAAO;MACvC;IACF;AAGA,oBAAgB,GAAG,UAAU,SAAS,EAAE,QAAQ,KAAK,CAAC;EACxD,CAAC;AACH;AAKO,IAAM,iBAAiB,CAAC,oBAAqC;AAClE,SAAO,OAAO,YAAiB;AAC7B,UAAM,YAAY,eAAe;AACjC,QAAI,CAAC,gBAAgB,SAAS;AAC5B,aAAO;IACT;AACA,WAAO,gBAAgB,QAAQ,SAAS,OAAO;EACjD;AACF;AAKO,IAAM,gBAAgB,CAAC,oBAAqC;AACjE,SAAO,UAAU,SAAc;AAC7B,UAAM,YAAY,eAAe;AACjC,WAAO,gBAAgB,QAAQ,GAAG,IAAI;EACxC;AACF;ALsDO,IAAM,UAAU,CAAC,4BAA4C,CAAC,MAAqB;AA9F1F,MAAA,IAAA;AA+FE,EAAAA,iCAAgC,SAAS;AAEzC,QAAM,EAAE,yBAAyB,GAAG,KAAK,IAAI,6BAAA,OAAA,4BAA6B,CAAC;AAC3E,QAAM,mBAAmB;AAEzB,QAAM,sBAAsB,eAAe;AAC3C,MAAI,cAAc;AAElB,MAAI,YAAY,cAAc,UAAa,YAAY,WAAW,QAAW;AAC3E,kBAAc,oBAAoB,OAAO,mBAAmB,CAAC;EAC/D;AAEA,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,eAAqB,4BAAY,eAAe,eAAe,GAAG,CAAC,eAAe,CAAC;AACzF,QAAM,cAAmB,4BAAY,cAAc,eAAe,GAAG,CAAC,eAAe,CAAC;AAEtF,GAAA,KAAA,gBAAgB,cAAhB,OAAA,SAAA,GAA2B,OAAO,kBAAkB,WAAW,EAAE,wBAAwB,CAAC,CAAA;AAE1F,SAAO;IACL;MACE,GAAG;MACH;MACA;IACF;IACA;MACE,yBACE,2BAAA,OAAA,2BAA2B,KAAA,gBAAgB,yBAAhB,OAAA,SAAA,GAAA,KAAA,iBAAuC,yBAAA;IACtE;EACF;AACF;AA4BO,SAAS,eACd,YACA,EAAE,0BAA0B,KAAK,IAA2B,CAAC,GAC9C;AACf,QAAM,EAAE,QAAQ,OAAO,SAAS,KAAAC,MAAK,SAAS,UAAU,gBAAgB,uBAAuB,cAAc,IAC3G,cAAA,OAAA,aAAc,CAAC;AAEjB,QAAM,iBAAa;IACjB,CAAC,WAAmE;AAClE,UAAIA,MAAK;AACP,eAAOA,KAAI,MAAM;MACnB;AACA,aAAO,yBAAyB;QAC9B;QACA;QACA;QACA;QACA;QACA,WAAY,iBAAA,OAAA,SAAA,cAA0C,QAAkB;QACxE,QAAS,iBAAA,OAAA,SAAA,cAA0C,QAAkB;MACvE,CAAC,EAAE,MAAM;IACX;IACA,CAACA,MAAK,QAAQ,OAAO,SAAS,gBAAgB,qBAAqB;EACrE;AAEA,QAAM,UAAU,iBAAiB;IAC/B,YAAY;MACV,GAAG;MACH;MACA;MACA,KAAK;IACP;IACA,SAAS;MACP;IACF;EACF,CAAC;AAED,MAAI,CAAC,SAAS;AACZ,WAAO,aAAa,MAAM,iBAAiB;EAC7C;AAEA,SAAO;AACT;AMhLA,SAAS,aACP,UACsF;AACtF,QAAM,EAAE,oBAAoB,oBAAoB,IAAI,eAAAC,QAAM,QAAQ,MAAM,SAAS,oBAAoB,GAAG,CAAC,QAAQ,CAAC;AAElH,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO;EACT,GAAG,CAAC,CAAC;AAEL,SAAO;IACL;IACA;EACF;AACF;ACoBO,IAAM,YAAY,MAAuB;AAnDhD,MAAA;AAoDE,EAAAF,iCAAgC,WAAW;AAE3C,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAAS,iBAAiB;AAEhC,GAAA,KAAA,gBAAgB,cAAhB,OAAA,SAAA,GAA2B,OAAOG,kBAAkB,WAAW,CAAA;AAE/D,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,WAAW,OAAU;EACpE;AAEA,SAAO;IACL,UAAU;IACV,QAAQ,OAAO;IACf,WAAW,gBAAgB;EAC7B;AACF;ACjBO,IAAM,YAAY,MAAuB;AAnDhD,MAAA;AAoDE,EAAAH,iCAAgC,WAAW;AAE3C,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAASI,iBAAiB;AAEhC,GAAA,KAAA,gBAAgB,cAAhB,OAAA,SAAA,GAA2B,OAAOD,kBAAkB,WAAW,CAAA;AAE/D,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,WAAW,OAAU;EACpE;AAEA,SAAO;IACL,UAAU;IACV,QAAQ,OAAO;IACf,WAAW,gBAAgB;EAC7B;AACF;AI9DO,IAAM,YAAY,CACvB,WACA,yBACG;AACH,QAAM,sBACJ,OAAO,yBAAyB,WAAW,uBAAuB,wBAAA,OAAA,SAAA,qBAAsB;AAC1F,QAAM,cAAc,uBAAuB,UAAU,eAAe,UAAU,QAAQ;AACtF,YAAU,cAAc;AAExB,QAAM,UAAU,OAAO,yBAAyB,WAAW,SAAY;AAEvE,QAAM,MAAM,CAAC,UAA+B;AAC1C,IAAAH,iCAAgC,eAAe,WAAW;AAE1D,UAAM,QAAQ,0BAA0B;AAExC,QAAI,CAAC,MAAM,UAAU,EAAC,WAAA,OAAA,SAAA,QAAS,qBAAoB;AACjD,aAAO;IACT;AAEA,WACEE,eAAAA,QAAA;MAAC;MAAA;QACE,GAAI;QACL,WAAW;QACX;MAAA;IACF;EAEJ;AACA,MAAI,cAAc,aAAa,WAAW;AAC1C,SAAO;AACT;AFzBO,IAAM,WAAW,CAAC,EAAE,UAAU,wBAAwB,MAAsD;AACjH,EAAAF,iCAAgC,UAAU;AAE1C,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAE,wBAAwB,CAAC;AACtD,MAAI,QAAQ;AACV,WAAO;EACT;AACA,SAAO;AACT;AAEO,IAAM,YAAY,CAAC,EAAE,UAAU,wBAAwB,MAAsD;AAClH,EAAAA,iCAAgC,WAAW;AAE3C,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAE,wBAAwB,CAAC;AACtD,MAAI,WAAW,MAAM;AACnB,WAAO;EACT;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAC,EAAE,SAAS,MAAwC;AAC7E,EAAAA,iCAAgC,aAAa;AAE7C,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,CAAC,gBAAgB,QAAQ;AAC3B,WAAO;EACT;AACA,SAAO;AACT;AAEO,IAAM,eAAe,CAAC,EAAE,SAAS,MAAwC;AAC9E,EAAAA,iCAAgC,cAAc;AAE9C,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,gBAAgB,WAAW,WAAW;AACxC,WAAO;EACT;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAC,EAAE,SAAS,MAAwC;AAC7E,EAAAA,iCAAgC,aAAa;AAE7C,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,gBAAgB,WAAW,SAAS;AACtC,WAAO;EACT;AACA,SAAO;AACT;AAEO,IAAM,gBAAgB,CAAC,EAAE,SAAS,MAAwC;AAC/E,EAAAA,iCAAgC,eAAe;AAE/C,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,gBAAgB,WAAW,YAAY;AACzC,WAAO;EACT;AACA,SAAO;AACT;AAoBO,IAAM,UAAU,CAAC,EAAE,UAAU,UAAU,yBAAyB,GAAG,qBAAqB,MAAoB;AACjH,EAAAA,iCAAgC,SAAS;AAEzC,QAAM,EAAE,UAAU,KAAAC,MAAK,OAAO,IAAI,QAAQ,EAAE,wBAAwB,CAAC;AAKrE,MAAI,CAAC,UAAU;AACb,WAAO;EACT;AAKA,QAAM,eAAe,YAAA,OAAA,WAAY;AAEjC,QAAM,aAAa;AAEnB,MAAI,CAAC,QAAQ;AACX,WAAO;EACT;AAKA,MAAI,OAAO,qBAAqB,cAAc,YAAY;AACxD,QAAI,qBAAqB,UAAUA,IAAG,GAAG;AACvC,aAAO;IACT;AACA,WAAO;EACT;AAEA,MACE,qBAAqB,QACrB,qBAAqB,cACrB,qBAAqB,WACrB,qBAAqB,MACrB;AACA,QAAIA,KAAI,oBAAoB,GAAG;AAC7B,aAAO;IACT;AACA,WAAO;EACT;AAMA,SAAO;AACT;AAEO,IAAM,mBAAmB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAE5B,QAAM,sBAAsB,OAAO,mBAC/B,OAAO,iBAAiB,SAAS;;IAEjC,OAAO,kBAAkB,OAAO,eAAe,SAAS;;AAE5DC,iBAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,YAAY,QAAQ,qBAAqB;AAC3C,WAAK,MAAM,uBAAuB;IACpC,OAAO;AACL,WAAK,MAAM,iBAAiB,KAAK;IACnC;EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAEd,IAAM,mBAAmB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvGA,iBAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,MAAM,iBAAiB,KAAK;EACnC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAMd,IAAM,wBAAwB,UAAU,CAAC,EAAE,MAAM,MAAM;AAC5DA,iBAAAA,QAAM,UAAU,MAAM;AACpB,eAAW,yBAAyB,mDAAmD;AACvF,SAAK,MAAM,sBAAsB;EACnC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,uBAAuB;AAMnB,IAAM,gCAAgC,UAAU,CAAC,EAAE,MAAM,MAAM;AACpEA,iBAAAA,QAAM,UAAU,MAAM;AACpB,eAAW,iCAAiC,2DAA2D;AACvG,SAAK,MAAM,8BAA8B;EAC3C,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,+BAA+B;AAM3B,IAAM,+BAA+B,UAAU,CAAC,EAAE,MAAM,MAAM;AACnEA,iBAAAA,QAAM,UAAU,MAAM;AACpB,eAAW,gCAAgC,0DAA0D;AACrG,SAAK,MAAM,6BAA6B;EAC1C,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,8BAA8B;AAE1B,IAAM,mCAAmC;EAC9C,CAAC,EAAE,OAAO,GAAG,6BAA6B,MAAgD;AACxFA,mBAAAA,QAAM,UAAU,MAAM;AACpB,WAAK,MAAM,uBAAuB,4BAA4B;IAChE,GAAG,CAAC,CAAC;AAEL,WAAO;EACT;EACA;AACF;;;AmCxNA,IAAIG,eAAc,CAAC,QAAQ;AACzB,QAAM,UAAU,GAAG;AACrB;AACA,IAAIC,iBAAgB,CAAC,KAAK,QAAQ,QAAQ,OAAO,IAAI,GAAG,KAAKD,aAAY,YAAY,GAAG;AACxF,IAAIE,gBAAe,CAAC,KAAK,QAAQ,YAAYD,eAAc,KAAK,QAAQ,yBAAyB,GAAG,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AAC9I,IAAIE,gBAAe,CAAC,KAAK,QAAQ,UAAU,OAAO,IAAI,GAAG,IAAIH,aAAY,mDAAmD,IAAI,kBAAkB,UAAU,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK;AACnM,IAAII,gBAAe,CAAC,KAAK,QAAQ,OAAO,YAAYH,eAAc,KAAK,QAAQ,wBAAwB,GAAG,SAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACrK,IAAII,mBAAkB,CAAC,KAAK,QAAQ,YAAYJ,eAAc,KAAK,QAAQ,uBAAuB,GAAG;;;ACG9F,IAAM,kBAAkB,CAAC,gBAAoC,iBAAiB,aAAuB;AAC1G,MAAI,gBAAgB;AAClB,WAAO;EACT;AAEA,QAAM,gBAAgB,iBAAiB,cAAc;AACrD,MAAI,eAAe;AACjB,QAAI,kBAAkB,YAAY;AAChC,aAAO;IACT;AAEA,WAAO;EACT;AAEA,SAAO,gBAAgB,cAAc;AACvC;AAEA,IAAM,mBAAmB,CAAC,mBAAA;;AACxB,8BACG,KAAK,EACL,QAAQ,MAAM,EAAE,EAChB,MAAM,cAAc,MAHvB,mBAG2B;;AAEtB,IAAM,kBAAkB,CAAC,mBAA2B,eAAe,KAAK,EAAE,QAAQ,MAAM,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;;;ACjCxG,SAAS,gBAAgB,KAAyB;AACvD,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AAEA,SAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,KAAyB;AACrD,SAAO,iBAAiB,KAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,KAAa;AAC9C,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,KAAiC;AACrE,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,SAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE,SAAS,IAAI;AACrF;;;ACPO,SAAS,eAAe,KAAyB;AACtD,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,MAAI;AACJ,MAAI,IAAI,MAAM,iBAAiB,GAAG;AAChC,YAAQ;EACV,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACxC,WAAO;EACT,OAAO;AACL,YAAQ;EACV;AAEA,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE;AACtC,SAAO,SAAS,QAAQ;AAC1B;;;ACYA,IAAM,iBAAyC;EAC7C,cAAc;EACd,wBAAwB;EACxB,QAAQ;EACR,aAAa,CAAC,GAAY,cAAsB,YAAY;EAC5D,kBAAkB;EAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,OAAqB,IAAI,QAAQ,CAAA,MAAK,WAAW,GAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;AAC5D,SAAO,SAAS,SAAS,IAAI,KAAK,OAAO,KAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;AACH,MAAI,cAAc;AAElB,QAAM,qBAAqB,MAAM;AAC/B,UAAM,WAAW,KAAK;AACtB,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,WAAW,KAAK,IAAI,MAAM,WAAW;AACjD,YAAQ,YAAY,OAAO,KAAK,MAAM;AACtC,WAAO,KAAK,IAAI,KAAK,0BAA0B,OAAO,KAAK;EAC7D;AAEA,SAAO,YAA2B;AAChC,UAAM,MAAM,mBAAmB,CAAC;AAChC;EACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,MAAkB;AACxG,MAAI,aAAa;AACjB,QAAM,EAAE,aAAa,cAAc,wBAAwB,QAAQ,kBAAkB,OAAO,IAAI;IAC9F,GAAG;IACH,GAAG;EACL;AAEA,QAAM,QAAQ,8BAA8B;IAC1C;IACA;IACA;IACA;EACF,CAAC;AAED,SAAO,MAAM;AACX,QAAI;AACF,aAAO,MAAM,SAAS;IACxB,SAAS,GAAG;AACV;AACA,UAAI,CAAC,YAAY,GAAG,UAAU,GAAG;AAC/B,cAAM;MACR;AACA,UAAI,oBAAoB,eAAe,GAAG;AACxC,cAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;MAC1D,OAAO;AACL,cAAM,MAAM;MACd;IACF;EACF;AACF;;;AC5GA,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AAUrB,eAAsB,WAAW,MAAM,IAAI,MAAqD;AAC9F,QAAM,EAAE,OAAO,OAAO,YAAY,aAAa,MAAM,IAAI,QAAQ,CAAC;AAElE,QAAM,OAAO,MAAM;AACjB,WAAO,IAAI,QAA2B,CAAC,SAAS,WAAW;AACzD,UAAI,CAAC,KAAK;AACR,eAAO,IAAI,MAAM,YAAY,CAAC;MAChC;AAEA,UAAI,CAAC,YAAY,CAAC,SAAS,MAAM;AAC/B,eAAO,iBAAiB;MAC1B;AAEA,YAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,UAAI,YAAa,QAAO,aAAa,eAAe,WAAW;AAC/D,aAAO,QAAQ,SAAS;AACxB,aAAO,QAAQ,SAAS;AAExB,aAAO,iBAAiB,QAAQ,MAAM;AACpC,eAAO,OAAO;AACd,gBAAQ,MAAM;MAChB,CAAC;AAED,aAAO,iBAAiB,SAAS,MAAM;AACrC,eAAO,OAAO;AACd,eAAO;MACT,CAAC;AAED,aAAO,MAAM;AACb,aAAO,QAAQ;AACf,+CAAa;AACb,eAAS,KAAK,YAAY,MAAM;IAClC,CAAC;EACH;AAEA,SAAO,MAAM,MAAM,EAAE,aAAa,CAAC,GAAG,eAAe,cAAc,EAAE,CAAC;AACxE;;;ACzCA,IAAM,uBAAuB;AAE7B,IAAM,EAAE,kBAAkB,IAAI,2BAA2B;AAEzD,IAAMK,gBAAe,kBAAkB,EAAE,aAAa,gBAAgB,CAAC;AAQhE,SAAS,kCAAkC,aAAqB;AACrE,EAAAA,cAAa,eAAe,EAAE,YAAY,CAAC;AAC7C;AA0BA,IAAM,oBAAoB,OAAO,SAAoC;AACnE,QAAM,iBAAiB,SAAS,cAAiC,8BAA8B;AAE/F,MAAI,gBAAgB;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,iBAAiB,QAAQ,MAAM;AAC5C,gBAAQ,cAAc;MACxB,CAAC;AAED,qBAAe,iBAAiB,SAAS,MAAM;AAC7C,eAAO,oBAAoB;MAC7B,CAAC;IACH,CAAC;EACH;AAEA,MAAI,EAAC,6BAAM,iBAAgB;AACzB,IAAAA,cAAa,gCAAgC;AAC7C;EACF;AAEA,SAAO,WAAW,iBAAiB,IAAI,GAAG;IACxC,OAAO;IACP,aAAa;IACb,OAAO,KAAK;IACZ,YAAY,6BAA6B,IAAI;EAC/C,CAAC,EAAE,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,oBAAoB;EACtC,CAAC;AACH;AAUA,IAAM,mBAAmB,CAAC,SAAmC;;AAC3D,QAAM,EAAE,YAAY,gBAAgB,gBAAgB,UAAU,QAAQ,eAAe,IAAI;AAEzF,MAAI,YAAY;AACd,WAAO;EACT;AAEA,MAAI,aAAa;AACjB,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,GAAG;AAC3C,iBAAa,sBAAsB,QAAQ,EAAE,QAAQ,iBAAiB,EAAE;EAC1E,WAAW,UAAU,CAAC,oBAAkB,yBAAoB,cAAc,MAAlC,mBAAqC,gBAAe,EAAE,GAAG;AAC/F,iBAAa,eAAe,MAAM;EACpC,OAAO;AACL,mBAAa,yBAAoB,cAAc,MAAlC,mBAAqC,gBAAe;EACnE;AAEA,QAAM,UAAU,iBAAiB,GAAG,eAAe,QAAQ,QAAQ,EAAE,CAAC,MAAM;AAC5E,QAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAO,WAAW,UAAU,wBAAwB,OAAO,eAAe,OAAO;AACnF;AAKA,IAAM,+BAA+B,CAAC,YAAsC;AAC1E,QAAM,MAA8B,CAAC;AAErC,MAAI,QAAQ,gBAAgB;AAC1B,QAAI,4BAA4B,IAAI,QAAQ;EAC9C;AAEA,MAAI,QAAQ,UAAU;AACpB,QAAI,sBAAsB,IAAI,QAAQ;EACxC;AAEA,MAAI,QAAQ,QAAQ;AAClB,QAAI,mBAAmB,IAAI,QAAQ;EACrC;AAEA,MAAI,QAAQ,OAAO;AACjB,QAAI,QAAQ,QAAQ;EACtB;AAEA,SAAO;AACT;AAEA,IAAM,+BAA+B,CAAC,YAAsC,CAAC,WAA8B;AACzG,QAAM,aAAa,6BAA6B,OAAO;AACvD,aAAW,aAAa,YAAY;AAClC,WAAO,aAAa,WAAW,WAAW,SAAS,CAAC;EACtD;AACF;;;AExIO,IAAM,oBAAoB,CAAC,YAAoB;AACpD,MAAI,yBAAyB,GAAG;AAC9B,YAAQ,MAAM,UAAU,OAAO,EAAE;EACnC;AACF;;;AEHO,SAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,MAAI,OAAO,UAAU,YAAY;AAC/B,WAAQ,MAAwB,GAAG;EACrC;AAEA,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;EACT;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;EACT;AAEA,SAAO;AACT;;;AGAA,IAAAC,iBAAgE;ACjBhE,IAAAA,iBAAkB;AEAlB,IAAAA,iBAAkB;ACAlB,IAAAA,iBAAgC;AAChC,uBAA6B;ACE7B,IAAAC,iBAAkB;ACHlB,IAAAA,iBAAkB;ACGlB,IAAAC,iBAAkB;ACHlB,IAAAA,iBAA4C;;;ASArC,IAAM,UAAU,CAAsC,QAAW,UAA2B;AACjG,QAAM,OAAO,EAAE,GAAG,IAAI;AACtB,aAAW,QAAQ,OAAO;AACxB,WAAO,KAAK,IAAI;EAClB;AACA,SAAO;AACT;;;ARHA,IAAAC,iBAAkB;ACFlB,IAAAA,iBAAkB;ACAlB,IAAAA,iBAAkB;ACAlB,IAAAA,iBAAkB;ACDlB,IAAAA,iBAAkB;ACClB,IAAAC,iBAAkB;;;AIaX,IAAM,cAAc,CAAC,kBAA2B,OAAkB,iBAA2C;AAClH,MAAI,CAAC,oBAAoB,cAAc;AACrC,WAAO,0BAA0B,YAAY;EAC/C;AACA,SAAO,0BAA0B,KAAK;AACxC;AAEA,IAAM,4BAA4B,CAAC,iBAA+B;AAChE,QAAM,SAAS,aAAa;AAC5B,QAAM,OAAO,aAAa;AAC1B,QAAM,YAAY,aAAa;AAC/B,QAAM,gBAAgB,aAAa;AACnC,QAAM,gBAAgB,aAAa;AACnC,QAAM,UAAU,aAAa;AAC7B,QAAM,eAAe,aAAa;AAClC,QAAM,QAAQ,aAAa;AAC3B,QAAM,UAAU,aAAa;AAC7B,QAAM,iBAAiB,aAAa;AACpC,QAAM,UAAU,aAAa;AAC7B,QAAM,QAAQ,aAAa;AAC3B,QAAM,wBAAwB,aAAa;AAE3C,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;AAEA,IAAM,4BAA4B,CAAC,UAAqB;;AACtD,QAAM,SAAoC,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAC7E,QAAM,OAAO,MAAM;AACnB,QAAM,YAAuC,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM;AACtF,QAAM,UAAU,MAAM;AACtB,QAAM,iBAAgB,WAAM,YAAN,mBAAe;AACrC,QAAM,gBAA+C,MAAM,WACvD,iBAAM,QAAQ,oBAAd,mBAA+B,QAA/B,mBAAoC,SACpC;AACJ,QAAM,wBAAiD,MAAM,UAAU,MAAM,QAAQ,wBAAwB;AAC7G,QAAM,QAAQ,mCAAS;AACvB,QAAM,eAAe,MAAM;AAC3B,QAAM,QAAmC,MAAM,eAAe,MAAM,aAAa,KAAK,MAAM;AAC5F,QAAM,UAAU,6CAAc;AAC9B,QAAM,aAAa,gBACf,kCAAM,4BAAN,mBAA+B,KAAK,CAAA,OAAM,GAAG,aAAa,OAAO,SACjE;AACJ,QAAM,iBAAiB,aAAa,WAAW,cAAc;AAC7D,QAAM,UAAU,aAAa,WAAW,OAAO;AAE/C,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;;;AHrFA,IAAAC,iBAAkB;;;AICX,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,IAAM,YAAY;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,KAAK,GAAG,GAAG,GAAG;;;ACuEzD,IAAM,MAAkB,CAAC,oBAAoB,kBAAkB,OAAO,SAAS,SAAS;AACtF,QAAM,EAAE,OAAO,IAAI,QAAQ,CAAC;AAC5B,MAAI,WAAW,mBAAmB,IAAI,KAAK;AAE3C,MAAI,CAAC,UAAU;AACb,eAAW,CAAC;AACZ,uBAAmB,IAAI,OAAO,QAAQ;EACxC;AAEA,WAAS,KAAK,OAAO;AAErB,MAAI,UAAU,iBAAiB,IAAI,KAAK,GAAG;AACzC,YAAQ,iBAAiB,IAAI,KAAK,CAAC;EACrC;AACF;AAKA,IAAM,YAA8B,CAAC,oBAAoB,OAAO,aAC7D,mBAAmB,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,CAAA,MAAK,EAAE,OAAO,CAAC;AAK3D,IAAM,OAAoB,CAAC,oBAAoB,OAAO,YAAY;AAChE,QAAM,WAAW,mBAAmB,IAAI,KAAK;AAC7C,MAAI,UAAU;AACZ,QAAI,SAAS;AACX,eAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;IACpD,OAAO;AACL,yBAAmB,IAAI,OAAO,CAAC,CAAC;IAClC;EACF;AACF;AA6CO,IAAM,iBAAiB,MAAgE;AAC5F,QAAM,qBAAqB,oBAAI,IAAmD;AAClF,QAAM,mBAAmB,oBAAI,IAAuB;AACpD,QAAM,gCAAgC,oBAAI,IAAmD;AAE7F,QAAM,OAAiC,CAAC,OAAO,YAAY;AACzD,qBAAiB,IAAI,OAAO,OAAO;AACnC,cAAU,+BAA+B,OAAO,OAAO;AACvD,cAAU,oBAAoB,OAAO,OAAO;EAC9C;AAEA,SAAO;;IAEL,IAAI,IAAI,SAAS,IAAI,oBAAoB,kBAAkB,GAAG,IAAI;;;IAGlE,eAAe,IAAI,SAAS,IAAI,+BAA+B,kBAAkB,GAAG,IAAI;;IAExF;;IAEA,KAAK,IAAI,SAAS,KAAK,oBAAoB,GAAG,IAAI;;;IAGlD,gBAAgB,IAAI,SAAS,KAAK,+BAA+B,GAAG,IAAI;;IAGxE,UAAU;MACR,mBAAmB,CAAA,UAAS,mBAAmB,IAAI,KAAK,KAAK,CAAC;IAChE;EACF;AACF;;;ACtNO,IAAM,cAAc;EACzB,QAAQ;AACV;AAEO,IAAM,sBAAsB,MAAM;AACvC,SAAO,eAAkC;AAC3C;;;AvBJA,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACnD,SAAO,SAAS,OAAO,WAAW,cAAc,SAAS;AAC3D;AGHO,IAAM,oBACX,CAAC,aACD,CAAC,SAAyF;AACxF,MAAI;AACF,WAAO,eAAAC,QAAM,SAAS,KAAK,QAAQ;EACrC,QAAQ;AACN,WAAO,aAAa,MAAM,kCAAkC,IAAI,CAAC;EACnE;AACF;AAEK,IAAM,4BAA4B,CAAC,UAAuC,gBAAwB;AACvG,MAAI,CAAC,UAAU;AACb,eAAW;EACb;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,eAAAA,QAAA,cAAC,UAAA,MAAQ,QAAS;EAC/B;AACA,SAAO;AACT;AAEO,IAAM,cACX,CAAC,OACD,IAAI,SAAc;AAChB,MAAI,MAAM,OAAO,OAAO,YAAY;AAClC,WAAO,GAAG,GAAG,IAAI;EACnB;AACF;AC/BK,SAAS,cAAiB,GAAgB;AAC/C,SAAO,OAAO,MAAM;AACtB;ACEA,IAAM,SAAS,oBAAI,IAAoB;AAEhC,SAAS,4BAA4B,MAAc,OAAe,WAAW,GAAS;AAC3FA,iBAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,IAAI,IAAI,KAAK;AAClC,QAAI,SAAS,UAAU;AACrB,aAAO,aAAa,MAAM,KAAK;IACjC;AACA,WAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B,WAAO,MAAM;AACX,aAAO,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAC9C;EACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,6BACd,kBACA,MACA,OACwB;AACxB,QAAM,cAAc,iBAAiB,eAAe,iBAAiB,QAAQ,QAAQ;AACrF,QAAM,MAAM,CAAC,UAAa;AACxB,gCAA4B,MAAM,KAAK;AACvC,WAAOA,eAAAA,QAAA,cAAC,kBAAA,EAAkB,GAAI,MAAA,CAAe;EAC/C;AACA,MAAI,cAAc,gCAAgC,WAAW;AAC7D,SAAO;AACT;ACfO,IAAM,yBAAyB,CAAC,aAA6C;AAClF,QAAM,eAAe,MAAM,SAAS,MAAM,EAAE,KAAK,IAAI;AACrD,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAA6B,YAAY;AAEnE,SAAO,SAAS,IAAI,CAAC,IAAI,WAAW;IAClC,IAAI,GAAG;IACP,OAAO,CAAC,SAAkB,SAAS,CAAA,cAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;IACjG,SAAS,MAAM,SAAS,CAAA,cAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;IACtF,QAAQ,MAAMA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,MAAM,KAAK,QAAI,+BAAa,GAAG,WAAW,MAAM,KAAK,CAAC,IAAI,IAAK;EAClF,EAAE;AACJ;AEzBO,IAAM,kBAAkB,CAAC,GAAQ,cAAqD;AAC3F,SAAO,CAAC,CAAC,KAAKA,eAAAA,QAAM,eAAe,CAAC,MAAM,KAAA,OAAA,SAAA,EAA0B,UAAS;AAC/E;ADcO,IAAM,4BAA4B,CACvC,UACA,YACG;AACH,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO;IACL;MACE;MACA;MACA,eAAe;MACf,eAAe;MACf,oBAAoB;MACpB,eAAe;IACjB;IACA;EACF;AACF;AAEO,IAAM,oCAAoC,CAC/C,UACA,YACG;AACH,QAAM,qBAAqB,CAAC,WAAW,SAAS;AAChD,SAAO;IACL;MACE;MACA;MACA,eAAe;MACf,eAAe;MACf,eAAe;IACjB;IACA;EACF;AACF;AA+BO,IAAM,uBAAuB,CAAC,aAA8B;AACjE,QAAM,oBAAuC,CAAC;AAE9C,QAAM,qBAA4B;IAChC;IACA;IACA;IACA;IACA;EACF;AAEAA,iBAAAA,QAAM,SAAS,QAAQ,UAAU,CAAA,UAAS;AACxC,QAAI,CAAC,mBAAmB,KAAK,CAAA,cAAa,gBAAgB,OAAO,SAAS,CAAC,GAAG;AAC5E,wBAAkB,KAAK,KAAK;IAC9B;EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,QAA8B,YAAoC;AACxF,QAAM,EAAE,UAAU,eAAe,eAAe,oBAAoB,oBAAoB,cAAc,IAAI;AAC1G,QAAM,EAAE,sBAAsB,MAAM,IAAI,WAAW,CAAC;AACpD,QAAM,gBAAwC,CAAC;AAE/CA,iBAAAA,QAAM,SAAS,QAAQ,UAAU,CAAA,UAAS;AACxC,QACE,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,kBAAkB,GAC1C;AACA,UAAI,SAAS,CAAC,qBAAqB;AACjC,0BAAkB,4BAA4B,aAAa,CAAC;MAC9D;AACA;IACF;AAEA,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,EAAE,UAAAC,WAAU,OAAO,KAAK,UAAU,IAAI;AAE5C,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,cAAc,OAAO,kBAAkB,GAAG;AAE5C,sBAAc,KAAK,EAAE,MAAM,CAAC;MAC9B,WAAW,aAAa,KAAK,GAAG;AAE9B,sBAAc,KAAK,EAAE,OAAO,WAAW,UAAAA,WAAU,IAAI,CAAC;MACxD,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;MACF;IACF;AAEA,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG;AAEzB,sBAAc,KAAK,EAAE,OAAO,WAAW,IAAI,CAAC;MAC9C,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;MACF;IACF;EACF,CAAC;AAED,QAAM,qBAAqD,CAAC;AAC5D,QAAM,uBAAuD,CAAC;AAC9D,QAAM,uBAAuD,CAAC;AAE9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,aAAa,EAAE,GAAG;AACpB,yBAAmB,KAAK,EAAE,WAAW,GAAG,UAAU,IAAI,MAAM,CAAC;AAC7D,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAChE;IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;IAClE;EACF,CAAC;AAED,QAAM,4BAA4B,uBAAuB,kBAAkB;AAC3E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAC/E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,QAAM,cAA4B,CAAC;AACnC,QAAM,qBAA4C,CAAC;AAEnD,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,cAAc,IAAI,kBAAkB,GAAG;AACzC,kBAAY,KAAK,EAAE,OAAO,GAAG,MAAM,CAAC;AACpC;IACF;AACA,QAAI,aAAa,EAAE,GAAG;AACpB,YAAM;QACJ,QAAQ;QACR;QACA;MACF,IAAI,0BAA0B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACtD,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,4BAA4B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,OAAO,SAAS,WAAW,YAAY,CAAC;AACzF,yBAAmB,KAAK,aAAa;AACrC,yBAAmB,KAAK,WAAW;AACnC;IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,4BAA4B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,WAAW,YAAY,CAAC;AACzE,yBAAmB,KAAK,WAAW;AACnC;IACF;EACF,CAAC;AAED,SAAO,EAAE,aAAa,mBAAmB;AAC3C;AAEA,IAAM,gBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,WAAW,KAAK,CAAA,MAAK,MAAM,KAAK;AAC5E;AAEA,IAAM,eAAe,CAAC,eAA6B;AACjD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACjD;AAEA,IAAM,iBAAiB,CAAC,eAA6B;AACnD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD;AEzMO,IAAM,+BAA+B,CAAC,aAAkD;AAC7F,QAAM,qBAAqB,CAAC,iBAAiB,SAAS;AACtD,SAAO,mBAAmB;IACxB;IACA;IACA,oBAAoB;IACpB,qBAAqB;IACrB,mBAAmB;IACnB,0BAA0B;IAC1B,0BAA0B;EAC5B,CAAC;AACH;AAcA,IAAM,qBAAqB,CAAC;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAgC;AAC9B,QAAM,gBAAsC,CAAC;AAC7C,QAAM,kBAAoC,CAAC;AAC3C,QAAM,yBAAgD,CAAC;AAEvDD,iBAAAA,QAAM,SAAS,QAAQ,UAAU,CAAA,UAAS;AACxC,QACE,CAAC,gBAAgB,OAAO,kBAAkB,KAC1C,CAAC,gBAAgB,OAAO,wBAAwB,KAChD,CAAC,gBAAgB,OAAO,wBAAwB,GAChD;AACA,UAAI,OAAO;AACTE,0BAAkB,0BAA0B;MAC9C;AACA;IACF;AAGA,QAAI,gBAAgB,OAAO,wBAAwB,KAAK,gBAAgB,OAAO,wBAAwB,GAAG;AACxG;IACF;AAGA,UAAM,EAAE,MAAM,IAAI;AAElBF,mBAAAA,QAAM,SAAS,QAAQ,MAAM,UAAU,CAAAG,WAAS;AAC9C,UAAI,CAAC,gBAAgBA,QAAO,mBAAmB,KAAK,CAAC,gBAAgBA,QAAO,iBAAiB,GAAG;AAC9F,YAAIA,QAAO;AACTD,4BAAkB,+BAA+B;QACnD;AAEA;MACF;AAEA,YAAM,EAAE,OAAAE,OAAM,IAAID;AAElB,YAAM,EAAE,OAAO,WAAW,MAAM,SAAS,KAAK,IAAIC;AAElD,UAAI,gBAAgBD,QAAO,mBAAmB,GAAG;AAC/C,YAAIE,eAAcD,QAAO,kBAAkB,GAAG;AAE5C,wBAAc,KAAK,EAAE,MAAM,CAAC;QAC9B,WAAW,iBAAiBA,MAAK,GAAG;AAClC,gBAAM,WAAW;YACf;YACA;UACF;AAEA,cAAI,YAAY,QAAW;AACzB,0BAAc,KAAK;cACjB,GAAG;cACH;YACF,CAAC;UACH,WAAW,SAAS,QAAW;AAC7B,0BAAc,KAAK;cACjB,GAAG;cACH,MAAM,KAAK,WAAW,GAAG,IAAI,OAAO,IAAI,IAAI;YAC9C,CAAC;UACH,OAAO;AAELF,8BAAkB,4DAA4D;AAC9E;UACF;QACF,OAAO;AACLA,4BAAkB,oCAAoC;AACtD;QACF;MACF;AAEA,UAAI,gBAAgBC,QAAO,iBAAiB,GAAG;AAC7C,YAAIG,gBAAeF,MAAK,GAAG;AACzB,wBAAc,KAAK,EAAE,OAAO,WAAW,KAAK,CAAC;QAC/C,OAAO;AACLF,4BAAkB,gCAAgC;AAClD;QACF;MACF;IACF,CAAC;EACH,CAAC;AAED,QAAM,2BAA2D,CAAC;AAClE,QAAM,uBAAuD,CAAC;AAC9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,iBAAiB,EAAE,GAAG;AACxB,+BAAyB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;IACtE;AACA,QAAII,gBAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;IAClE;EACF,CAAC;AAED,QAAM,kCAAkC,uBAAuB,wBAAwB;AACvF,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAID,eAAc,IAAI,kBAAkB,GAAG;AACzC,sBAAgB,KAAK;QACnB,OAAO,GAAG;MACZ,CAAC;IACH;AACA,QAAI,iBAAiB,EAAE,GAAG;AACxB,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,gCAAgC,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AAC5D,YAAM,WAA2B;QAC/B,OAAO,GAAG;QACV;QACA;MACF;AAEA,UAAI,aAAa,IAAI;AACnB,iBAAS,UAAU,GAAG;MACxB,WAAW,UAAU,IAAI;AACvB,iBAAS,OAAO,GAAG;MACrB;AACA,sBAAgB,KAAK,QAAQ;AAC7B,6BAAuB,KAAK,UAAU;IACxC;AACA,QAAIC,gBAAe,EAAE,GAAG;AACtB,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,4BAA4B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACxD,sBAAgB,KAAK;QACnB,OAAO,GAAG;QACV,MAAM,GAAG;QACT;QACA;MACF,CAAC;AACD,6BAAuB,KAAK,UAAU;IACxC;EACF,CAAC;AAED,SAAO,EAAE,iBAAiB,uBAAuB;AACnD;AAEA,IAAMD,iBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,SAAS,UAAU,IAAI;AAChD,SAAO,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,WAAW,KAAK,CAAA,MAAK,MAAM,KAAK;AAChF;AAEA,IAAM,mBAAmB,CAAC,eAAyD;AACjF,QAAM,EAAE,OAAO,WAAW,SAAS,KAAK,IAAI;AAC5C,SAAO,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,OAAO,YAAY,cAAc,OAAO,SAAS;AACrF;AAEA,IAAMC,kBAAiB,CAAC,eAAuD;AAC7E,QAAM,EAAE,OAAO,MAAM,UAAU,IAAI;AACnC,SAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;AACpC;ACrMA,SAAS,uBAAuB,SAA6E;AAC3G,QAAM,EAAE,OAAO,YAAA,OAAA,SAAA,SAAU,MAAM,UAAU,UAAU,EAAE,IAAI;AAEzD,SAAO,IAAI,QAAc,CAAC,SAAS,WAAW;AAC5C,QAAI,CAAC,MAAM;AACT,aAAO,IAAI,MAAM,0BAA0B,CAAC;AAC5C;IACF;AAEA,QAAI,iBAAqC;AACzC,QAAI,UAAU;AACZ,uBAAiB,QAAA,OAAA,SAAA,KAAM,cAAc,QAAA;IACvC;AAGA,UAAM,2BAA0B,kBAAA,OAAA,SAAA,eAAgB,sBAAqB,eAAe,oBAAoB;AACxG,QAAI,yBAAyB;AAC3B,cAAQ;AACR;IACF;AAGA,UAAM,WAAW,IAAI,iBAAiB,CAAA,kBAAiB;AACrD,iBAAW,YAAY,eAAe;AACpC,YAAI,SAAS,SAAS,aAAa;AACjC,cAAI,CAAC,kBAAkB,UAAU;AAC/B,6BAAiB,QAAA,OAAA,SAAA,KAAM,cAAc,QAAA;UACvC;AAEA,eAAI,kBAAA,OAAA,SAAA,eAAgB,sBAAqB,eAAe,oBAAoB,GAAG;AAC7E,qBAAS,WAAW;AACpB,oBAAQ;AACR;UACF;QACF;MACF;IACF,CAAC;AAED,aAAS,QAAQ,MAAM,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAGzD,QAAI,UAAU,GAAG;AACf,iBAAW,MAAM;AACf,iBAAS,WAAW;AACpB,eAAO,IAAI,MAAM,sCAAsC,CAAC;MAC1D,GAAG,OAAO;IACZ;EACF,CAAC;AACH;AAKO,SAAS,yBAAyB,WAAoB;AAC3D,QAAM,iBAAa,uBAAsB;AACzC,QAAM,CAAC,QAAQ,SAAS,QAAIC,eAAAA,UAA6C,WAAW;AAEpF,gCAAU,MAAM;AACd,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,4DAA4D;IAC9E;AAEA,QAAI,OAAO,WAAW,eAAe,CAAC,WAAW,SAAS;AACxD,iBAAW,UAAU,uBAAuB,EAAE,UAAU,0BAA0B,SAAS,KAAK,CAAC,EAC9F,KAAK,MAAM;AACV,kBAAU,UAAU;MACtB,CAAC,EACA,MAAM,MAAM;AACX,kBAAU,OAAO;MACnB,CAAC;IACL;EACF,GAAG,CAAC,SAAS,CAAC;AAEd,SAAO;AACT;ACxEA,IAAM,eAAe,CAAC,UAAoC;AACxD,SAAO,WAAW;AACpB;AAEA,IAAM,cAAc,CAAC,UAAmC;AACtD,SAAO,UAAU;AACnB;AAEA,IAAM,4BAA4B,CAChC,cAKG;AACH,SAAO,aAAA,OAAA,SAAA,UAAW,IAAI,CAAC,EAAE,WAAW,aAAa,GAAG,KAAK,MAAM,IAAA;AACjE;AAmCO,IAAM,oBAAN,cAAgCP,eAAAA,QAAM,cAQ3C;EARK,cAAA;AAAA,UAAA,GAAA,SAAA;AASL,SAAQ,UAAUA,eAAAA,QAAM,UAA0B;EAAA;EAElD,mBAAmB,YAA8C;AArEnE,QAAA,IAAA,IAAA,IAAA;AAsEI,QAAI,CAAC,aAAa,UAAU,KAAK,CAAC,aAAa,KAAK,KAAK,GAAG;AAC1D;IACF;AAKA,UAAM,YAAY,QAAQ,WAAW,OAAO,eAAe,mBAAmB,UAAU;AACxF,UAAM,WAAW,QAAQ,KAAK,MAAM,OAAO,eAAe,mBAAmB,UAAU;AAGvF,UAAM,uBAAqB,KAAA,UAAU,gBAAV,OAAA,SAAA,GAAuB,cAAW,KAAA,SAAS,gBAAT,OAAA,SAAA,GAAsB;AACnF,UAAM,2BAAyB,KAAA,UAAU,oBAAV,OAAA,SAAA,GAA2B,cAAW,KAAA,SAAS,oBAAT,OAAA,SAAA,GAA0B;AAI/F,UAAM,+BAA+B,0BAA0B,WAAW,MAAM,eAAe;AAC/F,UAAM,8BAA8B,0BAA0B,KAAK,MAAM,MAAM,eAAe;AAE9F,QACE,CAAC,cAAc,WAAW,QAAQ,KAClC,CAAC,cAAc,8BAA8B,2BAA2B,KACxE,sBACA,wBACA;AACA,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,MAAM,YAAY,EAAE,MAAM,KAAK,QAAQ,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC;MAChF;IACF;EACF;EAEA,oBAAoB;AAClB,QAAI,KAAK,QAAQ,SAAS;AACxB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,MAAM,KAAK,QAAQ,SAAS,KAAK,MAAM,KAAK;MACzD;AAEA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,KAAK,KAAK,MAAM,KAAK;MAClC;IACF;EACF;EAEA,uBAAuB;AACrB,QAAI,KAAK,QAAQ,SAAS;AACxB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO;MACzC;AACA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,MAAM;MACnB;IACF;EACF;EAEA,SAAS;AACP,UAAM,EAAE,sBAAsB,MAAM,IAAI,KAAK;AAC7C,UAAM,iBAAiB;MACrB,KAAK,KAAK;MACV,GAAG,KAAK,MAAM;MACd,GAAI,KAAK,MAAM,aAAa,EAAE,wBAAwB,KAAK,MAAM,UAAU;IAC7E;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,CAAC,uBAAuBA,eAAAA,QAAA,cAAC,OAAA,EAAK,GAAG,eAAA,CAAgB,GACjD,KAAK,MAAM,QACd;EAEJ;AACF;ATpBA,IAAM,wBAAwB,CAAC,UAAsC;AAvHrE,MAAA,IAAA;AAwHE,SACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,OACG,KAAA,SAAA,OAAA,SAAA,MAAO,uBAAP,OAAA,SAAA,GAA2B,IAAI,CAAC,QAAQ,cAAU,8BAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,CAAA,IACtF,KAAA,SAAA,OAAA,SAAA,MAAO,2BAAP,OAAA,SAAA,GAA+B,IAAI,CAAC,QAAQ,cAAU,8BAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,CAAA,CAC7F;AAEJ;AAEO,IAAM,SAAS;EACpB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAiD;AACvF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,UAAU,oBAAoB,KAAK;AAClD;AAEO,IAAM,SAAS;EACpB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAiD;AACvF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,UAAU,oBAAoB,KAAK;AAClD;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrFE,oBAAkB,4BAA4B;AAC9C,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrFE,oBAAkB,4BAA4B;AAC9C,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEA,IAAM,eAAe;EACnB,CAAC;IACC;IACA;IACA;IACA,GAAG;EACL,MAAiG;AAC/F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,QAAQ;AACpF,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACvBA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;QAC/B,WAAW;MAAA;MAEXA,eAAAA,QAAA,cAAC,uBAAA,EAAsB,mBAAA,CAAwC;IACjE,CACF;EAEJ;EACA,EAAE,WAAW,eAAe,oBAAoB,KAAK;AACvD;AAEO,IAAM,cAAqC,OAAO,OAAO,cAAc;EAC5E,MAAM;EACN,MAAM;AACR,CAAC;AAED,IAAM,wBAAoB,8BAA0B;EAClD,OAAO,MAAM;EAAC;EACd,SAAS,MAAM;EAAC;EAChB,aAAa,MAAM;EAAC;AACtB,CAAC;AAED,IAAM,cAAc;EAClB,CAAC;IACC;IACA;IACA;IACA,GAAG;EACL,MAA0F;AACxF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,UAAU;MACpF,qBAAqB,CAAC,CAAC,MAAM;IAC/B,CAAC;AACD,UAAM,mBAAmB,OAAO,OAAO,MAAM,oBAAoB,CAAC,GAAG,EAAE,YAAY,CAAC;AACpF,UAAM,EAAE,iBAAiB,uBAAuB,IAAI,6BAA6B,MAAM,QAAQ;AAC/F,UAAM,oBAAoB,qBAAqB,MAAM,QAAQ;AAE7D,UAAM,gBAAgB;MACpB,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B,OAAO,EAAE,GAAG,OAAO,kBAAkB,gBAAgB;IACvD;AACA,UAAM,cAAc;MAClB;MACA;IACF;AAEA,WACEA,eAAAA,QAAA,cAAC,kBAAkB,UAAlB,EAA2B,OAAO,cAAA,GAChC,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACC,GAAG;QACJ,qBAAqB,CAAC,CAAC,MAAM;QAC7B,WAAW;MAAA;MAGV,MAAM,4BAA4B,oBAAoB;MACvDA,eAAAA,QAAA,cAAC,uBAAA,EAAuB,GAAG,YAAA,CAAa;IAC1C,CAEJ;EAEJ;EACA,EAAE,WAAW,cAAc,oBAAoB,KAAK;AACtD;AAEO,SAAS,UAAU,EAAE,SAAS,GAAsB;AACzDE,oBAAkB,gCAAgC;AAClD,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,WAAW,EAAE,SAAS,GAA6C;AACjFE,oBAAkB,iCAAiC;AACnD,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,SAAS,EAAE,SAAS,GAA2C;AAC7EE,oBAAkB,+BAA+B;AACjD,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,iBAAiB,aAA2D;AAC1F,QAAM,oBAAgB,2BAAW,iBAAiB;AAElD,QAAM,cAAc;IAClB,GAAG;IACH,OAAO;MACL,GAAG,cAAc;MACjB,GAAG;IACL;EACF;AAEA,SAAOA,eAAAA,QAAA,cAAC,mBAAA,EAAmB,GAAG,YAAA,CAAa;AAC7C;AAEO,IAAM,aAAmC,OAAO,OAAO,aAAa;EACzE;EACA;EACA;EACA,QAAQ;EACR,MAAM;EACN,uBAAuB;AACzB,CAAC;AAEM,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrGE,oBAAkB,oCAAoC;AACtD,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrGE,oBAAkB,oCAAoC;AACtD,SAAOF,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEA,IAAM,uBAAuB;EAC3B,CAAC;IACC;IACA;IACA;IACA,GAAG;EACL,MAAyG;AACvG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,QAAQ;AAC5F,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;QAC/B,WAAW;MAAA;MAEXA,eAAAA,QAAA,cAAC,uBAAA,EAAsB,mBAAA,CAAwC;IACjE,CAEJ;EAEJ;EACA,EAAE,WAAW,uBAAuB,oBAAoB,KAAK;AAC/D;AAEO,IAAM,sBAAqD,OAAO,OAAO,sBAAsB;EACpG,MAAM;EACN,MAAM;AACR,CAAC;AAEM,IAAM,qBAAqB;EAChC,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAA6D;AACnG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,sBAAsB,oBAAoB,KAAK;AAC9D;AAEA,IAAM,kCAA8B,8BAA0B;EAC5D,OAAO,MAAM;EAAC;EACd,SAAS,MAAM;EAAC;EAChB,aAAa,MAAM;EAAC;AACtB,CAAC;AAED,IAAM,wBAAwB;EAC5B,CAAC;IACC;IACA;IACA;IACA,GAAG;EACL,MAAoG;AAClG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,UAAU;MAC5F,qBAAqB,CAAC,CAAC,MAAM;IAC/B,CAAC;AACD,UAAM,2BAA2B,OAAO,OAAO,MAAM,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC;AACpG,UAAM,oBAAoB,qBAAqB,MAAM,QAAQ;AAE7D,UAAM,gBAAgB;MACpB,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B,OAAO,EAAE,GAAG,OAAO,yBAAyB;MAC5C,WAAW;MACX;IACF;AAKA,UAAM,4CAA4C;AAElD,WACEA,eAAAA,QAAA,cAAC,4BAA4B,UAA5B,EAAqC,OAAO,cAAA,GAC3CA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACE,GAAG;QACJ,qBAAqB,CAAC,CAAC,MAAM;MAAA;MAG5B,MAAM,4BAA4B,oBAAoB;MACvDA,eAAAA,QAAA,cAAC,uBAAA,EAAsB,mBAAA,CAAwC;IACjE,CAEJ,CACF;EAEJ;EACA,EAAE,WAAW,wBAAwB,oBAAoB,KAAK;AAChE;AAEO,SAAS,2BACd,aACA;AACA,QAAM,oBAAgB,2BAAW,2BAA2B;AAE5D,QAAM,cAAc;IAClB,GAAG;IACH,OAAO;MACL,GAAG,cAAc;MACjB,GAAG;IACL;EACF;AAEA,SAAOA,eAAAA,QAAA,cAAC,mBAAA,EAAmB,GAAG,YAAA,CAAa;AAC7C;AAEO,IAAM,uBAAuD,OAAO,OAAO,uBAAuB;EACvG;EACA;EACA,uBAAuB;AACzB,CAAC;AAEM,IAAM,mBAAmB;EAC9B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAA2D;AACjG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,oBAAoB,oBAAoB,KAAK;AAC5D;AAEO,IAAM,eAAe;EAC1B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAuD;AAC7F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,MAAM,MAAM;QACZ,OAAO,MAAM;QACb,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AAEO,IAAM,WAAW;EACtB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAmD;AACzF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,YAAY,oBAAoB,KAAK;AACpD;AAEO,IAAM,eAAe;EAC1B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAuD;AAC7F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AAMO,IAAM,UAAU;EACrB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAkD;AACxF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;IACrE;AAEA,WACEA,eAAAA,QAAA,cAAAA,eAAAA,QAAA,UAAA,MACG,sBAAsB,UACtB,MAAM,UACLA,eAAAA,QAAA;MAAC;MAAA;QACC;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B;QACA,WAAW;MAAA;IACb,CAEJ;EAEJ;EACA,EAAE,WAAW,WAAW,oBAAoB,KAAK;AACnD;AUnnBO,IAAM,eAAe;EAC1B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAiE;AAC5F,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,eAAW,0BAA0B,UAAU,SAAS;AACxD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,UAAM,eAAe,MAAM;AACzB,YAAM,OAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;MACF;AAEA,UAAI,SAAS,SAAS;AACpB,eAAO,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM,WAAW,CAAC;MACnE;AACA,aAAO,MAAM,iBAAiB;QAC5B,GAAG;QACH,2BAA2B;QAC3B,wBAAwB;MAC1B,CAAC;IACH;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,UAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;AAC1D,cAAM,YAAY,MAAM,MAAM,OAAO,EAAE,CAAC;MAC1C;AACA,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOA,eAAAA,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AChDO,IAAM,eAAe;EAC1B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAiE;AAC5F,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,eAAW,0BAA0B,UAAU,SAAS;AACxD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,UAAM,eAAe,MAAM;AACzB,YAAM,OAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;MACF;AAEA,UAAI,SAAS,SAAS;AACpB,eAAO,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM,WAAW,CAAC;MACnE;AAEA,aAAO,MAAM,iBAAiB;QAC5B,GAAG;QACH,2BAA2B;QAC3B,wBAAwB;MAC1B,CAAC;IACH;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,UAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;AAC1D,cAAM,YAAY,MAAM,MAAM,OAAO,EAAE,CAAC;MAC1C;AACA,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOA,eAAAA,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AC5CO,IAAM,gBAAgB;EAC3B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAkE;AAC7F,UAAM,EAAE,cAAc,KAAK,gBAAgB,GAAG,KAAK,IAAI;AAEvD,eAAW,0BAA0B,UAAU,UAAU;AACzD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,eAAe;AAEzD,UAAM,eAAe,MAAM,MAAM,QAAQ,EAAE,aAAa,GAAG,eAAe,CAAC;AAC3E,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOA,eAAAA,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA,EAAE,WAAW,iBAAiB,oBAAoB,KAAK;AACzD;ACxBO,IAAM,2BAA2B;EACtC,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAoD;AAC/E,UAAM,EAAE,aAAa,GAAG,KAAK,IAAI;AAEjC,eAAW,0BAA0B,UAAU,uBAAuB;AACtE,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,0BAA0B;AAIpE,UAAM,eAAe,YAAY;AAC/B,qBAAe,eAAe;AAC5B,cAAM,MAAM,yBAAyB,EAAE,aAAa,eAAe,OAAU,CAAC;MAChF;AACA,WAAK,aAAa;IACpB;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOA,eAAAA,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA,EAAE,WAAW,sBAAsB,oBAAoB,KAAK;AAC9D;AGkCA,IAAI,OAAO,WAAW,0BAA0B,aAAa;AAC3D,aAAW,wBAAwB;AACrC;AAEA,IAAM,eAAe;EACnB,MAAM;EACN,SAAS;EACT,aAAa;AACf;AAzEA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAgHO,IAAM,mBAAN,MAAMQ,kBAAiD;EA+H5D,YAAY,SAAiC;AA/HxC,IAAAC,cAAA,MAAA,0BAAA;AAIL,SAAQ,UAAsD;AAC9D,SAAQ,gBAA2C;AACnD,SAAQ,0BAAoE;AAC5E,SAAQ,gBAAqC;AAC7C,SAAQ,kBAAoD;AAC5D,SAAQ,qBAA0D;AAClE,SAAQ,gBAAqC;AAC7C,SAAQ,qBAA+C;AACvD,SAAQ,6BAA+D;AACvE,SAAQ,4BAA6D;AACrE,SAAQ,kBAAyC;AACjD,SAAQ,sBAAsB,oBAAI,IAA6C;AAC/E,SAAQ,sBAAsB,oBAAI,IAA6C;AAC/E,SAAQ,2BAA2B,oBAAI,IAAkD;AACzF,SAAQ,0BAA0B,oBAAI,IAAiD;AACvF,SAAQ,mCAAmC,oBAAI,IAA0D;AACzG,SAAQ,kCAAkC,oBAAI,IAAyD;AACvG,SAAQ,oCAAoC,oBAAI,IAA2D;AAC3G,SAAQ,gCAAgC,oBAAI,IAAuD;AACnG,SAAQ,sBAAsB,oBAAI,IAA8C;AAChF,SAAQ,wBAAwB,oBAAI,IAA+C;AACnF,SAAQ,4BAA4B,oBAAI,IAAmD;AAC3F,SAAQ,uBAAuB,oBAAI,IAA8C;AACjF,SAAQ,4BAA4B,oBAAI,IAA8D;AAEtG,SAAQ,2BAA2B,oBAAI,IAMrC;AACF,SAAQ,kBAAqC,CAAC;AAE9C,IAAAA,cAAA,MAAA,SAAuB,SAAA;AACvB,IAAAA,cAAA,MAAA,OAAA;AACA,IAAAA,cAAA,MAAA,SAAA;AACA,IAAAA,cAAA,MAAA,eAAA;AACA,IAAAA,cAAA,MAAA,WAAY,oBAAoB,CAAA;AAqIhC,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAhSxB,YAAA;AAgS2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,eAAe,IAAA,MAAS;MAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;MACzD;IACF;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAzSxB,YAAA;AAyS2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,eAAe,IAAA,MAAS;MAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;MACzD;IACF;AAEA,SAAA,sBAAsB,IAAI,SAAkE;AAC1F,YAAM,WAAW,MAAG;AAlTxB,YAAA;AAkT2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,oBAAoB,GAAG,IAAA,MAAS;MAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;MAC9D;IACF;AAEA,SAAA,sBAAsB,IAAI,SAAkE;AAC1F,YAAM,WAAW,MAAG;AA3TxB,YAAA;AA2T2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,oBAAoB,GAAG,IAAA,MAAS;MAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;MAC9D;IACF;AAEA,SAAA,uBAAuB,MAAqB;AAC1C,YAAM,WAAW,MAAG;AApUxB,YAAA;AAoU2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,qBAAA,MAA0B;MAAA;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,wBAAwB,QAAQ;MAC/D;IACF;AAEA,SAAA,kCAAkC,MAAqB;AACrD,YAAM,WAAW,MAAG;AA7UxB,YAAA;AA6U2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gCAAA,MAAqC;MAAA;AAC1E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mCAAmC,QAAQ;MAC1E;IACF;AAEA,SAAA,yCAAyC,MAAqB;AAC5D,YAAM,WAAW,MAAG;AAtVxB,YAAA;AAsV2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,uCAAA,MAA4C;MAAA;AACjF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,0CAA0C,QAAQ;MACjF;IACF;AAEA,SAAA,sBAAsB,MAAqB;AACzC,YAAM,WAAW,MAAG;AA/VxB,YAAA;AA+V2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,oBAAA,MAAyB;MAAA;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;MAC9D;IACF;AAEA,SAAA,6BAA6B,MAAqB;AAChD,YAAM,WAAW,MAAG;AAxWxB,YAAA;AAwW2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,2BAAA,MAAgC;MAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;MACrE;IACF;AAEA,SAAA,8BAA8B,MAAqB;AACjD,YAAM,WAAW,MAAG;AAjXxB,YAAA;AAiX2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4BAAA,MAAiC;MAAA;AACtE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;MACtE;IACF;AAEA,SAAA,mBAAmB,MAAqB;AACtC,YAAM,WAAW,MAAG;AA1XxB,YAAA;AA0X2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAA,MAAsB;MAAA;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,mBAAmB,CAAC,OAA8B;AAChD,YAAM,WAAW,MAAG;AAnYxB,YAAA;AAmY2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,EAAA,MAAO;MAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,wBAAwB,YAAY;AAClC,YAAM,WAAW,MAAG;AA5YxB,YAAA;AA4Y2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;MAChE;IACF;AAsFA,SAAO,KAAkB,IAAI,SAAS;AAxexC,UAAA;AA0eI,WAAI,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,IAAI;AACpB,eAAO,KAAK,QAAQ,GAAG,GAAG,IAAI;MAChC,OAAO;AACL,QAAAC,cAAA,MAAK,SAAA,EAAU,GAAG,GAAG,IAAI;MAC3B;IACF;AAEA,SAAO,MAAoB,IAAI,SAAS;AAjf1C,UAAA;AAmfI,WAAI,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,KAAK;AACrB,eAAO,KAAK,QAAQ,IAAI,GAAG,IAAI;MACjC,OAAO;AACL,QAAAA,cAAA,MAAK,SAAA,EAAU,IAAI,GAAG,IAAI;MAC5B;IACF;AAKA,SAAO,cAAc,CAAC,OAAmB;AACvC,WAAK,gBAAgB,KAAK,EAAE;AAI5B,UAAI,KAAK,QAAQ;AACf,aAAK,WAAW;MAClB;IACF;AAKA,SAAO,aAAa,MAAM;AACxB,WAAK,gBAAgB,QAAQ,CAAA,OAAM,GAAG,CAAC;AACvC,WAAK,kBAAkB,CAAC;IAC1B;AAEA,SAAQ,aAAa,CAAC,YAA6D;AACjF,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;MACrD;IACF;AAEA,SAAQ,iBAAiB,CAAC,YAA6D;AArhBzF,UAAA;AAshBI,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;MACrD;AAEA,WAAK,UAAU;AAEf,WAAK,oBAAoB,QAAQ,CAAA,OAAM,GAAG,CAAC;AAC3C,WAAK,yBAAyB,QAAQ,CAAC,kBAAkB,aAAa;AACpE,yBAAiB,oBAAoB,QAAQ,YAAY,QAAQ;MACnE,CAAC;AAED,OAAA,KAAAA,cAAA,MAAK,SAAA,EAAU,SAAS,kBAAkB,QAAQ,MAAlD,OAAA,SAAA,GAAqD,QAAQ,CAAA,aAAY;AAEvE,aAAK,GAAG,UAAU,UAAU,EAAE,QAAQ,KAAK,CAAC;MAC9C,CAAA;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;MACvC;AAEA,UAAI,KAAK,oBAAoB,MAAM;AACjC,gBAAQ,wBAAwB,KAAK,eAAe;MACtD;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,2BAA2B,KAAK,kBAAkB;MAC5D;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;MACvC;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,gBAAgB,KAAK,kBAAkB;MACjD;AAEA,UAAI,KAAK,4BAA4B,MAAM;AACzC,gBAAQ,8BAA8B,KAAK,uBAAuB;MACpE;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,iBAAiB,KAAK,aAAa;MAC7C;AAEA,UAAI,KAAK,+BAA+B,MAAM;AAC5C,gBAAQ,wBAAwB,KAAK,0BAA0B;MACjE;AAEA,UAAI,KAAK,8BAA8B,MAAM;AAC3C,gBAAQ,uBAAuB,KAAK,yBAAyB;MAC/D;AAEA,UAAI,KAAK,oBAAoB,MAAM;AACjC,gBAAQ,aAAa,KAAK,eAAe;MAC3C;AAEA,WAAK,oBAAoB,QAAQ,CAAC,OAAO,SAAS;AAChD,gBAAQ,YAAY,MAAM,KAAK;MACjC,CAAC;AAED,WAAK,oBAAoB,QAAQ,CAAC,OAAO,SAAS;AAChD,gBAAQ,YAAY,MAAM,KAAK;MACjC,CAAC;AAED,WAAK,yBAAyB,QAAQ,CAAC,OAAO,SAAS;AACrD,gBAAQ,iBAAiB,MAAM,KAAK;MACtC,CAAC;AAED,WAAK,wBAAwB,QAAQ,CAAC,OAAO,SAAS;AACpD,gBAAQ,gBAAgB,MAAM,KAAK;MACrC,CAAC;AAED,WAAK,8BAA8B,QAAQ,CAAC,OAAO,SAAS;AAC1D,gBAAQ,sBAAsB,MAAM,KAAK;MAC3C,CAAC;AAED,WAAK,sBAAsB,QAAQ,CAAC,OAAO,SAAS;AAClD,gBAAQ,cAAc,MAAM,KAAK;MACnC,CAAC;AAED,WAAK,0BAA0B,QAAQ,CAAC,OAAO,SAAS;AACtD,gBAAQ,kBAAkB,MAAM,KAAK;MACvC,CAAC;AAED,WAAK,qBAAqB,QAAQ,CAAC,OAAO,SAAS;AACjD,gBAAQ,aAAa,MAAM,KAAK;MAClC,CAAC;AAED,WAAK,0BAA0B,QAAQ,CAAC,OAAO,SAAS;AACtD,gBAAQ,6BAA6B,MAAM,KAAK;MAClD,CAAC;AAKD,UAAI,OAAO,KAAK,QAAQ,WAAW,aAAa;AAC9C,QAAAA,cAAA,MAAK,SAAA,EAAU,KAAK,YAAY,QAAQ,OAAO;MACjD;AAEA,WAAK,WAAW;AAChB,aAAO,KAAK;IACd;AAgFA,SAAA,0BAA0B,OAAO,UAA8B;AAC7D,YAAM,UAAU,MAAMC,iBAAA,MAAK,4BAAA,iBAAA,EAAL,KAAA,IAAA;AAEtB,UAAI,WAAW,6BAA6B,SAAS;AACnD,eAAQ,QAAgB,wBAAwB,KAAK;MACvD;IACF;AAEA,SAAA,gCAAgC,OAAO,WAA2C;AAChF,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,8BAA8B,MAAM;MAC1D,OAAO;AACL,eAAO,QAAQ,OAAO;MACxB;IACF;AAKA,SAAA,YAAY,CAAC,WAA2C;AACtD,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,UAAU,MAAM;MACtC,OAAO;AACL,eAAO,QAAQ,OAAO;MACxB;IACF;AAEA,SAAA,aAAa,CAAC,UAAwB;AACpC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,WAAW,KAAK;MAC/B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,MAAM;AAClB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY;MAC3B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,0BAA0B,CAAC,UAAqC;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,KAAK;MAC5C,OAAO;AACL,aAAK,kBAAkB;MACzB;IACF;AAEA,SAAA,2BAA2B,MAAM;AAC/B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB;MACxC,OAAO;AACL,aAAK,kBAAkB;MACzB;IACF;AAEA,SAAA,6BAA6B,CAAC,UAAwC;AACpE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,2BAA2B,KAAK;MAC/C,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,8BAA8B,MAAM;AAClC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,4BAA4B;MAC3C,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,gCAAgC,CAAC,UAAkD;AACjF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,8BAA8B,KAAK;MAClD,OAAO;AACL,aAAK,0BAA0B;MACjC;IACF;AAEA,SAAA,iCAAiC,MAAM;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,+BAA+B;MAC9C,OAAO;AACL,aAAK,0BAA0B;MACjC;IACF;AAEA,SAAA,mBAAmB,CAAC,UAA8B;AAChD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB,KAAK;MACrC,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,oBAAoB,MAAM;AACxB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB;MACjC,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,kBAAkB,CAAC,UAA6B;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,KAAK;MACpC,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,mBAAmB,MAAM;AACvB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB;MAChC,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,0BAA0B,CAAC,UAAqC;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,KAAK;MAC5C,OAAO;AACL,aAAK,6BAA6B;MACpC;IACF;AAEA,SAAA,2BAA2B,MAAM;AAC/B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB;MACxC,OAAO;AACL,aAAK,6BAA6B;MACpC;IACF;AAEA,SAAA,yBAAyB,CAAC,UAAoC;AAC5D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,uBAAuB,KAAK;MAC3C,OAAO;AACL,aAAK,4BAA4B;MACnC;IACF;AAEA,SAAA,0BAA0B,MAAM;AAC9B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB;MACvC,OAAO;AACL,aAAK,4BAA4B;MACnC;IACF;AAEA,SAAA,eAAe,CAAC,UAA0B;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,aAAa,KAAK;MACjC,OAAO;AACL,aAAK,kBAAkB;MACzB;IACF;AAEA,SAAA,gBAAgB,MAAM;AACpB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc;MAC7B,OAAO;AACL,aAAK,kBAAkB;MACzB;IACF;AAEA,SAAA,aAAa,CAAC,UAAwB;AACpC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,WAAW,KAAK;MAC/B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,MAAM;AAClB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY;MAC3B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,CAAC,MAAsB,UAAwB;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY,MAAM,KAAK;MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;MAC1C;IACF;AAEA,SAAA,gBAAgB,CAAC,SAAyB;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,IAAI;MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;MACtC;IACF;AAEA,SAAA,cAAc,CAAC,MAAsB,UAAwB;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY,MAAM,KAAK;MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;MAC1C;IACF;AAEA,SAAA,gBAAgB,CAAC,SAAyB;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,IAAI;MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;MACtC;IACF;AAEA,SAAA,mBAAmB,CAAC,MAAsB,UAA6B;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB,MAAM,KAAK;MAC3C,OAAO;AACL,aAAK,yBAAyB,IAAI,MAAM,KAAK;MAC/C;IACF;AAEA,SAAA,qBAAqB,CAAC,SAAyB;AAC7C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,mBAAmB,IAAI;MACtC,OAAO;AACL,aAAK,yBAAyB,OAAO,IAAI;MAC3C;IACF;AAEA,SAAA,2BAA2B,CAAC,MAAsB,UAAqC;AACrF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB,MAAM,KAAK;MACnD,OAAO;AACL,aAAK,iCAAiC,IAAI,MAAM,KAAK;MACvD;IACF;AAEA,SAAA,6BAA6B,CAAC,SAAyB;AACrD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,2BAA2B,IAAI;MAC9C,OAAO;AACL,aAAK,iCAAiC,OAAO,IAAI;MACnD;IACF;AAEA,SAAA,0BAA0B,CAAC,MAAsB,UAAoC;AACnF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,MAAM,KAAK;MAClD,OAAO;AACL,aAAK,gCAAgC,IAAI,MAAM,KAAK;MACtD;IACF;AAEA,SAAA,4BAA4B,CAAC,SAAyB;AACpD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,0BAA0B,IAAI;MAC7C,OAAO;AACL,aAAK,gCAAgC,OAAO,IAAI;MAClD;IACF;AAEA,SAAA,4BAA4B,CAAC,MAAsB,UAAsC;AACvF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,0BAA0B,MAAM,KAAK;MACpD,OAAO;AACL,aAAK,kCAAkC,IAAI,MAAM,KAAK;MACxD;IACF;AAEA,SAAA,8BAA8B,CAAC,SAAyB;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,4BAA4B,IAAI;MAC/C,OAAO;AACL,aAAK,kCAAkC,OAAO,IAAI;MACpD;IACF;AAEA,SAAA,8CAA8C,MAAM;AAClD,YAAM,WAAW,MAAG;AAv+BxB,YAAA;AAu+B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4CAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,+CAA+C,QAAQ;MACtF;IACF;AAEA,SAAA,wBAAwB,CAAC,MAAsB,UAAkC;AAC/E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,sBAAsB,MAAM,KAAK;MAChD,OAAO;AACL,aAAK,8BAA8B,IAAI,MAAM,KAAK;MACpD;IACF;AAEA,SAAA,0BAA0B,CAAC,SAAyB;AAClD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,IAAI;MAC3C,OAAO;AACL,aAAK,8BAA8B,OAAO,IAAI;MAChD;IACF;AAEA,SAAA,kBAAkB,CAAC,MAAsB,oBAAsC;AAC7E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,MAAM,eAAe;MACpD,OAAO;AACL,aAAK,wBAAwB,IAAI,MAAM,eAAe;MACxD;IACF;AAEA,SAAA,oBAAoB,CAAC,SAAyB;AAC5C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB,IAAI;MACrC,OAAO;AACL,aAAK,wBAAwB,OAAO,IAAI;MAC1C;IACF;AAEA,SAAA,gBAAgB,CAAC,MAAsB,UAA0B;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,MAAM,KAAK;MACxC,OAAO;AACL,aAAK,sBAAsB,IAAI,MAAM,KAAK;MAC5C;IACF;AAEA,SAAA,kBAAkB,CAAC,SAAyB;AAC1C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,IAAI;MACnC,OAAO;AACL,aAAK,sBAAsB,OAAO,IAAI;MACxC;IACF;AAEA,SAAA,oBAAoB,CAAC,MAAsB,UAA8B;AACvE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB,MAAM,KAAK;MAC5C,OAAO;AACL,aAAK,0BAA0B,IAAI,MAAM,KAAK;MAChD;IACF;AAEA,SAAA,sBAAsB,CAAC,SAAyB;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,oBAAoB,IAAI;MACvC,OAAO;AACL,aAAK,0BAA0B,OAAO,IAAI;MAC5C;IACF;AAEA,SAAA,eAAe,CAAC,MAAsB,UAA+B;AACnE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,aAAa,MAAM,KAAK;MACvC,OAAO;AACL,aAAK,qBAAqB,IAAI,MAAM,KAAK;MAC3C;IACF;AAEA,SAAA,iBAAiB,CAAC,SAA+B;AAC/C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,eAAe,IAAI;MAClC,OAAO;AACL,aAAK,qBAAqB,OAAO,IAAI;MACvC;IACF;AAEA,SAAA,+BAA+B,CAAC,MAAsB,UAAyC;AAC7F,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,6BAA6B,MAAM,KAAK;MACvD,OAAO;AACL,aAAK,0BAA0B,IAAI,MAAM,KAAK;MAChD;IACF;AAEA,SAAA,iCAAiC,CAAC,SAAyB;AACzD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,+BAA+B,IAAI;MAClD,OAAO;AACL,aAAK,0BAA0B,OAAO,IAAI;MAC5C;IACF;AAEA,SAAA,cAAc,CAAC,aAAoD;AACjE,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,YAAY,QAAQ;MAC1C,OAAO;AACL,cAAM,cAAc,MAAM;AAnlChC,cAAA;AAolCQ,gBAAM,mBAAmB,KAAK,yBAAyB,IAAI,QAAQ;AACnE,cAAI,kBAAkB;AACpB,aAAA,KAAA,iBAAiB,sBAAjB,OAAA,SAAA,GAAA,KAAA,gBAAA;AACA,iBAAK,yBAAyB,OAAO,QAAQ;UAC/C;QACF;AACA,aAAK,yBAAyB,IAAI,UAAU,EAAE,aAAa,mBAAmB,OAAU,CAAC;AACzF,eAAO;MACT;IACF;AAEA,SAAA,WAAW,CAAC,OAAe;AACzB,YAAM,WAAW,MAAG;AAhmCxB,YAAA;AAgmC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,SAAS,EAAA;MAAA;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,YAAY,QAAQ;MACnD;IACF;AAEA,SAAA,mBAAmB,UAAU,SAAgD;AAC3E,YAAM,WAAW,MAAG;AAzmCxB,YAAA;AAymC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,GAAG,IAAA;MAAA;AACzD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;MACF;IACF;AAEA,SAAA,mBAAmB,OAAO,SAAiC;AACzD,YAAM,WAAW,MAAG;AAnnCxB,YAAA;AAmnC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,IAAA;MAAA;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;MACF;IACF;AAEA,SAAA,mBAAmB,OAAO,SAAiC;AACzD,YAAM,WAAW,MAAG;AA7nCxB,YAAA;AA6nC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,IAAA;MAAA;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;MACF;IACF;AAEA,SAAA,wBAAwB,YAAY;AAClC,YAAM,WAAW,MAAG;AAvoCxB,YAAA;AAuoC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAC9D;MACF;IACF;AAEA,SAAA,wBAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AAjpCxB,YAAA;AAipC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;MAChE;IACF;AAEA,SAAA,wBAAwB,MAAM;AAC5B,YAAM,WAAW,MAAG;AA1pCxB,YAAA;AA0pC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;MAChE;IACF;AAEA,SAAA,yBAAyB,MAAM;AAC7B,YAAM,WAAW,MAAG;AAnqCxB,YAAA;AAmqC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,uBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;MACjE;IACF;AAEA,SAAA,gCAAgC,YAAY;AAC1C,YAAM,WAAW,MAAG;AA5qCxB,YAAA;AA4qC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,8BAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,iCAAiC,QAAQ;AACtE;MACF;IACF;AAEA,SAAA,+BAA+B,YAAY;AACzC,YAAM,WAAW,MAAG;AAtrCxB,YAAA;AAsrC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,6BAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gCAAgC,QAAQ;AACrE;MACF;IACF;AAEA,SAAA,qBAAqB,YAAY;AAC/B,YAAM,WAAW,MAAG;AAhsCxB,YAAA;AAgsC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,mBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;AAC3D;MACF;IACF;AAEA,SAAA,yBAAyB,OAAO,WAAqD;AAzsCvF,UAAA;AA0sCI,YAAM,WAAW,MAAG;AA1sCxB,YAAAC;AA0sC2B,gBAAAA,MAAA,KAAK,YAAL,OAAA,SAAAA,IAAc,uBAAuB,MAAA;MAAA;AAC5D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAK,KAAA,SAAS,MAAT,OAAA,SAAA,GAAY,MAAM,MAAM;QAQ7B,CAAA;MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;MACjE;IACF;AAEA,SAAA,6BAA6B,OAC3B,YACA,WACkB;AA7tCtB,UAAA;AA8tCI,YAAM,WAAW,MAAG;AA9tCxB,YAAAA;AA8tC2B,gBAAAA,MAAA,KAAK,YAAL,OAAA,SAAAA,IAAc,2BAA2B,YAAY,MAAA;MAAA;AAC5E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAK,KAAA,SAAS,MAAT,OAAA,SAAA,GAAY,MAAM,MAAM;QAQ7B,CAAA;MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;MACrE;IACF;AAEA,SAAA,8BAA8B,OAAO,WAA8C;AACjF,YAAM,WAAW,MAAG;AA/uCxB,YAAA;AA+uC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4BAA4B,MAAA;MAAA;AACjE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;MACtE;IACF;AAEA,SAAA,2BAA2B,OAAO,WAA4C;AAC5E,YAAM,WAAW,MAAG;AAxvCxB,YAAA;AAwvC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,yBAAyB,MAAA;MAAA;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,4BAA4B,QAAQ;MACnE;IACF;AAEA,SAAA,iCAAiC,OAAO,WAAkD;AACxF,YAAM,WAAW,MAAG;AAjwCxB,YAAA;AAiwC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,+BAA+B,MAAA;MAAA;AACpE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kCAAkC,QAAQ;MACzE;IACF;AAEA,SAAA,4BAA4B,OAAO,WAA6C;AAC9E,YAAM,WAAW,MAAG;AA1wCxB,YAAA;AA0wC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,0BAA0B,MAAA;MAAA;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,6BAA6B,QAAQ;MACpE;IACF;AAEA,SAAA,uBAAuB,OAAO,WAA4C;AACxE,YAAM,WAAW,MAAG;AAnxCxB,YAAA;AAmxC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,qBAAqB,MAAA;MAAA;AAC1D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,wBAAwB,QAAQ;MAC/D;IACF;AAEA,SAAA,+BAA+B,OAAO,WAA+C;AACnF,YAAM,UAAU,MAAMD,iBAAA,MAAK,4BAAA,iBAAA,EAAL,KAAA,IAAA;AACtB,aAAO,QAAQ,6BAA6B,MAAM;IACpD;AAEA,SAAA,qBAAqB,OAAO,WAA2E;AACrG,YAAM,WAAW,MAAG;AAjyCxB,YAAA;AAiyC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,mBAAmB,MAAA;MAAA;AACxD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;MAC7D;IACF;AAEA,SAAA,kBAAkB,OAAO,mBAAiE;AACxF,YAAM,WAAW,MAAG;AA1yCxB,YAAA;AA0yC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAgB,cAAA;MAAA;AACrD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mBAAmB,QAAQ;MAC1D;IACF;AAEA,SAAA,eAAe,OAAO,WAAiE;AACrF,YAAM,WAAW,MAAG;AAnzCxB,YAAA;AAmzC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,aAAa,MAAA;MAAA;AAClD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gBAAgB,QAAQ;MACvD;IACF;AAEA,SAAA,UAAU,UAAU,SAAuC;AACzD,YAAM,WAAW,MAAG;AA5zCxB,YAAA;AA4zC2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,QAAQ,GAAG,IAAA;MAAA;AAChD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,WAAW,QAAQ;MAClD;IACF;AAllCE,UAAM,EAAE,QAAQ,MAAM,eAAe,IAAI,WAAW,CAAC;AACrD,IAAAE,cAAA,MAAK,iBAAkB,cAAA;AACvB,IAAAA,cAAA,MAAK,WAAY,WAAA,OAAA,SAAA,QAAS,QAAA;AAC1B,IAAAA,cAAA,MAAK,SAAU,WAAA,OAAA,SAAA,QAAS,MAAA;AACxB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAO,UAAU,IAAI,YAAY;AAEtC,QAAI,CAAC,KAAK,QAAQ,aAAa;AAC7B,WAAK,QAAQ,cAAc;IAC7B;AACA,IAAAH,cAAA,MAAK,SAAA,EAAU,KAAK,YAAY,QAAQ,SAAS;AACjD,IAAAA,cAAA,MAAK,SAAA,EAAU,cAAc,YAAY,QAAQ,CAAA,WAAWG,cAAA,MAAK,SAAU,MAAA,CAAO;AAElF,QAAIH,cAAA,MAAK,eAAA,GAAiB;AACxB,WAAK,KAAK,YAAY;IACxB;EACF;EArGA,IAAI,iBAAyB;AAC3B,WAAOA,cAAA,MAAK,eAAA;EACd;EAEA,IAAI,SAAkB;AAhKxB,QAAA;AAiKI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,WAAU;EACjC;EAEA,IAAI,SAAsB;AApK5B,QAAA;AAwKI,QAAI,CAAC,KAAK,SAAS;AACjB,aAAOA,cAAA,MAAK,OAAA;IACd;AACA,aACE,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;;;;;;KAOb,KAAK,QAAQ,SAAS,UAAU;EAErC;EAIA,OAAO,oBAAoB,SAAiC;AAK1D,QACE,CAAC,UAAU,KACX,CAACA,cAAA,MAAK,SAAA,KACL,QAAQ,SAASA,cAAA,MAAK,SAAA,EAAU,UAAU,QAAQ;IAEnDA,cAAA,MAAK,SAAA,EAAU,mBAAmB,QAAQ,gBAC1C;AACA,MAAAG,cAAA,MAAK,WAAY,IAAIL,kBAAgB,OAAO,CAAA;IAC9C;AACA,WAAOE,cAAA,MAAK,SAAA;EACd;EAEA,OAAO,gBAAgB;AACrB,IAAAG,cAAA,MAAK,WAAY,IAAA;EACnB;EAEA,IAAI,SAAiB;AAGnB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgBH,cAAA,MAAK,OAAA,GAAS,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;IACxE;AACA,QAAI,OAAOA,cAAA,MAAK,OAAA,MAAY,YAAY;AACtC,aAAO,aAAa,MAAM,6CAA6C;IACzE;AACA,WAAOA,cAAA,MAAK,OAAA,KAAW;EACzB;EAEA,IAAI,WAAmB;AAGrB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgBA,cAAA,MAAK,SAAA,GAAW,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;IAC1E;AACA,QAAI,OAAOA,cAAA,MAAK,SAAA,MAAc,YAAY;AACxC,aAAO,aAAa,MAAM,6CAA6C;IACzE;AACA,WAAOA,cAAA,MAAK,SAAA,KAAa;EAC3B;;;;;;EAOO,qBAAmD,KAAqC;AA3OjG,QAAA,IAAA;AA4OI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,yBAAuB,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,qBAAqB,GAAA,IAAO,KAAK,QAAQ,GAAG;EACxG;EAsBA,IAAI,cAAc;AAnQpB,QAAA;AAoQI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAe,KAAK,QAAQ,eAAe;EAClE;EAEA,IAAI,eAAe;AAvQrB,QAAA;AAwQI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,IAAI,cAAc;AA3QpB,QAAA;AA4QI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAe;EACtC;EAEA,IAAI,oBAAoB;AA/Q1B,QAAA;AAgRI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAqB,KAAK,QAAQ,mBAAmB;EAC5E;EAEA,IAAI,cAAc;AAGhB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,KAAK,QAAQ,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,KAAK;IACvF;AACA,QAAI,OAAO,KAAK,QAAQ,gBAAgB,YAAY;AAClD,aAAO,aAAa,MAAM,6CAA6C;IACzE;AACA,WAAO;EACT;EA8HA,MAAM,cAAwE;AA3ZhF,QAAA;AA4ZI,QAAI,KAAK,SAAS,aAAa,KAAK,QAAQ;AAC1C;IACF;AAYA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,0BAA0BA,cAAA,MAAK,eAAA;AACtC,aAAO,oBAAoB,KAAK;AAChC,aAAO,iBAAiB,KAAK;IAC/B;AAEA,QAAI;AACF,UAAI,KAAK,OAAO;AAEd,YAAI;AAEJ,YAAI,cAAyE,KAAK,KAAK,GAAG;AAExF,cAAI,IAAI,KAAK,MAAMA,cAAA,MAAK,eAAA,GAAiB;YACvC,UAAU,KAAK;YACf,QAAQ,KAAK;UACf,CAAQ;AAER,eAAK,WAAW,CAAC;AACjB,gBAAM,EAAE,KAAK,KAAK,OAAO;QAC3B,OAAO;AAEL,cAAI,KAAK;AACT,cAAI,CAAC,EAAE,QAAQ;AACb,iBAAK,WAAW,CAAC;AACjB,kBAAM,EAAE,KAAK,KAAK,OAAO;UAC3B;QACF;AAEA,eAAO,QAAQ;MACjB,WAAW,CAAC,uBAAuB;AAEjC,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,kBAAkB;YACtB,GAAG,KAAK;YACR,gBAAgBA,cAAA,MAAK,eAAA;YACrB,UAAU,KAAK;YACf,QAAQ,KAAK;YACb,OAAO,KAAK,QAAQ;UACtB,CAAC;QACH;AAEA,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,IAAI,MAAM,+DAA+D;QACjF;AAEA,aAAK,WAAW,OAAO,KAAK;AAC5B,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO;MACtC;AAEA,WAAI,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,QAAQ;AACxB,eAAO,KAAK,eAAe,OAAO,KAAK;MACzC;AACA;IACF,SAAS,KAAK;AACZ,YAAM,QAAQ;AACd,MAAAA,cAAA,MAAK,SAAA,EAAU,KAAK,YAAY,QAAQ,OAAO;AAC/C,cAAQ,MAAM,MAAM,SAAS,MAAM,WAAW,KAAK;AACnD;IACF;EACF;EAuJA,IAAI,UAAU;AA7nBhB,QAAA;AA8nBI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,IAAI,SAAqC;AACvC,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IAEtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,UAAU;AACZ,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,OAAO;AACT,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,eAAe;AACjB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,YAAY;AACd,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,0BAA+B;AACjC,QAAI,KAAK,SAAS;AAChB,aAAQ,KAAK,QAAgB;IAE/B,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,aAAsB;AACxB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,UAAgD;AA3rBtD,QAAA;AA4rBI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,IAAI,UAAwC;AA/rB9C,QAAA;AAgsBI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,8BAA8B,MAAiB;AAC7C,QAAI,KAAK,WAAW,gCAAgC,KAAK,SAAS;AAC/D,WAAK,QAAgB,2BAA2B,IAAI;IACvD,OAAO;AACL,aAAO;IACT;EACF;AA0nBF;AA7qCE,UAAA,oBAAA,QAAA;AACA,UAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AACA,kBAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AA6BO,YAAA,oBAAA,QAAA;AAvEF,6BAAA,oBAAA,QAAA;AAoSL,oBAAe,WAAiD;AAC9D,SAAO,IAAI,QAA6C,CAAA,YAAW;AAEjE,SAAK,YAAY,MAAM,QAAQ,KAAK,OAAQ,CAAC;EAC/C,CAAC;AACH;AAlOAD,cAvEW,kBAuEJ,SAAA;AAvEF,IAAM,kBAAN;AD9FA,SAAS,qBAAqB,OAA6B;AAChE,QAAM,EAAE,wBAAwB,cAAc,SAAS,IAAI;AAC3D,QAAM,EAAE,iBAAiB,OAAO,YAAY,IAAI,yBAAyB,sBAAsB;AAE/F,QAAM,CAAC,OAAO,QAAQ,IAAIT,eAAAA,QAAM,SAAoC;IAClE,QAAQ,MAAM;IACd,SAAS,MAAM;IACf,MAAM,MAAM;IACZ,cAAc,MAAM;EACtB,CAAC;AAEDA,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM,YAAY,CAAA,MAAK,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;EAClD,GAAG,CAAC,CAAC;AAEL,QAAM,eAAe,YAAY,MAAM,QAAQ,OAAO,YAAY;AAClE,QAAM,WAAWA,eAAAA,QAAM;IACrB,OAAO,EAAE,OAAO,MAAM;IACtB;;MAEE;IACF;EACF;AACA,QAAM,YAAYA,eAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC;AAE/E,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,QAAM,UAAUA,eAAAA,QAAM,QAAQ,MAAM;AAClC,UAAM,QAAQ;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;AACA,WAAO,EAAE,MAAM;EACjB,GAAG,CAAC,WAAW,eAAe,QAAQ,OAAO,OAAO,SAAS,SAAS,uBAAuB,iBAAA,OAAA,SAAA,cAAe,KAAK,CAAC;AAElH,QAAM,aAAaA,eAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC;AACjF,QAAM,UAAUA,eAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;AACrE,QAAM,kBAAkBA,eAAAA,QAAM,QAAQ,MAAM;AAC1C,UAAM,QAAQ;MACZ;IACF;AACA,WAAO,EAAE,MAAM;EACjB,GAAG,CAAC,OAAO,YAAY,CAAC;AAExB;;IAEEA,eAAAA,QAAA,cAAC,uBAAuB,UAAvB,EAAgC,OAAO,SAAA,GACtCA,eAAAA,QAAA,cAAC,cAAc,UAAd,EAAuB,OAAO,UAAA,GAC7BA,eAAAA,QAAA,cAAC,eAAe,UAAf,EAAwB,OAAO,WAAA,GAC9BA,eAAAA,QAAA,cAAC,sBAAA,EAAsB,GAAG,gBAAgB,MAAA,GACxCA,eAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,QAAA,GAC3BA,eAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,QAAA,GAAU,QAAS,CAClD,CACF,CACF,CACF,CACF;;AAEJ;AAEA,IAAM,2BAA2B,CAAC,YAAoC;AACpE,QAAM,qBAAqBA,eAAAA,QAAM,OAAO,gBAAgB,oBAAoB,OAAO,CAAC;AACpF,QAAM,CAAC,aAAa,cAAc,IAAIA,eAAAA,QAAM,SAAS,mBAAmB,QAAQ,MAAM;AAEtFA,iBAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,mBAAmB,QAAQ,wBAAwB,EAAE,YAAY,QAAQ,WAAW,CAAC;EAC5F,GAAG,CAAC,QAAQ,UAAU,CAAC;AAEvBA,iBAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,mBAAmB,QAAQ,wBAAwB,EAAE,QAAQ,CAAC;EACrE,GAAG,CAAC,QAAQ,YAAY,CAAC;AAEzBA,iBAAAA,QAAM,UAAU,MAAM;AACpB,uBAAmB,QAAQ,GAAG,UAAU,cAAc;AACtD,WAAO,MAAM;AACX,UAAI,mBAAmB,SAAS;AAC9B,2BAAmB,QAAQ,IAAI,UAAU,cAAc;MACzD;AACA,sBAAgB,cAAc;IAChC;EACF,GAAG,CAAC,CAAC;AAEL,SAAO,EAAE,iBAAiB,mBAAmB,SAAS,YAAY;AACpE;ADlHA,SAAS,kBAAkB,OAA2B;AACpD,QAAM,EAAE,cAAc,UAAU,wCAAwC,GAAG,2BAA2B,IAAI;AAC1G,QAAM,EAAE,iBAAiB,IAAI,OAAO,qBAAqB,IAAI;AAE7D,MAAI,CAAC,wBAAwB,CAAC,wCAAwC;AACpE,QAAI,CAAC,gBAAgB;AACnB,mBAAa,gCAAgC;IAC/C,WAAW,kBAAkB,CAAC,iBAAiB,cAAc,GAAG;AAC9D,mBAAa,gCAAgC,EAAE,KAAK,eAAe,CAAC;IACtE;EACF;AAEA,SACEA,eAAAA,QAAA;IAAC;IAAA;MACC;MACA,wBAAwB;IAAA;IAEvB;EACH;AAEJ;AAEA,IAAM,gBAAgB,6BAA6B,mBAAmB,iBAAiB,2BAA2B;AAElH,cAAc,cAAc;AfrB5B,uBAAuB,EAAE,aAAa,qBAAa,CAAC;AACpD,kCAAkC,oBAAY;", "names": ["React", "useState", "useLayoutEffect", "useEffect", "useDebugValue", "packageName", "customMessages", "__export", "config", "has", "import_react", "import_react", "SWRConfig", "import_react", "noop", "cache", "React", "mutate", "import_react", "React", "_config", "use", "middleware", "useSWR", "noop", "UNDEFINED", "OBJECT", "isUndefined", "isFunction", "table", "isObjectType", "counter", "stableHash", "serialize", "use", "React", "cache", "compare", "cachedData", "SWRConfig", "import_react", "import_shim", "noop", "UNDEFINED", "OBJECT", "isUndefined", "isFunction", "table", "isObjectType", "counter", "stableHash", "serialize", "mutate", "import_react", "has", "dequal", "import_react", "React", "__export", "default", "React", "default", "undefinedPaginatedResource", "<PERSON><PERSON><PERSON>", "dequal", "useRef", "useCallback", "<PERSON><PERSON><PERSON>", "import_react", "import_react", "import_react", "useAssertWrappedByClerkProvider", "has", "React", "eventMethodCalled", "useClientContext", "__typeError", "__access<PERSON>heck", "__privateGet", "__privateAdd", "__privateSet", "__privateMethod", "errorThrower", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "React", "children", "logErrorInDevMode", "child", "props", "isReorderItem", "isExternalLink", "useState", "_IsomorphicClerk", "__privateAdd", "__privateGet", "__privateMethod", "_a", "__privateSet"]}