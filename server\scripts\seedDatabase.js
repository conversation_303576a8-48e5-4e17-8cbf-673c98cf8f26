const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Post = require('../models/Post');

// Sample data
const sampleUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'admin',
    bio: 'Passionate blogger and tech enthusiast. Love writing about web development, AI, and digital trends.',
    blogTitle: 'Tech Insights',
    blogDescription: 'Exploring the latest in technology and web development',
    socialLinks: {
      twitter: 'https://twitter.com/johndoe',
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe',
      website: 'https://johndoe.dev'
    },
    isVerified: true
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    bio: 'Digital marketing expert and content creator. Helping businesses grow through strategic content.',
    blogTitle: 'Marketing Mastery',
    blogDescription: 'Digital marketing strategies and content creation tips',
    socialLinks: {
      twitter: 'https://twitter.com/janesmith',
      linkedin: 'https://linkedin.com/in/janesmith'
    },
    isVerified: true
  },
  {
    name: 'Mike Johnson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    bio: 'Startup founder and entrepreneur. Sharing insights about building successful businesses.',
    blogTitle: 'Startup Stories',
    blogDescription: 'Entrepreneurship, startups, and business growth',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/mikejohnson',
      website: 'https://mikejohnson.co'
    },
    isVerified: true
  }
];

const samplePosts = [
  {
    title: 'Getting Started with React and Vite',
    content: `# Getting Started with React and Vite

React has been the go-to library for building user interfaces for years, and with the introduction of Vite, the development experience has become even better. In this post, we'll explore how to set up a React project with Vite and why it's a game-changer for modern web development.

## What is Vite?

Vite is a build tool that aims to provide a faster and leaner development experience for modern web projects. It consists of two major parts:

- A dev server that provides rich feature enhancements over native ES modules
- A build command that bundles your code with Rollup

## Setting Up React with Vite

Getting started is incredibly simple:

\`\`\`bash
npm create vite@latest my-react-app -- --template react
cd my-react-app
npm install
npm run dev
\`\`\`

## Key Benefits

1. **Lightning Fast HMR**: Hot Module Replacement is incredibly fast
2. **Native ES Modules**: No bundling during development
3. **Optimized Build**: Production builds are optimized with Rollup
4. **Rich Plugin Ecosystem**: Extensive plugin support

## Conclusion

Vite represents the future of frontend tooling, offering unparalleled speed and developer experience. If you haven't tried it yet, now is the perfect time to make the switch!`,
    categories: ['React', 'JavaScript', 'Web Development'],
    tags: ['react', 'vite', 'frontend', 'javascript', 'tutorial'],
    status: 'published',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
      alt: 'React and Vite development setup',
      caption: 'Modern React development with Vite'
    },
    metaTitle: 'Getting Started with React and Vite - Complete Guide',
    metaDescription: 'Learn how to set up React with Vite for lightning-fast development. Complete guide with examples and best practices.',
    keywords: ['react', 'vite', 'javascript', 'frontend', 'web development']
  },
  {
    title: 'The Future of AI in Content Creation',
    content: `# The Future of AI in Content Creation

Artificial Intelligence is revolutionizing how we create, edit, and optimize content. From automated writing assistants to sophisticated SEO optimization tools, AI is becoming an indispensable part of the content creator's toolkit.

## Current State of AI Content Tools

Today's AI tools can:

- Generate blog post outlines and drafts
- Optimize content for SEO
- Create social media posts
- Generate meta descriptions and titles
- Suggest improvements for readability

## Popular AI Writing Tools

### GPT-based Tools
- ChatGPT for conversational content
- Jasper for marketing copy
- Copy.ai for various content types

### Specialized Tools
- Grammarly for grammar and style
- Hemingway for readability
- Surfer SEO for optimization

## Best Practices for AI-Assisted Writing

1. **Use AI as a starting point**, not the final product
2. **Always fact-check** AI-generated content
3. **Maintain your unique voice** and perspective
4. **Edit and refine** AI suggestions
5. **Combine AI efficiency** with human creativity

## The Road Ahead

As AI continues to evolve, we can expect:

- More sophisticated understanding of context and nuance
- Better integration with content management systems
- Real-time collaboration between humans and AI
- Improved personalization capabilities

## Conclusion

AI is not here to replace content creators but to augment their capabilities. The future belongs to those who can effectively combine human creativity with AI efficiency.`,
    categories: ['AI', 'Content Marketing', 'Technology'],
    tags: ['ai', 'content-creation', 'writing', 'automation', 'future'],
    status: 'published',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
      alt: 'AI and content creation concept',
      caption: 'The intersection of AI and creative writing'
    },
    metaTitle: 'The Future of AI in Content Creation - Trends and Tools',
    metaDescription: 'Explore how AI is transforming content creation. Learn about current tools, best practices, and future trends in AI-assisted writing.',
    keywords: ['ai content creation', 'artificial intelligence', 'writing tools', 'content marketing']
  },
  {
    title: 'Building a Successful Startup: Lessons from the Trenches',
    content: `# Building a Successful Startup: Lessons from the Trenches

Starting a company is one of the most challenging yet rewarding experiences an entrepreneur can undertake. After building and scaling multiple startups, I've learned valuable lessons that I wish I knew when I started.

## The Foundation: Problem-Solution Fit

Before you write a single line of code or create a business plan, you need to understand:

- **What problem are you solving?**
- **Who has this problem?**
- **How painful is this problem?**
- **What solutions currently exist?**

## Key Lessons Learned

### 1. Start Small, Think Big

Don't try to build everything at once. Focus on your core value proposition and execute it flawlessly.

### 2. Customer Development is Everything

Talk to your customers constantly. They'll tell you what to build, how to improve, and where to focus your efforts.

### 3. Cash Flow is King

Revenue solves most problems. Focus on generating cash flow as early as possible, even if it's not scalable initially.

### 4. Team Matters More Than Ideas

A great team can execute a mediocre idea better than a mediocre team can execute a great idea.

### 5. Embrace Failure Fast

Fail quickly, learn from it, and iterate. The faster you can validate or invalidate assumptions, the better.

## Common Pitfalls to Avoid

- Building in isolation without customer feedback
- Perfectionism over progress
- Hiring too fast or too slow
- Ignoring unit economics
- Underestimating the importance of marketing

## The Startup Toolkit

Essential tools for modern startups:

- **Analytics**: Google Analytics, Mixpanel
- **Customer Support**: Intercom, Zendesk
- **Project Management**: Notion, Linear
- **Communication**: Slack, Discord
- **Development**: GitHub, Vercel

## Conclusion

Building a startup is a marathon, not a sprint. Focus on solving real problems for real people, build a great team, and never stop learning from your customers.

Remember: every successful company started as someone's crazy idea. The difference between success and failure often comes down to execution and persistence.`,
    categories: ['Entrepreneurship', 'Startup', 'Business'],
    tags: ['startup', 'entrepreneurship', 'business', 'lessons', 'advice'],
    status: 'published',
    featuredImage: {
      url: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=400&fit=crop',
      alt: 'Startup team working together',
      caption: 'Building the future, one startup at a time'
    },
    metaTitle: 'Building a Successful Startup: Essential Lessons and Tips',
    metaDescription: 'Learn key lessons for building a successful startup. Practical advice from experienced entrepreneurs on avoiding common pitfalls.',
    keywords: ['startup advice', 'entrepreneurship', 'business building', 'startup lessons']
  }
];

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Seed users
const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log('🗑️  Cleared existing users');

    // Create users with hashed passwords
    const users = [];
    for (const userData of sampleUsers) {
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      const user = new User({
        ...userData,
        password: hashedPassword
      });
      
      // Generate blog domain
      user.generateBlogDomain();
      users.push(user);
    }

    const createdUsers = await User.insertMany(users);
    console.log(`✅ Created ${createdUsers.length} users`);
    return createdUsers;
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    throw error;
  }
};

// Seed posts
const seedPosts = async (users) => {
  try {
    // Clear existing posts
    await Post.deleteMany({});
    console.log('🗑️  Cleared existing posts');

    // Create posts and assign to users
    const posts = samplePosts.map((postData, index) => ({
      ...postData,
      author: users[index % users.length]._id,
      publishedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
      views: Math.floor(Math.random() * 1000) + 50,
      likes: Math.floor(Math.random() * 100) + 5,
      shares: Math.floor(Math.random() * 50) + 1
    }));

    const createdPosts = await Post.insertMany(posts);
    console.log(`✅ Created ${createdPosts.length} posts`);

    // Update user post counts
    for (const user of users) {
      const userPostCount = createdPosts.filter(post => 
        post.author.toString() === user._id.toString()
      ).length;
      
      await User.findByIdAndUpdate(user._id, {
        totalPosts: userPostCount
      });
    }

    console.log('✅ Updated user post counts');
    return createdPosts;
  } catch (error) {
    console.error('❌ Error seeding posts:', error);
    throw error;
  }
};

// Main seeding function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    await connectDB();
    
    const users = await seedUsers();
    const posts = await seedPosts(users);
    
    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Users: ${users.length}`);
    console.log(`   - Posts: ${posts.length}`);
    console.log('');
    console.log('🔑 Sample login credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
