# Rivsy Production Optimization Guide

This guide covers advanced optimization techniques for running Rivsy in production environments.

## 🚀 Performance Optimizations

### 1. Caching Strategy

#### Redis Configuration
```bash
# Install Redis
sudo apt-get install redis-server

# Configure Redis for production
sudo nano /etc/redis/redis.conf
```

```conf
# /etc/redis/redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

#### Environment Variables
```env
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
ENABLE_CACHE=true
```

### 2. Database Optimization

#### MongoDB Indexing
```javascript
// Run in MongoDB shell
use rivsy-blog

// Essential indexes for performance
db.posts.createIndex({ "status": 1, "publishedAt": -1 })
db.posts.createIndex({ "slug": 1 }, { unique: true })
db.posts.createIndex({ "author": 1, "status": 1 })
db.posts.createIndex({ "categories": 1, "status": 1 })
db.posts.createIndex({ "tags": 1, "status": 1 })
db.posts.createIndex({ "views": -1 })
db.posts.createIndex({ "publishedAt": -1 })

// Text search index
db.posts.createIndex({ 
  "title": "text", 
  "content": "text", 
  "excerpt": "text" 
})

// User indexes
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "blogDomain": 1 }, { unique: true })
```

#### MongoDB Configuration
```yaml
# /etc/mongod.conf
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp
```

### 3. Static Site Generation

#### Automated Generation
```bash
# Add to crontab for regular static site generation
0 */6 * * * cd /path/to/rivsy && npm run generate-static

# Or use GitHub Actions
```

```yaml
# .github/workflows/static-generation.yml
name: Generate Static Site
on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours
  workflow_dispatch:

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: npm run generate-static
      - uses: actions/upload-artifact@v3
        with:
          name: static-site
          path: static-site/
```

### 4. CDN Integration

#### Cloudflare Setup
```javascript
// server/middleware/cdn.js
const cdnMiddleware = (req, res, next) => {
  // Set cache headers for static assets
  if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$/)) {
    res.set('Cache-Control', 'public, max-age=31536000, immutable');
  }
  
  // Set cache headers for API responses
  if (req.url.startsWith('/api/posts') && req.method === 'GET') {
    res.set('Cache-Control', 'public, max-age=300'); // 5 minutes
  }
  
  next();
};
```

#### AWS CloudFront
```json
{
  "Origins": [
    {
      "DomainName": "your-domain.com",
      "Id": "rivsy-origin",
      "CustomOriginConfig": {
        "HTTPPort": 80,
        "HTTPSPort": 443,
        "OriginProtocolPolicy": "https-only"
      }
    }
  ],
  "DefaultCacheBehavior": {
    "TargetOriginId": "rivsy-origin",
    "ViewerProtocolPolicy": "redirect-to-https",
    "CachePolicyId": "managed-caching-optimized",
    "Compress": true
  }
}
```

## 🔒 Security Hardening

### 1. Environment Security
```env
# Production environment variables
NODE_ENV=production
JWT_SECRET=super-secure-random-string-64-chars-minimum
REFRESH_TOKEN_SECRET=another-super-secure-random-string-64-chars
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. Nginx Security Configuration
```nginx
# /etc/nginx/sites-available/rivsy
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://localhost:5000;
    }
}
```

### 3. Firewall Configuration
```bash
# UFW (Ubuntu Firewall)
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Fail2ban for additional protection
sudo apt-get install fail2ban
```

## 📊 Monitoring & Logging

### 1. Application Monitoring
```javascript
// server/middleware/monitoring.js
const monitoring = require('./services/monitoringService');

app.use(monitoring.requestLogger());
app.use(monitoring.errorTracker());
app.use(monitoring.performanceMonitor());
```

### 2. Log Management
```javascript
// server/config/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

### 3. Health Checks
```javascript
// server/routes/health.js
router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: await checkDatabaseHealth(),
    cache: await checkCacheHealth(),
    services: await checkExternalServices()
  };
  
  res.json(health);
});
```

## 🔄 Backup & Recovery

### 1. Database Backup
```bash
#!/bin/bash
# backup-mongodb.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mongodb"
DB_NAME="rivsy-blog"

mkdir -p $BACKUP_DIR

mongodump --db $DB_NAME --out $BACKUP_DIR/$DATE

# Compress backup
tar -czf $BACKUP_DIR/$DATE.tar.gz -C $BACKUP_DIR $DATE
rm -rf $BACKUP_DIR/$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/$DATE.tar.gz s3://your-backup-bucket/mongodb/
```

### 2. File Backup
```bash
#!/bin/bash
# backup-files.sh
rsync -av --delete /path/to/rivsy/uploads/ /backups/uploads/
rsync -av --delete /path/to/rivsy/logs/ /backups/logs/
```

### 3. Automated Backup Schedule
```bash
# Add to crontab
0 2 * * * /path/to/backup-mongodb.sh
0 3 * * * /path/to/backup-files.sh
```

## 🚀 Scaling Strategies

### 1. Horizontal Scaling
```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  rivsy:
    deploy:
      replicas: 3
    environment:
      - NODE_ENV=production
  
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
      - "443:443"
```

### 2. Load Balancer Configuration
```nginx
# nginx-lb.conf
upstream rivsy_backend {
    least_conn;
    server rivsy_1:5000;
    server rivsy_2:5000;
    server rivsy_3:5000;
}

server {
    listen 80;
    location / {
        proxy_pass http://rivsy_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. Database Scaling
```javascript
// MongoDB Replica Set
rs.initiate({
  _id: "rivsy-rs",
  members: [
    { _id: 0, host: "mongo1:27017" },
    { _id: 1, host: "mongo2:27017" },
    { _id: 2, host: "mongo3:27017" }
  ]
});
```

## 📈 Performance Monitoring

### 1. Key Metrics to Track
- Response time (avg, p95, p99)
- Error rate
- Throughput (requests/second)
- Memory usage
- CPU usage
- Database query performance
- Cache hit rate

### 2. Alerting Rules
```yaml
# alerts.yml
groups:
  - name: rivsy-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
```

## 🔧 Maintenance Tasks

### 1. Regular Maintenance Script
```bash
#!/bin/bash
# maintenance.sh

echo "Starting maintenance tasks..."

# Update dependencies
npm audit fix

# Clean up old logs
find logs/ -name "*.log" -mtime +30 -delete

# Optimize database
mongo rivsy-blog --eval "db.runCommand({compact: 'posts'})"

# Clear old cache entries
redis-cli FLUSHDB

# Restart services
pm2 restart all

echo "Maintenance completed!"
```

### 2. Performance Optimization Checklist
- [ ] Enable gzip compression
- [ ] Optimize images (WebP format)
- [ ] Minify CSS and JavaScript
- [ ] Enable browser caching
- [ ] Use CDN for static assets
- [ ] Implement lazy loading
- [ ] Optimize database queries
- [ ] Enable Redis caching
- [ ] Monitor and fix memory leaks
- [ ] Regular security updates

---

This optimization guide ensures Rivsy runs efficiently and securely in production environments while maintaining high performance and reliability.
