# Rivsy - Modern Blogging Platform

A complete MERN stack blogging platform that competes with Superblog.ai, featuring AI-powered content assistance, lightning-fast performance, and automatic SEO optimization.

## 🚀 Features

### Core Features
- **Lightning Fast**: Static site generation with CDN delivery
- **AI-Powered**: Content suggestions and SEO optimization using LiteLLM
- **SEO Optimized**: Automatic meta tags, sitemaps, and schema markup
- **Custom Domains**: Connect your own domain or use subdomain hosting
- **Analytics**: Privacy-friendly analytics dashboard
- **GDPR Compliant**: Built with privacy in mind

### Technical Features
- **MERN Stack**: MongoDB, Express.js, React.js, Node.js
- **Modern Frontend**: React with Vite, Tailwind CSS, Framer Motion
- **Responsive Design**: Mobile-first approach with dark mode support
- **Authentication**: JWT-based secure authentication
- **File Upload**: Image optimization and CDN integration
- **Real-time**: Live preview and auto-save functionality

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (v18.0.0 or higher)
- **npm** (v8.0.0 or higher)
- **MongoDB** (v5.0 or higher)
- **Git**

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/rivsy.git
cd rivsy
```

### 2. Install Dependencies
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 3. Environment Configuration
```bash
# Copy the environment template
cp .env.template .env

# Edit the .env file with your configuration
# At minimum, configure:
# - MONGODB_URI
# - JWT_SECRET
# - REFRESH_TOKEN_SECRET
```

### 4. Database Setup
Make sure MongoDB is running on your system:
```bash
# Start MongoDB (varies by OS)
# macOS with Homebrew:
brew services start mongodb-community

# Ubuntu/Debian:
sudo systemctl start mongod

# Windows: Start MongoDB service from Services panel
```

### 5. Start Development Servers
```bash
# From the root directory, start both client and server
npm run dev

# Or start them separately:
# Terminal 1 - Server
npm run server

# Terminal 2 - Client
npm run client
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

## 🔧 Configuration

### Environment Variables

Copy `.env.template` to `.env` and configure the following:

#### Required Variables
```env
MONGODB_URI=mongodb://localhost:27017/rivsy-blog
JWT_SECRET=your-super-secret-jwt-key
REFRESH_TOKEN_SECRET=your-refresh-token-secret
```

#### AI Integration (Optional)
Configure one of the following for AI features:

**Azure OpenAI:**
```env
AZURE_API_KEY=your-azure-openai-api-key
AZURE_API_BASE=https://your-resource.openai.azure.com/
AZURE_API_VERSION=2023-12-01-preview
AZURE_DEPLOYMENT_NAME=gpt-4
```

**OpenAI:**
```env
OPENAI_API_KEY=your-openai-api-key
```

**AWS Bedrock:**
```env
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
```

## 📁 Project Structure

```
rivsy/
├── client/                 # React frontend
│   ├── public/            # Static assets
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── contexts/      # React contexts
│   │   ├── hooks/         # Custom hooks
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── utils/         # Utility functions
│   ├── package.json
│   └── vite.config.js
├── server/                # Express backend
│   ├── middleware/        # Express middleware
│   ├── models/           # Mongoose models
│   ├── routes/           # API routes
│   ├── services/         # Business logic
│   ├── utils/            # Utility functions
│   ├── package.json
│   └── index.js
├── .env.template         # Environment variables template
├── .env                  # Your environment variables (create this)
├── package.json          # Root package.json
└── README.md
```

## 🚀 Deployment

### Production Build
```bash
# Build the client
cd client
npm run build

# The built files will be in client/dist/
```

### Environment Setup
1. Set `NODE_ENV=production` in your environment
2. Configure production database URI
3. Set secure JWT secrets
4. Configure CDN and file storage
5. Set up SSL certificates

### Deployment Options
- **Vercel/Netlify**: For frontend static deployment
- **Heroku/Railway**: For full-stack deployment
- **AWS/DigitalOcean**: For custom server deployment
- **Docker**: Container deployment (Dockerfile included)

## 🧪 Testing

```bash
# Run server tests
cd server
npm test

# Run client tests
cd client
npm test

# Run tests in watch mode
npm run test:watch
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/password` - Change password

### Blog Post Endpoints
- `GET /api/posts` - Get published posts
- `GET /api/posts/:slug` - Get single post
- `POST /api/posts` - Create new post (auth required)
- `PUT /api/posts/:id` - Update post (auth required)
- `DELETE /api/posts/:id` - Delete post (auth required)

### AI Endpoints
- `POST /api/ai/suggestions` - Get AI content suggestions (auth required)
- `POST /api/ai/optimize` - Optimize content for SEO (auth required)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/your-username/rivsy/issues) page
2. Create a new issue with detailed information
3. Join our [Discord community](https://discord.gg/rivsy)
4. Email <NAME_EMAIL>

## 🙏 Acknowledgments

- [Superblog.ai](https://superblog.ai/) for inspiration
- [LiteLLM](https://docs.litellm.ai/) for AI integration
- The amazing open-source community

---

Made with ❤️ by the Rivsy Team
