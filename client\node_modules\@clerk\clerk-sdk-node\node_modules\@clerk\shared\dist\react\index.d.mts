import React, { PropsWithChildren } from 'react';
import { ClerkPaginatedResponse, OrganizationDomainResource, OrganizationMembershipRequestResource, OrganizationMembershipResource, OrganizationInvitationResource, OrganizationResource, GetDomainsParams, GetMembershipRequestParams, GetMembersParams, GetInvitationsParams, UserOrganizationInvitationResource, OrganizationSuggestionResource, CreateOrganizationParams, SetActive, GetUserOrganizationMembershipParams, GetUserOrganizationInvitationsParams, GetUserOrganizationSuggestionsParams, UseSessionReturn, UseSessionListReturn, UseUserReturn, LoadedClerk, UserResource, ClientResource, ActiveSessionResource, ClerkOptions } from '@clerk/types';
import { ClerkAPIResponseError } from '../error.mjs';
import { dequal } from 'dequal';

declare function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal;
type Options = {
    assertCtxFn?: (v: unknown, msg: string) => void;
};
type ContextOf<T> = React.Context<{
    value: T;
} | undefined>;
type UseCtxFn<T> = () => T;
/**
 * Creates and returns a Context and two hooks that return the context value.
 * The Context type is derived from the type passed in by the user.
 * The first hook returned guarantees that the context exists so the returned value is always CtxValue
 * The second hook makes no guarantees, so the returned value can be CtxValue | undefined
 */
declare const createContextAndHook: <CtxVal>(displayName: string, options?: Options) => [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>];

type ValueOrSetter<T = unknown> = (size: T | ((_size: T) => T)) => void;
type CacheSetter<CData = any> = (data?: CData | ((currentData?: CData) => Promise<undefined | CData> | undefined | CData)) => Promise<CData | undefined>;
/**
 * @interface
 */
type PaginatedResources<T = unknown, Infinite = false> = {
    /**
     * An array that contains the fetched data.
     */
    data: T[];
    /**
     * The total count of data that exist remotely.
     */
    count: number;
    /**
     * Clerk's API response error object.
     */
    error: ClerkAPIResponseError | null;
    /**
     * A boolean that is `true` if there is an ongoing request and there is no fetched data.
     */
    isLoading: boolean;
    /**
     * A boolean that is `true` if there is an ongoing request or a revalidation.
     */
    isFetching: boolean;
    /**
     * A boolean that indicates the request failed.
     */
    isError: boolean;
    /**
     * A number that indicates the current page.
     */
    page: number;
    /**
     * A number that indicates the total amount of pages. It is calculated based on `count`, `initialPage`, and `pageSize`.
     */
    pageCount: number;
    /**
     * A function that triggers a specific page to be loaded.
     */
    fetchPage: ValueOrSetter<number>;
    /**
     *
     * A helper function that triggers the previous page to be loaded. This is the same as `fetchPage(page => Math.max(0, page - 1))`.
     */
    fetchPrevious: () => void;
    /**
     * A helper function that triggers the next page to be loaded. This is the same as `fetchPage(page => Math.min(pageCount, page + 1))`.
     */
    fetchNext: () => void;
    /**
     * A boolean that indicates if there are available pages to be fetched.
     */
    hasNextPage: boolean;
    /**
     * A boolean that indicates if there are available pages to be fetched.
     */
    hasPreviousPage: boolean;
    /**
     * A function that triggers a revalidation of the current page.
     */
    revalidate: () => Promise<void>;
    /**
     * A function that allows you to set the data manually.
     */
    setData: Infinite extends true ? CacheSetter<(ClerkPaginatedResponse<T> | undefined)[]> : CacheSetter<ClerkPaginatedResponse<T> | undefined>;
};
type PaginatedResourcesWithDefault<T> = {
    [K in keyof PaginatedResources<T>]: PaginatedResources<T>[K] extends boolean ? false : undefined;
};
type PaginatedHookConfig<T> = T & {
    /**
     * If `true`, newly fetched data will be appended to the existing list rather than replacing it. Useful for implementing infinite scroll functionality. Defaults to `false`.
     */
    infinite?: boolean;
    /**
     * If `true`, the previous data will be kept in the cache until new data is fetched. Defaults to `false`.
     */
    keepPreviousData?: boolean;
};

type UseOrganizationParams = {
    /**
     * If set to `true`, all default properties will be used. Otherwise, accepts an object with an optional `enrollmentMode` property of type [`OrganizationEnrollmentMode`](https://clerk.com/docs/references/react/use-organization#organization-enrollment-mode) and any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization#shared-properties).
     */
    domains?: true | PaginatedHookConfig<GetDomainsParams>;
    /**
     * If set to `true`, all default properties will be used. Otherwise, accepts an object with an optional `status` property of type [`OrganizationInvitationStatus`](https://clerk.com/docs/references/react/use-organization#organization-invitation-status) and any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization#shared-properties).
     */
    membershipRequests?: true | PaginatedHookConfig<GetMembershipRequestParams>;
    /**
     * If set to `true`, all default properties will be used. Otherwise, accepts an object with an optional `role` property of type [`OrganizationCustomRoleKey[]`](https://clerk.com/docs/references/react/use-organization#organization-custome-role-key) and any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization#shared-properties).
     */
    memberships?: true | PaginatedHookConfig<GetMembersParams>;
    /**
     * If set to `true`, all default properties will be used. Otherwise, accepts an object with an optional `status` property of type [`OrganizationInvitationStatus`](https://clerk.com/docs/references/react/use-organization#organization-invitation-status) and any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization#shared-properties).
     */
    invitations?: true | PaginatedHookConfig<GetInvitationsParams>;
};
type UseOrganization = <T extends UseOrganizationParams>(params?: T) => {
    /**
     * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.
     */
    isLoaded: false;
    /**
     * The currently active organization.
     */
    organization: undefined;
    /**
     * The current organization membership.
     */
    membership: undefined;
    /**
     * Includes a paginated list of the organization's domains.
     */
    domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;
    /**
     * Includes a paginated list of the organization's membership requests.
     */
    membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;
    /**
     * Includes a paginated list of the organization's memberships.
     */
    memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;
    /**
     * Includes a paginated list of the organization's invitations.
     */
    invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;
} | {
    /**
     * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.
     */
    isLoaded: true;
    /**
     * The currently active organization.
     */
    organization: OrganizationResource;
    /**
     * The current organization membership.
     */
    membership: undefined;
    /**
     * Includes a paginated list of the organization's domains.
     */
    domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;
    /**
     * Includes a paginated list of the organization's membership requests.
     */
    membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;
    /**
     * Includes a paginated list of the organization's memberships.
     */
    memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;
    /**
     * Includes a paginated list of the organization's invitations.
     */
    invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;
} | {
    /**
     * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.
     */
    isLoaded: boolean;
    /**
     * The currently active organization.
     */
    organization: OrganizationResource | null;
    /**
     * The current organization membership.
     */
    membership: OrganizationMembershipResource | null | undefined;
    /**
     * Includes a paginated list of the organization's domains.
     */
    domains: PaginatedResources<OrganizationDomainResource, T['membershipRequests'] extends {
        infinite: true;
    } ? true : false> | null;
    /**
     * Includes a paginated list of the organization's membership requests.
     */
    membershipRequests: PaginatedResources<OrganizationMembershipRequestResource, T['membershipRequests'] extends {
        infinite: true;
    } ? true : false> | null;
    /**
     * Includes a paginated list of the organization's memberships.
     */
    memberships: PaginatedResources<OrganizationMembershipResource, T['memberships'] extends {
        infinite: true;
    } ? true : false> | null;
    /**
     * Includes a paginated list of the organization's invitations.
     */
    invitations: PaginatedResources<OrganizationInvitationResource, T['invitations'] extends {
        infinite: true;
    } ? true : false> | null;
};
/**
 * The `useOrganization()` hook retrieves attributes of the currently active organization.
 */
declare const useOrganization: UseOrganization;

type UseOrganizationListParams = {
    /**
     * `true` or an object with any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization-list#shared-properties). If set to `true`, all default properties will be used.
     */
    userMemberships?: true | PaginatedHookConfig<GetUserOrganizationMembershipParams>;
    /**
     * `true` or an object with [`status: OrganizationInvitationStatus`](https://clerk.com/docs/references/react/use-organization-list#organization-invitation-status) or any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization-list#shared-properties). If set to `true`, all default properties will be used.
     */
    userInvitations?: true | PaginatedHookConfig<GetUserOrganizationInvitationsParams>;
    /**
     * `true` or an object with [`status: OrganizationSuggestionsStatus | OrganizationSuggestionStatus[]`](https://clerk.com/docs/references/react/use-organization-list#organization-suggestion-status) or any of the properties described in [Shared properties](https://clerk.com/docs/references/react/use-organization-list#shared-properties). If set to `true`, all default properties will be used.
     */
    userSuggestions?: true | PaginatedHookConfig<GetUserOrganizationSuggestionsParams>;
};
type UseOrganizationList = <T extends UseOrganizationListParams>(params?: T) => {
    /**
     * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.
     */
    isLoaded: false;
    /**
     * A function that returns a `Promise` which resolves to the newly created `Organization`.
     */
    createOrganization: undefined;
    /**
     * A function that sets the active session and/or organization.
     */
    setActive: undefined;
    /**
     * Returns `PaginatedResources` which includes a list of the user's organization memberships.
     */
    userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;
    /**
     * Returns `PaginatedResources` which includes a list of the user's organization invitations.
     */
    userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;
    /**
     * Returns `PaginatedResources` which includes a list of suggestions for organizations that the user can join.
     */
    userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;
} | {
    /**
     * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.
     */
    isLoaded: boolean;
    /**
     * A function that returns a `Promise` which resolves to the newly created `Organization`.
     */
    createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;
    /**
     * A function that sets the active session and/or organization.
     */
    setActive: SetActive;
    /**
     * Returns `PaginatedResources` which includes a list of the user's organization memberships.
     */
    userMemberships: PaginatedResources<OrganizationMembershipResource, T['userMemberships'] extends {
        infinite: true;
    } ? true : false>;
    /**
     * Returns `PaginatedResources` which includes a list of the user's organization invitations.
     */
    userInvitations: PaginatedResources<UserOrganizationInvitationResource, T['userInvitations'] extends {
        infinite: true;
    } ? true : false>;
    /**
     * Returns `PaginatedResources` which includes a list of suggestions for organizations that the user can join.
     */
    userSuggestions: PaginatedResources<OrganizationSuggestionResource, T['userSuggestions'] extends {
        infinite: true;
    } ? true : false>;
};
/**
 * The `useOrganizationList()` hook provides access to the current user's organization memberships, invitations, and suggestions. It also includes methods for creating new organizations and managing the active organization.
 */
declare const useOrganizationList: UseOrganizationList;

declare const useSafeLayoutEffect: typeof React.useLayoutEffect;

type UseSession = () => UseSessionReturn;
/**
 * The `useSession()` hook provides access to the current user's [`Session`](https://clerk.com/docs/references/javascript/session) object, as well as helpers for setting the active session.
 *
 * @example
 * ### Access the `Session` object
 *
 * The following example uses the `useSession()` hook to access the `Session` object, which has the `lastActiveAt` property. The `lastActiveAt` property is a `Date` object used to show the time the session was last active.
 *
 * ```tsx {{ filename: 'src/Home.tsx' }}
 * import { useSession } from '@clerk/clerk-react'
 *
 * export default function Home() {
 *   const { isLoaded, session, isSignedIn } = useSession()
 *
 *   if (!isLoaded) {
 *     // Handle loading state
 *     return null
 *   }
 *   if (!isSignedIn) {
 *     // Handle signed out state
 *     return null
 *   }
 *
 *   return (
 *     <div>
 *       <p>This session has been active since {session.lastActiveAt.toLocaleString()}</p>
 *     </div>
 *   )
 * }
 * ```
 */
declare const useSession: UseSession;

/**
 * The `useSessionList()` hook returns an array of [`Session`](https://clerk.com/docs/references/javascript/session) objects that have been registered on the client device.
 *
 * @example
 * ### Get a list of sessions
 *
 * The following example uses `useSessionList()` to get a list of sessions that have been registered on the client device. The `sessions` property is used to show the number of times the user has visited the page.
 *
 * ```tsx {{ filename: 'src/Home.tsx' }}
 * import { useSessionList } from '@clerk/clerk-react'
 *
 * export default function Home() {
 *   const { isLoaded, sessions } = useSessionList()
 *
 *   if (!isLoaded) {
 *     // Handle loading state
 *     return null
 *   }
 *
 *   return (
 *     <div>
 *       <p>Welcome back. You've been here {sessions.length} times before.</p>
 *     </div>
 *   )
 * }
 * ```
 */
declare const useSessionList: () => UseSessionListReturn;

/**
 * The `useUser()` hook provides access to the current user's [`User`](https://clerk.com/docs/references/javascript/user/user) object, which contains all the data for a single user in your application and provides methods to manage their account. This hook also allows you to check if the user is signed in and if Clerk has loaded and initialized.
 *
 * @example
 * ### Get the current user
 *
 * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user/user) object, which contains the current user's data such as their full name. The `isLoaded` and `isSignedIn` properties are used to handle the loading state and to check if the user is signed in, respectively.
 *
 * ```tsx {{ filename: 'src/Example.tsx' }}
 * export default function Example() {
 *   const { isSignedIn, user, isLoaded } = useUser()
 *
 *   if (!isLoaded) {
 *     return <div>Loading...</div>
 *   }
 *
 *   if (!isSignedIn) {
 *     return <div>Sign in to view this page</div>
 *   }
 *
 *   return <div>Hello {user.firstName}!</div>
 * }
 * ```
 *
 * @example
 * ### Update user data
 *
 * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user/user) object, which calls the [`update()`](https://clerk.com/docs/references/javascript/user/user#update) method to update the current user's information.
 *
 * ```tsx {{ filename: 'src/Home.tsx' }}
 * import { useUser } from '@clerk/clerk-react'
 *
 * export default function Home() {
 *   const { isLoaded, user } = useUser()
 *
 *   if (!isLoaded) {
 *     // Handle loading state
 *     return null
 *   }
 *
 *   if (!user) return null
 *
 *   const updateUser = async () => {
 *     await user.update({
 *       firstName: 'John',
 *       lastName: 'Doe',
 *     })
 *   }
 *
 *   return (
 *     <>
 *       <button onClick={updateUser}>Update your name</button>
 *       <p>user.firstName: {user?.firstName}</p>
 *       <p>user.lastName: {user?.lastName}</p>
 *     </>
 *   )
 * }
 * ```
 *
 * @example
 * ### Reload user data
 *
 * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user/user) object, which calls the [`reload()`](https://clerk.com/docs/references/javascript/user/user#reload) method to get the latest user's information.
 *
 * ```tsx {{ filename: 'src/Home.tsx' }}
 * import { useUser } from '@clerk/clerk-react'
 *
 * export default function Home() {
 *   const { isLoaded, user } = useUser()
 *
 *   if (!isLoaded) {
 *     // Handle loading state
 *     return null
 *   }
 *
 *   if (!user) return null
 *
 *   const updateUser = async () => {
 *     // Update data via an API endpoint
 *     const updateMetadata = await fetch('/api/updateMetadata')
 *
 *     // Check if the update was successful
 *     if (updateMetadata.message !== 'success') {
 *       throw new Error('Error updating')
 *     }
 *
 *     // If the update was successful, reload the user data
 *     await user.reload()
 *   }
 *
 *   return (
 *     <>
 *       <button onClick={updateUser}>Update your metadata</button>
 *       <p>user role: {user?.publicMetadata.role}</p>
 *     </>
 *   )
 * }
 * ```
 */
declare function useUser(): UseUserReturn;

/**
 * The `useClerk()` hook provides access to the [`Clerk`](https://clerk.com/docs/references/javascript/clerk/clerk) object, allowing you to build alternatives to any Clerk Component.
 *
 * @warning
 * This composable should only be used for advanced use cases, such as building a completely custom OAuth flow or as an escape hatch to access to the `Clerk` object.
 *
 * @returns The `Clerk` object, which includes all the methods and properties listed in the [`Clerk` reference](https://clerk.com/docs/references/javascript/clerk/clerk).
 *
 * @example
 *
 * The following example uses the `useClerk()` hook to access the `clerk` object. The `clerk` object is used to call the [`openSignIn()`](https://clerk.com/docs/references/javascript/clerk/clerk#sign-in) method to open the sign-in modal.
 *
 * ```tsx {{ filename: 'src/Home.tsx' }}
 * import { useClerk } from '@clerk/clerk-react'
 *
 * export default function Home() {
 *   const clerk = useClerk()
 *
 *   return <button onClick={() => clerk.openSignIn({})}>Sign in</button>
 * }
 * ```
 */
declare const useClerk: () => LoadedClerk;

type UseMemoFactory<T> = () => T;
type UseMemoDependencyArray = Exclude<Parameters<typeof React.useMemo>[1], 'undefined'>;
type UseDeepEqualMemo = <T>(factory: UseMemoFactory<T>, dependencyArray: UseMemoDependencyArray) => T;
declare const useDeepEqualMemo: UseDeepEqualMemo;
declare const isDeeplyEqual: typeof dequal;

type ExcludeClerkError<T, P> = T extends {
    clerk_error: any;
} ? (P extends {
    throwOnCancel: true;
} ? never : null) : T;
/**
 * The optional options object.
 */
type UseReverificationOptions = {
    /**
     * A callback function that is invoked when the user cancels the reverification process.
     */
    onCancel?: () => void;
    /**
     * Determines if an error should throw when the user cancels the reverification process. Defaults to `false`.
     */
    throwOnCancel?: boolean;
};
type UseReverificationResult<Fetcher extends (...args: any[]) => Promise<any> | undefined, Options extends UseReverificationOptions> = readonly [(...args: Parameters<Fetcher>) => Promise<ExcludeClerkError<Awaited<ReturnType<Fetcher>>, Options>>];
/**
 * The `useReverification()` hook is used to handle a session's reverification flow. If a request requires reverification, a modal will display, prompting the user to verify their credentials. Upon successful verification, the original request will automatically retry.
 *
 * @warning
 *
 * This feature is currently in public beta. **It is not recommended for production use.**
 *
 * Depending on the SDK you're using, this feature requires `@clerk/nextjs@6.5.0` or later, `@clerk/clerk-react@5.17.0` or later, and `@clerk/clerk-js@5.35.0` or later.
 *
 * @example
 * ### Handle cancellation of the reverification process
 *
 * The following example demonstrates how to handle scenarios where a user cancels the reverification flow, such as closing the modal, which might result in `myData` being `null`.
 *
 * In the following example, `myFetcher` would be a function in your backend that fetches data from the route that requires reverification. See the [guide on how to require reverification](https://clerk.com/docs/guides/reverification) for more information.
 *
 * ```tsx {{ filename: 'src/components/MyButton.tsx' }}
 * import { useReverification } from '@clerk/react'
 *
 * export function MyButton() {
 *   const [enhancedFetcher] = useReverification(myFetcher)
 *
 *   const handleClick = async () => {
 *     const myData = await enhancedFetcher()
 *     // If `myData` is null, the user canceled the reverification process
 *     // You can choose how your app responds. This example returns null.
 *     if (!myData) return
 *   }
 *
 *   return <button onClick={handleClick}>Update User</button>
 * }
 * ```
 *
 * @example
 * ### Handle `throwOnCancel`
 *
 * When `throwOnCancel` is set to `true`, the fetcher will throw a `ClerkRuntimeError` with the code `"reverification_cancelled"` if the user cancels the reverification flow (for example, by closing the modal). This error can be caught and handled according to your app's needs. For example, by displaying a toast notification to the user or silently ignoring the cancellation.
 *
 * In this example, `myFetcher` would be a function in your backend that fetches data from the route that requires reverification. See the [guide on how to require reverification](https://clerk.com/docs/guides/reverification) for more information.
 *
 * ```tsx {{ filename: 'src/components/MyButton.tsx' }}
 * import { useReverification } from '@clerk/clerk-react'
 * import { isClerkRuntimeError } from '@clerk/clerk-react/errors'
 *
 * export function MyButton() {
 *   const [enhancedFetcher] = useReverification(myFetcher, { throwOnCancel: true })
 *
 *   const handleClick = async () => {
 *     try {
 *       const myData = await enhancedFetcher()
 *     } catch (e) {
 *       // Handle if user cancels the reverification process
 *       if (isClerkRuntimeError(e) && e.code === 'reverification_cancelled') {
 *         console.error('User cancelled reverification', e.code)
 *       }
 *     }
 *   }
 *
 *   return <button onClick={handleClick}>Update user</button>
 * }
 * ```
 */
declare function useReverification<Fetcher extends (...args: any[]) => Promise<any> | undefined, Options extends UseReverificationOptions>(fetcher: Fetcher, options?: Options): UseReverificationResult<Fetcher, Options>;

declare const ClerkInstanceContext: React.Context<{
    value: LoadedClerk;
} | undefined>;
declare const useClerkInstanceContext: () => LoadedClerk;
declare const UserContext: React.Context<{
    value: UserResource | null | undefined;
} | undefined>;
declare const useUserContext: () => UserResource | null | undefined;
declare const ClientContext: React.Context<{
    value: ClientResource | null | undefined;
} | undefined>;
declare const useClientContext: () => ClientResource | null | undefined;
declare const SessionContext: React.Context<{
    value: ActiveSessionResource | null | undefined;
} | undefined>;
declare const useSessionContext: () => ActiveSessionResource | null | undefined;
declare const OptionsContext: React.Context<ClerkOptions>;
declare function useOptionsContext(): ClerkOptions;
type OrganizationContextProps = {
    organization: OrganizationResource | null | undefined;
};
declare const useOrganizationContext: () => {
    organization: OrganizationResource | null | undefined;
};
declare const OrganizationProvider: ({ children, organization, swrConfig, }: PropsWithChildren<OrganizationContextProps & {
    swrConfig?: any;
}>) => React.JSX.Element;
declare function useAssertWrappedByClerkProvider(displayNameOrFn: string | (() => void)): void;

export { ClerkInstanceContext, ClientContext, OptionsContext, OrganizationProvider, SessionContext, UserContext, assertContextExists, createContextAndHook, isDeeplyEqual, useAssertWrappedByClerkProvider, useClerk, useClerkInstanceContext, useClientContext, useDeepEqualMemo, useOptionsContext, useOrganization, useOrganizationContext, useOrganizationList, useReverification, useSafeLayoutEffect, useSession, useSessionContext, useSessionList, useUser, useUserContext };
