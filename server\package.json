{"name": "rivsy-server", "version": "1.0.0", "description": "Backend API server for Rivsy blog platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedDatabase.js"}, "dependencies": {"@clerk/backend": "^2.3.1", "@clerk/clerk-js": "^5.69.3", "@clerk/clerk-react": "^5.32.3", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dompurify": "^3.0.7", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsdom": "^23.0.1", "jsonwebtoken": "^9.0.2", "marked": "^11.1.1", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "redis": "^5.5.6", "rss": "^1.2.2", "sharp": "^0.33.1", "sitemap": "^7.1.1", "slugify": "^1.6.6", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xss": "^1.0.14"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}