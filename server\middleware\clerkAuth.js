const { createClerkClient } = require('@clerk/backend');
const User = require('../models/User');
const logger = require('../config/logger');

// Initialize Clerk with secret key
const clerk = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
});

// Middleware to verify Clerk JWT token
const verifyClerkToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.',
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify the token with Clerk
    const payload = await clerk.verifyToken(token);
    
    if (!payload || !payload.sub) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.',
      });
    }

    // Get user from Clerk
    const clerkUser = await clerk.users.getUser(payload.sub);
    
    if (!clerkUser) {
      return res.status(401).json({
        success: false,
        message: 'User not found in Clerk.',
      });
    }

    // Find or create user in our database
    let user = await User.findOne({ clerkId: clerkUser.id });
    
    if (!user) {
      // Create new user if doesn't exist
      user = await User.create({
        clerkId: clerkUser.id,
        email: clerkUser.primaryEmailAddress?.emailAddress,
        name: clerkUser.fullName || `${clerkUser.firstName} ${clerkUser.lastName}`.trim(),
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        imageUrl: clerkUser.imageUrl,
        isVerified: clerkUser.primaryEmailAddress?.verification?.status === 'verified',
        role: 'user', // Default role
      });

      logger.info('New user created via Clerk', {
        userId: user._id,
        clerkId: clerkUser.id,
        email: user.email,
      });
    } else {
      // Update existing user with latest Clerk data
      const updateData = {
        email: clerkUser.primaryEmailAddress?.emailAddress,
        name: clerkUser.fullName || `${clerkUser.firstName} ${clerkUser.lastName}`.trim(),
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        imageUrl: clerkUser.imageUrl,
        isVerified: clerkUser.primaryEmailAddress?.verification?.status === 'verified',
        lastLoginAt: new Date(),
      };

      user = await User.findByIdAndUpdate(user._id, updateData, { new: true });
    }

    // Attach user and Clerk data to request
    req.user = user;
    req.clerkUser = clerkUser;
    req.clerkPayload = payload;

    next();
  } catch (error) {
    logger.logError(error, req, {
      middleware: 'verifyClerkToken',
      token: req.headers.authorization ? 'present' : 'missing',
    });

    if (error.message.includes('Token expired')) {
      return res.status(401).json({
        success: false,
        message: 'Token expired. Please sign in again.',
      });
    }

    return res.status(401).json({
      success: false,
      message: 'Invalid token.',
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
const optionalClerkAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    // Use the main verification middleware
    await verifyClerkToken(req, res, next);
  } catch (error) {
    // Log error but continue without authentication
    logger.warn('Optional auth failed', {
      error: error.message,
      url: req.originalUrl,
    });
    next();
  }
};

// Role-based authorization middleware
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Authentication required.',
      });
    }

    if (!roles.includes(req.user.role)) {
      logger.warn('Authorization failed', {
        userId: req.user._id,
        userRole: req.user.role,
        requiredRoles: roles,
        url: req.originalUrl,
      });

      return res.status(403).json({
        success: false,
        message: `Access denied. Required role(s): ${roles.join(', ')}`,
      });
    }

    next();
  };
};

// Admin only middleware
const adminOnly = authorize('admin');

// Author or admin middleware
const authorOrAdmin = authorize('author', 'admin');

// User, author, or admin middleware
const authenticatedUser = authorize('user', 'author', 'admin');

// Middleware to check if user owns the resource
const checkOwnership = (resourceField = 'author') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.',
      });
    }

    // Admin can access everything
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user owns the resource
    const resource = req.resource || req.body || req.params;
    const resourceOwnerId = resource[resourceField];

    if (!resourceOwnerId) {
      return res.status(400).json({
        success: false,
        message: 'Resource ownership cannot be determined.',
      });
    }

    if (resourceOwnerId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only access your own resources.',
      });
    }

    next();
  };
};

// Middleware to sync user data with Clerk
const syncUserWithClerk = async (req, res, next) => {
  try {
    if (!req.user || !req.user.clerkId) {
      return next();
    }

    // Get latest user data from Clerk
    const clerkUser = await clerk.users.getUser(req.user.clerkId);
    
    if (clerkUser) {
      // Update user data if needed
      const updateData = {
        email: clerkUser.primaryEmailAddress?.emailAddress,
        name: clerkUser.fullName || `${clerkUser.firstName} ${clerkUser.lastName}`.trim(),
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        imageUrl: clerkUser.imageUrl,
        isVerified: clerkUser.primaryEmailAddress?.verification?.status === 'verified',
      };

      // Only update if data has changed
      const hasChanges = Object.keys(updateData).some(
        key => req.user[key] !== updateData[key]
      );

      if (hasChanges) {
        req.user = await User.findByIdAndUpdate(
          req.user._id, 
          updateData, 
          { new: true }
        );
      }
    }

    next();
  } catch (error) {
    logger.warn('Failed to sync user with Clerk', {
      error: error.message,
      userId: req.user?._id,
      clerkId: req.user?.clerkId,
    });
    
    // Continue even if sync fails
    next();
  }
};

module.exports = {
  verifyClerkToken,
  optionalClerkAuth,
  authorize,
  adminOnly,
  authorOrAdmin,
  authenticatedUser,
  checkOwnership,
  syncUserWithClerk,
  clerk,
};
