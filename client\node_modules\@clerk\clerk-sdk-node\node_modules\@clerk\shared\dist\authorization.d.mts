import * as _clerk_types from '@clerk/types';
import { ReverificationConfig, SessionVerificationLevel, CheckAuthorizationWithCustomPermissions } from '@clerk/types';

type AuthorizationOptions = {
    userId: string | null | undefined;
    orgId: string | null | undefined;
    orgRole: string | null | undefined;
    orgPermissions: string[] | null | undefined;
    factorVerificationAge: [number, number] | null;
};
declare const validateReverificationConfig: (config: ReverificationConfig | undefined | null) => false | (() => {
    level: SessionVerificationLevel;
    afterMinutes: _clerk_types.SessionVerificationAfterMinutes;
});
/**
 * Creates a function for comprehensive user authorization checks.
 * Combines organization-level and step-up authentication checks.
 * The returned function authorizes if both checks pass, or if at least one passes
 * when the other is indeterminate. Fails if userId is missing.
 */
declare const createCheckAuthorization: (options: AuthorizationOptions) => CheckAuthorizationWithCustomPermissions;

export { createCheckAuthorization, validateReverificationConfig };
