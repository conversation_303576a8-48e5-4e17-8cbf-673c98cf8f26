const os = require('os');
const process = require('process');

class PerformanceService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        responseTimes: []
      },
      system: {
        startTime: Date.now(),
        uptime: 0,
        memory: {},
        cpu: {}
      }
    };

    // Start collecting system metrics
    this.startSystemMonitoring();
  }

  // Middleware to track request performance
  middleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      // Track request start
      this.metrics.requests.total++;

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = (...args) => {
        const responseTime = Date.now() - startTime;
        
        // Track response time
        this.metrics.requests.responseTimes.push(responseTime);
        
        // Keep only last 1000 response times
        if (this.metrics.requests.responseTimes.length > 1000) {
          this.metrics.requests.responseTimes.shift();
        }

        // Track success/error
        if (res.statusCode >= 200 && res.statusCode < 400) {
          this.metrics.requests.success++;
        } else {
          this.metrics.requests.errors++;
        }

        // Add performance headers
        res.set('X-Response-Time', `${responseTime}ms`);
        res.set('X-Server-Timing', `total;dur=${responseTime}`);

        originalEnd.apply(res, args);
      };

      next();
    };
  }

  // Start system monitoring
  startSystemMonitoring() {
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000); // Update every 30 seconds

    // Initial update
    this.updateSystemMetrics();
  }

  // Update system metrics
  updateSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    this.metrics.system = {
      startTime: this.metrics.system.startTime,
      uptime: Date.now() - this.metrics.system.startTime,
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers,
        systemTotal: os.totalmem(),
        systemFree: os.freemem(),
        systemUsed: os.totalmem() - os.freemem()
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        loadAverage: os.loadavg(),
        cores: os.cpus().length
      },
      platform: {
        arch: os.arch(),
        platform: os.platform(),
        version: os.version(),
        hostname: os.hostname()
      }
    };
  }

  // Get performance metrics
  getMetrics() {
    const responseTimes = this.metrics.requests.responseTimes;
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const p95ResponseTime = responseTimes.length > 0
      ? this.calculatePercentile(responseTimes, 95)
      : 0;

    const p99ResponseTime = responseTimes.length > 0
      ? this.calculatePercentile(responseTimes, 99)
      : 0;

    const errorRate = this.metrics.requests.total > 0
      ? (this.metrics.requests.errors / this.metrics.requests.total) * 100
      : 0;

    return {
      requests: {
        total: this.metrics.requests.total,
        success: this.metrics.requests.success,
        errors: this.metrics.requests.errors,
        errorRate: parseFloat(errorRate.toFixed(2)),
        avgResponseTime: parseFloat(avgResponseTime.toFixed(2)),
        p95ResponseTime: parseFloat(p95ResponseTime.toFixed(2)),
        p99ResponseTime: parseFloat(p99ResponseTime.toFixed(2))
      },
      system: {
        uptime: this.metrics.system.uptime,
        uptimeFormatted: this.formatUptime(this.metrics.system.uptime),
        memory: {
          heapUsed: this.formatBytes(this.metrics.system.memory.heapUsed),
          heapTotal: this.formatBytes(this.metrics.system.memory.heapTotal),
          rss: this.formatBytes(this.metrics.system.memory.rss),
          systemUsed: this.formatBytes(this.metrics.system.memory.systemUsed),
          systemTotal: this.formatBytes(this.metrics.system.memory.systemTotal),
          systemFree: this.formatBytes(this.metrics.system.memory.systemFree),
          heapUsagePercent: parseFloat(((this.metrics.system.memory.heapUsed / this.metrics.system.memory.heapTotal) * 100).toFixed(2)),
          systemUsagePercent: parseFloat(((this.metrics.system.memory.systemUsed / this.metrics.system.memory.systemTotal) * 100).toFixed(2))
        },
        cpu: {
          loadAverage: this.metrics.system.cpu.loadAverage.map(load => parseFloat(load.toFixed(2))),
          cores: this.metrics.system.cpu.cores,
          loadPerCore: this.metrics.system.cpu.loadAverage.map(load => 
            parseFloat((load / this.metrics.system.cpu.cores * 100).toFixed(2))
          )
        },
        platform: this.metrics.system.platform
      },
      timestamp: new Date().toISOString()
    };
  }

  // Calculate percentile
  calculatePercentile(arr, percentile) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  // Format bytes to human readable
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Format uptime to human readable
  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // Health check
  getHealthStatus() {
    const metrics = this.getMetrics();
    const issues = [];

    // Check memory usage
    if (metrics.system.memory.heapUsagePercent > 90) {
      issues.push('High heap memory usage');
    }

    if (metrics.system.memory.systemUsagePercent > 90) {
      issues.push('High system memory usage');
    }

    // Check CPU load
    const avgLoad = metrics.system.cpu.loadPerCore[0]; // 1-minute average
    if (avgLoad > 80) {
      issues.push('High CPU load');
    }

    // Check error rate
    if (metrics.requests.errorRate > 5) {
      issues.push('High error rate');
    }

    // Check response time
    if (metrics.requests.p95ResponseTime > 2000) {
      issues.push('Slow response times');
    }

    const status = issues.length === 0 ? 'healthy' : 'warning';

    return {
      status,
      issues,
      metrics,
      timestamp: new Date().toISOString()
    };
  }

  // Reset metrics
  reset() {
    this.metrics.requests = {
      total: 0,
      success: 0,
      errors: 0,
      responseTimes: []
    };
  }

  // Get database performance metrics
  async getDatabaseMetrics() {
    try {
      const mongoose = require('mongoose');
      const db = mongoose.connection.db;
      
      if (!db) {
        return { error: 'Database not connected' };
      }

      const stats = await db.stats();
      const serverStatus = await db.admin().serverStatus();

      return {
        database: {
          collections: stats.collections,
          objects: stats.objects,
          dataSize: this.formatBytes(stats.dataSize),
          storageSize: this.formatBytes(stats.storageSize),
          indexSize: this.formatBytes(stats.indexSize),
          avgObjSize: this.formatBytes(stats.avgObjSize)
        },
        connections: {
          current: serverStatus.connections.current,
          available: serverStatus.connections.available,
          totalCreated: serverStatus.connections.totalCreated
        },
        operations: {
          insert: serverStatus.opcounters.insert,
          query: serverStatus.opcounters.query,
          update: serverStatus.opcounters.update,
          delete: serverStatus.opcounters.delete,
          getmore: serverStatus.opcounters.getmore,
          command: serverStatus.opcounters.command
        },
        memory: {
          resident: this.formatBytes(serverStatus.mem.resident * 1024 * 1024),
          virtual: this.formatBytes(serverStatus.mem.virtual * 1024 * 1024),
          mapped: this.formatBytes((serverStatus.mem.mapped || 0) * 1024 * 1024)
        }
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  // Performance recommendations
  getRecommendations() {
    const metrics = this.getMetrics();
    const recommendations = [];

    // Memory recommendations
    if (metrics.system.memory.heapUsagePercent > 80) {
      recommendations.push({
        type: 'memory',
        priority: 'high',
        message: 'Consider increasing heap size or optimizing memory usage',
        action: 'Review memory leaks and optimize data structures'
      });
    }

    // Response time recommendations
    if (metrics.requests.avgResponseTime > 1000) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: 'Average response time is high',
        action: 'Enable caching, optimize database queries, or add CDN'
      });
    }

    // Error rate recommendations
    if (metrics.requests.errorRate > 2) {
      recommendations.push({
        type: 'reliability',
        priority: 'high',
        message: 'Error rate is above acceptable threshold',
        action: 'Review error logs and fix underlying issues'
      });
    }

    // CPU recommendations
    const avgLoad = metrics.system.cpu.loadPerCore[0];
    if (avgLoad > 70) {
      recommendations.push({
        type: 'cpu',
        priority: 'medium',
        message: 'CPU load is high',
        action: 'Consider scaling horizontally or optimizing CPU-intensive operations'
      });
    }

    return recommendations;
  }
}

module.exports = new PerformanceService();
