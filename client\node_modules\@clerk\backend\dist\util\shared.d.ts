export { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from '@clerk/shared/url';
export { retry } from '@clerk/shared/retry';
export { isDevelopmentFromSecretKey, isProductionFromSecretKey, parsePublishable<PERSON><PERSON>, getCookieSuffix, getSuffixedCookieName, } from '@clerk/shared/keys';
export { deprecated, deprecatedProperty } from '@clerk/shared/deprecated';
export declare const errorThrower: import("@clerk/shared/error").ErrorThrower;
export declare const isDevOrStagingUrl: (url: string | URL) => boolean;
//# sourceMappingURL=shared.d.ts.map