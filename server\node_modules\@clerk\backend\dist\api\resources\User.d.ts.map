{"version": 3, "file": "User.d.ts", "sourceRoot": "", "sources": ["../../../src/api/resources/User.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,KAAK,EAAwC,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAC7E,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;GAEG;AACH,qBAAa,IAAI;IAQb;;OAEG;IACH,QAAQ,CAAC,EAAE,EAAE,MAAM;IACnB;;OAEG;IACH,QAAQ,CAAC,eAAe,EAAE,OAAO;IACjC;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,OAAO;IAC7B;;OAEG;IACH,QAAQ,CAAC,iBAAiB,EAAE,OAAO;IACnC;;OAEG;IACH,QAAQ,CAAC,gBAAgB,EAAE,OAAO;IAClC;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,OAAO;IACxB;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,OAAO;IACxB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM;IAC1B;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM;IAC1B;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM;IACzB;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,OAAO;IAC1B;;OAEG;IACH,QAAQ,CAAC,qBAAqB,EAAE,MAAM,GAAG,IAAI;IAC7C;;OAEG;IACH,QAAQ,CAAC,oBAAoB,EAAE,MAAM,GAAG,IAAI;IAC5C;;OAEG;IACH,QAAQ,CAAC,mBAAmB,EAAE,MAAM,GAAG,IAAI;IAC3C;;OAEG;IACH,QAAQ,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;IACpC;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAClC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAChC;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IACjC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAChC;;OAEG;IACH,QAAQ,CAAC,cAAc,EAAE,kBAAkB;IAC3C;;OAEG;IACH,QAAQ,CAAC,eAAe,EAAE,mBAAmB;IAC7C;;OAEG;IACH,QAAQ,CAAC,cAAc,EAAE,kBAAkB;IAC3C;;OAEG;IACH,QAAQ,CAAC,cAAc,EAAE,YAAY,EAAE;IACvC;;OAEG;IACH,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE;IACpC;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE;IAClC;;OAEG;IACH,QAAQ,CAAC,gBAAgB,EAAE,eAAe,EAAE;IAC5C;;OAEG;IACH,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE;IACpC;;OAEG;IACH,QAAQ,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;IACpC;;OAEG;IACH,QAAQ,CAAC,yBAAyB,EAAE,OAAO;IAC3C;;OAEG;IACH,QAAQ,CAAC,wBAAwB,EAAE,MAAM,GAAG,IAAI;IAChD;;OAEG;IACH,QAAQ,CAAC,iBAAiB,EAAE,OAAO;IACnC;;OAEG;IACH,QAAQ,CAAC,eAAe,EAAE,MAAM,GAAG,IAAI;IAtIzC,OAAO,CAAC,IAAI,CAAyB;IAErC,IAAW,GAAG,IAAI,QAAQ,GAAG,IAAI,CAEhC;;IAGC;;OAEG;IACM,EAAE,EAAE,MAAM;IACnB;;OAEG;IACM,eAAe,EAAE,OAAO;IACjC;;OAEG;IACM,WAAW,EAAE,OAAO;IAC7B;;OAEG;IACM,iBAAiB,EAAE,OAAO;IACnC;;OAEG;IACM,gBAAgB,EAAE,OAAO;IAClC;;OAEG;IACM,MAAM,EAAE,OAAO;IACxB;;OAEG;IACM,MAAM,EAAE,OAAO;IACxB;;OAEG;IACM,SAAS,EAAE,MAAM;IAC1B;;OAEG;IACM,SAAS,EAAE,MAAM;IAC1B;;OAEG;IACM,QAAQ,EAAE,MAAM;IACzB;;OAEG;IACM,QAAQ,EAAE,OAAO;IAC1B;;OAEG;IACM,qBAAqB,EAAE,MAAM,GAAG,IAAI;IAC7C;;OAEG;IACM,oBAAoB,EAAE,MAAM,GAAG,IAAI;IAC5C;;OAEG;IACM,mBAAmB,EAAE,MAAM,GAAG,IAAI;IAC3C;;OAEG;IACM,YAAY,EAAE,MAAM,GAAG,IAAI;IACpC;;OAEG;IACM,UAAU,EAAE,MAAM,GAAG,IAAI;IAClC;;OAEG;IACM,QAAQ,EAAE,MAAM,GAAG,IAAI;IAChC;;OAEG;IACM,SAAS,EAAE,MAAM,GAAG,IAAI;IACjC;;OAEG;IACM,QAAQ,EAAE,MAAM,GAAG,IAAI;IAChC;;OAEG;IACM,cAAc,EAAE,kBAAkB,YAAK;IAChD;;OAEG;IACM,eAAe,EAAE,mBAAmB,YAAK;IAClD;;OAEG;IACM,cAAc,EAAE,kBAAkB,YAAK;IAChD;;OAEG;IACM,cAAc,EAAE,YAAY,EAAE,YAAK;IAC5C;;OAEG;IACM,YAAY,EAAE,WAAW,EAAE,YAAK;IACzC;;OAEG;IACM,WAAW,EAAE,UAAU,EAAE,YAAK;IACvC;;OAEG;IACM,gBAAgB,EAAE,eAAe,EAAE,YAAK;IACjD;;OAEG;IACM,YAAY,EAAE,WAAW,EAAE,YAAK;IACzC;;OAEG;IACM,YAAY,EAAE,MAAM,GAAG,IAAI;IACpC;;OAEG;IACM,yBAAyB,EAAE,OAAO;IAC3C;;OAEG;IACM,wBAAwB,EAAE,MAAM,GAAG,IAAI,YAAO;IACvD;;OAEG;IACM,iBAAiB,EAAE,OAAO;IACnC;;OAEG;IACM,eAAe,EAAE,MAAM,GAAG,IAAI;IAGzC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAuCrC;;OAEG;IACH,IAAI,mBAAmB,wBAEtB;IAED;;OAEG;IACH,IAAI,kBAAkB,uBAErB;IAED;;OAEG;IACH,IAAI,iBAAiB,sBAEpB;IAED;;OAEG;IACH,IAAI,QAAQ,kBAEX;CACF"}