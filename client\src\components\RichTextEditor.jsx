import { useState, useRef, useEffect } from 'react';
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Quote, 
  Code, 
  Link, 
  Image,
  Heading1,
  Heading2,
  Heading3,
  Eye,
  Edit3,
  Sparkles
} from 'lucide-react';

const RichTextEditor = ({ 
  value = '', 
  onChange, 
  placeholder = 'Start writing your amazing content...',
  showAIAssist = true,
  onAIAssist
}) => {
  const [content, setContent] = useState(value);
  const [isPreview, setIsPreview] = useState(false);
  const [selection, setSelection] = useState({ start: 0, end: 0 });
  const textareaRef = useRef(null);

  useEffect(() => {
    setContent(value);
  }, [value]);

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    setContent(newContent);
    onChange?.(newContent);
  };

  const insertText = (before, after = '', placeholder = '') => {
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newContent = 
      content.substring(0, start) + 
      before + textToInsert + after + 
      content.substring(end);
    
    setContent(newContent);
    onChange?.(newContent);
    
    // Set cursor position
    setTimeout(() => {
      const newCursorPos = start + before.length + textToInsert.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
      textarea.focus();
    }, 0);
  };

  const formatText = (type) => {
    switch (type) {
      case 'bold':
        insertText('**', '**', 'bold text');
        break;
      case 'italic':
        insertText('*', '*', 'italic text');
        break;
      case 'underline':
        insertText('<u>', '</u>', 'underlined text');
        break;
      case 'h1':
        insertText('# ', '', 'Heading 1');
        break;
      case 'h2':
        insertText('## ', '', 'Heading 2');
        break;
      case 'h3':
        insertText('### ', '', 'Heading 3');
        break;
      case 'ul':
        insertText('- ', '', 'List item');
        break;
      case 'ol':
        insertText('1. ', '', 'Numbered item');
        break;
      case 'quote':
        insertText('> ', '', 'Quote text');
        break;
      case 'code':
        insertText('`', '`', 'code');
        break;
      case 'codeblock':
        insertText('```\n', '\n```', 'code block');
        break;
      case 'link':
        insertText('[', '](url)', 'link text');
        break;
      case 'image':
        insertText('![', '](image-url)', 'alt text');
        break;
      default:
        break;
    }
  };

  const renderPreview = () => {
    // Simple markdown preview (you can enhance this with a proper markdown parser)
    return content
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/\n/g, '<br>');
  };

  const toolbarButtons = [
    { icon: Bold, action: () => formatText('bold'), title: 'Bold' },
    { icon: Italic, action: () => formatText('italic'), title: 'Italic' },
    { icon: Underline, action: () => formatText('underline'), title: 'Underline' },
    { type: 'separator' },
    { icon: Heading1, action: () => formatText('h1'), title: 'Heading 1' },
    { icon: Heading2, action: () => formatText('h2'), title: 'Heading 2' },
    { icon: Heading3, action: () => formatText('h3'), title: 'Heading 3' },
    { type: 'separator' },
    { icon: List, action: () => formatText('ul'), title: 'Bullet List' },
    { icon: ListOrdered, action: () => formatText('ol'), title: 'Numbered List' },
    { icon: Quote, action: () => formatText('quote'), title: 'Quote' },
    { type: 'separator' },
    { icon: Code, action: () => formatText('code'), title: 'Inline Code' },
    { icon: Link, action: () => formatText('link'), title: 'Link' },
    { icon: Image, action: () => formatText('image'), title: 'Image' },
  ];

  return (
    <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-800">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
        <div className="flex items-center space-x-1">
          {toolbarButtons.map((button, index) => {
            if (button.type === 'separator') {
              return (
                <div 
                  key={index} 
                  className="w-px h-6 bg-gray-300 dark:bg-gray-500 mx-2"
                />
              );
            }
            
            const Icon = button.icon;
            return (
              <button
                key={index}
                onClick={button.action}
                title={button.title}
                className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                <Icon className="w-4 h-4 text-gray-600 dark:text-gray-300" />
              </button>
            );
          })}
        </div>
        
        <div className="flex items-center space-x-2">
          {showAIAssist && (
            <button
              onClick={() => onAIAssist?.(content)}
              className="flex items-center space-x-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 rounded-md hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors duration-200"
            >
              <Sparkles className="w-4 h-4" />
              <span className="text-sm font-medium">AI Assist</span>
            </button>
          )}
          
          <button
            onClick={() => setIsPreview(!isPreview)}
            className={`flex items-center space-x-1 px-3 py-1 rounded-md transition-colors duration-200 ${
              isPreview 
                ? 'bg-indigo-600 text-white' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
            }`}
          >
            {isPreview ? (
              <>
                <Edit3 className="w-4 h-4" />
                <span className="text-sm font-medium">Edit</span>
              </>
            ) : (
              <>
                <Eye className="w-4 h-4" />
                <span className="text-sm font-medium">Preview</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Editor/Preview Area */}
      <div className="relative">
        {isPreview ? (
          <div 
            className="p-4 min-h-[400px] prose prose-lg max-w-none dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: renderPreview() }}
          />
        ) : (
          <textarea
            ref={textareaRef}
            value={content}
            onChange={handleContentChange}
            placeholder={placeholder}
            className="w-full p-4 min-h-[400px] resize-none border-none outline-none bg-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            style={{ fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace' }}
          />
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>Words: {content.split(/\s+/).filter(word => word.length > 0).length}</span>
          <span>Characters: {content.length}</span>
          <span>Reading time: ~{Math.ceil(content.split(/\s+/).length / 200)} min</span>
        </div>
        
        <div className="text-xs">
          Markdown supported • Ctrl+B for bold • Ctrl+I for italic
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;
