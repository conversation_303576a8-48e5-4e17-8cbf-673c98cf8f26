# Multi-stage build for Rivsy Blog Platform

# Stage 1: Build the client
FROM node:18-alpine AS client-builder

WORKDIR /app/client

# Copy client package files
COPY client/package*.json ./

# Install client dependencies
RUN npm ci --only=production

# Copy client source code
COPY client/ ./

# Build the client
RUN npm run build

# Stage 2: Setup the server
FROM node:18-alpine AS server-setup

WORKDIR /app/server

# Copy server package files
COPY server/package*.json ./

# Install server dependencies
RUN npm ci --only=production

# Copy server source code
COPY server/ ./

# Stage 3: Final production image
FROM node:18-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S rivsy -u 1001

WORKDIR /app

# Copy built client from client-builder stage
COPY --from=client-builder --chown=rivsy:nodejs /app/client/dist ./client/dist

# Copy server from server-setup stage
COPY --from=server-setup --chown=rivsy:nodejs /app/server ./server

# Copy root package.json and other config files
COPY --chown=rivsy:nodejs package*.json ./
COPY --chown=rivsy:nodejs .env.example ./

# Create necessary directories
RUN mkdir -p uploads logs && chown -R rivsy:nodejs uploads logs

# Switch to non-root user
USER rivsy

# Expose ports
EXPOSE 3000 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node server/healthcheck.js || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "server/index.js"]

# Labels for metadata
LABEL maintainer="Rivsy Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Rivsy Blog Platform - Modern MERN stack blogging platform"
LABEL org.opencontainers.image.source="https://github.com/rivsy/rivsy"
LABEL org.opencontainers.image.documentation="https://github.com/rivsy/rivsy/blob/main/README.md"
LABEL org.opencontainers.image.licenses="MIT"
