# Rivsy Deployment Guide

This guide covers various deployment options for the Rivsy blog platform.

## 🚀 Quick Deployment Options

### 1. Docker Deployment (Recommended)

#### Prerequisites
- Docker and Docker Compose installed
- Domain name (for production)
- SSL certificates (for HTTPS)

#### Development Deployment
```bash
# Clone the repository
git clone <your-repo-url>
cd rivsy

# Copy environment file
cp .env.example .env

# Edit .env with your configuration
nano .env

# Start with Docker Compose
docker-compose up -d
```

#### Production Deployment
```bash
# Build and start production containers
docker-compose -f docker-compose.yml --profile production up -d

# Or use the production override
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 2. Manual Deployment

#### Server Requirements
- Node.js 18+
- MongoDB 5.0+
- Redis (optional, for caching)
- Nginx (for reverse proxy)
- SSL certificate

#### Installation Steps
```bash
# 1. Clone and setup
git clone <your-repo-url>
cd rivsy
npm run setup

# 2. Install dependencies
npm run install-all

# 3. Build client
npm run build

# 4. Start production server
npm start
```

## 🌐 Platform-Specific Deployments

### Vercel (Frontend + Serverless)

1. **Frontend Deployment**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy frontend
cd client
vercel --prod
```

2. **API Routes** (Optional)
Create `api/` directory in client for serverless functions.

### Heroku (Full Stack)

1. **Prepare for Heroku**
```bash
# Create Procfile
echo "web: cd server && npm start" > Procfile

# Create heroku-postbuild script in package.json
"heroku-postbuild": "npm run install-all && npm run build"
```

2. **Deploy to Heroku**
```bash
# Create Heroku app
heroku create your-app-name

# Add MongoDB addon
heroku addons:create mongolab:sandbox

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-jwt-secret
heroku config:set REFRESH_TOKEN_SECRET=your-refresh-secret

# Deploy
git push heroku main
```

### DigitalOcean App Platform

1. **Connect Repository**
   - Link your GitHub repository
   - Select the main branch

2. **Configure Build Settings**
   - Build Command: `npm run install-all && npm run build`
   - Run Command: `cd server && npm start`

3. **Environment Variables**
   - Add all required environment variables
   - Configure database connection

### AWS (EC2 + RDS + S3)

1. **EC2 Setup**
```bash
# Connect to EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Clone and setup application
git clone <your-repo-url>
cd rivsy
npm run setup
```

2. **Database Setup (RDS)**
   - Create MongoDB Atlas cluster or use DocumentDB
   - Update MONGODB_URI in environment

3. **File Storage (S3)**
   - Create S3 bucket for uploads
   - Configure AWS credentials
   - Update file upload service

### Google Cloud Platform

1. **App Engine Deployment**
```yaml
# app.yaml
runtime: nodejs18

env_variables:
  NODE_ENV: production
  MONGODB_URI: your-mongodb-uri
  JWT_SECRET: your-jwt-secret

automatic_scaling:
  min_instances: 1
  max_instances: 10
```

2. **Deploy**
```bash
gcloud app deploy
```

## 🔧 Production Configuration

### Environment Variables

#### Required
```env
NODE_ENV=production
MONGODB_URI=********************************:port/database
JWT_SECRET=your-super-secure-jwt-secret
REFRESH_TOKEN_SECRET=your-super-secure-refresh-secret
CLIENT_URL=https://yourdomain.com
SERVER_URL=https://api.yourdomain.com
```

#### Optional
```env
# AI Integration
OPENAI_API_KEY=your-openai-key
AZURE_API_KEY=your-azure-key

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage
AWS_S3_BUCKET=your-bucket-name
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
```

### Nginx Configuration

```nginx
# /etc/nginx/sites-available/rivsy
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # API routes
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location / {
        root /path/to/rivsy/client/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Uploads
    location /uploads {
        alias /path/to/rivsy/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

### PM2 Configuration

```json
{
  "name": "rivsy",
  "script": "server/index.js",
  "instances": "max",
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": 5000
  },
  "error_file": "logs/err.log",
  "out_file": "logs/out.log",
  "log_file": "logs/combined.log",
  "time": true
}
```

Start with PM2:
```bash
pm2 start ecosystem.config.json
pm2 save
pm2 startup
```

## 📊 Monitoring and Maintenance

### Health Checks
- API: `GET /health`
- Database connectivity
- File system permissions
- Memory usage

### Logging
- Application logs: `logs/`
- Error tracking: Sentry integration
- Performance monitoring: New Relic

### Backups
- Database: Daily automated backups
- Uploads: S3 versioning
- Configuration: Git repository

### Updates
```bash
# Pull latest changes
git pull origin main

# Install dependencies
npm run install-all

# Build client
npm run build

# Restart server
pm2 restart rivsy
```

## 🔒 Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers implemented
- [ ] Regular security updates
- [ ] Backup strategy in place
- [ ] Monitoring and alerting setup

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version (18+)
   - Clear node_modules and reinstall
   - Check for missing environment variables

2. **Database Connection**
   - Verify MongoDB URI
   - Check network connectivity
   - Ensure database user has proper permissions

3. **Performance Issues**
   - Enable Redis caching
   - Optimize database queries
   - Use CDN for static assets
   - Enable gzip compression

### Support
- Documentation: README.md
- Issues: GitHub Issues
- Community: Discord/Slack
- Email: <EMAIL>

---

Happy deploying! 🚀
