# Rivsy Setup Guide

This guide will walk you through setting up the Rivsy blogging platform from scratch.

## 🎯 Quick Start (5 minutes)

### 1. Prerequisites Check
```bash
# Check Node.js version (should be 18+)
node --version

# Check npm version (should be 8+)
npm --version

# Check if MongoDB is installed
mongod --version
```

### 2. <PERSON><PERSON> and Install
```bash
# Clone the repository
git clone <your-repo-url>
cd rivsy

# Install all dependencies
npm run install-all
```

### 3. Environment Setup
```bash
# Copy environment template
cp .env.template .env

# Edit .env file with your settings
# Minimum required:
# - MONGODB_URI=mongodb://localhost:27017/rivsy-blog
# - JWT_SECRET=your-secret-key-here
# - REFRESH_TOKEN_SECRET=your-refresh-secret-here
```

### 4. Start Development
```bash
# Start MongoDB (if not running)
# macOS: brew services start mongodb-community
# Ubuntu: sudo systemctl start mongod
# Windows: Start MongoDB service

# Start the application
npm run dev
```

Visit http://localhost:3000 to see your blog!

## 🔧 Detailed Setup

### Database Setup

#### Option 1: Local MongoDB
```bash
# Install MongoDB Community Edition
# macOS:
brew tap mongodb/brew
brew install mongodb-community

# Ubuntu:
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### Option 2: MongoDB Atlas (Cloud)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free account
3. Create a new cluster
4. Get your connection string
5. Update `MONGODB_URI` in `.env`

### AI Integration Setup

#### Option 1: Azure OpenAI
1. Create an Azure account
2. Create an OpenAI resource
3. Get your API key and endpoint
4. Update `.env`:
```env
AZURE_API_KEY=your-api-key
AZURE_API_BASE=https://your-resource.openai.azure.com/
AZURE_API_VERSION=2023-12-01-preview
AZURE_DEPLOYMENT_NAME=gpt-4
```

#### Option 2: OpenAI
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account and get API key
3. Update `.env`:
```env
OPENAI_API_KEY=your-openai-api-key
```

#### Option 3: AWS Bedrock
1. Set up AWS account
2. Enable Bedrock service
3. Create IAM user with Bedrock permissions
4. Update `.env`:
```env
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
```

### Email Configuration (Optional)

#### SMTP Setup
```env
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Blog Name
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

#### SendGrid Setup
```env
SENDGRID_API_KEY=your-sendgrid-api-key
```

### File Storage Setup (Optional)

#### AWS S3
```env
AWS_S3_BUCKET=your-bucket-name
AWS_S3_REGION=us-east-1
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key
```

#### Cloudinary
```env
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

## 🚀 Production Deployment

### 1. Build for Production
```bash
# Build the client
cd client
npm run build

# Test production build locally
cd ..
NODE_ENV=production npm start
```

### 2. Environment Variables for Production
```env
NODE_ENV=production
MONGODB_URI=your-production-mongodb-uri
JWT_SECRET=super-secure-production-secret
REFRESH_TOKEN_SECRET=super-secure-refresh-secret
CLIENT_URL=https://yourdomain.com
SERVER_URL=https://api.yourdomain.com
```

### 3. Deployment Options

#### Vercel (Frontend + Serverless Functions)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

#### Heroku (Full Stack)
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-uri
heroku config:set JWT_SECRET=your-jwt-secret

# Deploy
git push heroku main
```

#### DigitalOcean App Platform
1. Connect your GitHub repository
2. Configure environment variables
3. Set build and run commands
4. Deploy

#### Docker Deployment
```bash
# Build Docker image
docker build -t rivsy .

# Run container
docker run -p 3000:3000 -p 5000:5000 --env-file .env rivsy
```

## 🔍 Troubleshooting

### Common Issues

#### MongoDB Connection Error
```bash
# Check if MongoDB is running
sudo systemctl status mongod

# Check MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Restart MongoDB
sudo systemctl restart mongod
```

#### Port Already in Use
```bash
# Find process using port 3000 or 5000
lsof -i :3000
lsof -i :5000

# Kill the process
kill -9 <PID>
```

#### npm Install Fails
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Build Errors
```bash
# Check Node.js version
node --version

# Update to Node.js 18+
nvm install 18
nvm use 18

# Clear build cache
rm -rf client/dist
rm -rf client/node_modules/.vite
```

### Performance Optimization

#### Database Indexing
```javascript
// Run in MongoDB shell
use rivsy-blog

// Create indexes for better performance
db.posts.createIndex({ "slug": 1 })
db.posts.createIndex({ "author": 1 })
db.posts.createIndex({ "status": 1 })
db.posts.createIndex({ "publishedAt": -1 })
db.users.createIndex({ "email": 1 })
```

#### Client-Side Optimization
- Enable gzip compression
- Use CDN for static assets
- Implement lazy loading
- Optimize images

## 📊 Monitoring and Analytics

### Application Monitoring
- Set up error tracking (Sentry)
- Monitor performance (New Relic)
- Set up uptime monitoring

### SEO Monitoring
- Google Search Console
- Google Analytics
- Bing Webmaster Tools

## 🔐 Security Checklist

- [ ] Use HTTPS in production
- [ ] Set secure JWT secrets
- [ ] Enable rate limiting
- [ ] Validate all inputs
- [ ] Use CORS properly
- [ ] Keep dependencies updated
- [ ] Regular security audits

## 📞 Getting Help

If you need help with setup:

1. Check this guide thoroughly
2. Search existing [GitHub Issues](https://github.com/your-repo/issues)
3. Create a new issue with:
   - Your operating system
   - Node.js version
   - Error messages
   - Steps to reproduce
4. Join our [Discord community](https://discord.gg/rivsy)

---

Happy blogging with Rivsy! 🎉
