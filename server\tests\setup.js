const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// Setup test database
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
});

// Clean up after each test
afterEach(async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

// Test utilities
global.testUtils = {
  // Create test user
  createTestUser: async (userData = {}) => {
    const User = require('../models/User');
    const bcrypt = require('bcryptjs');
    
    const defaultUser = {
      name: 'Test User',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 12),
      isVerified: true,
      role: 'user'
    };
    
    const user = new User({ ...defaultUser, ...userData });
    await user.save();
    return user;
  },

  // Create test post
  createTestPost: async (postData = {}, author = null) => {
    const Post = require('../models/Post');
    
    if (!author) {
      author = await global.testUtils.createTestUser();
    }
    
    const defaultPost = {
      title: 'Test Post',
      content: 'This is a test post content.',
      excerpt: 'Test excerpt',
      author: author._id,
      status: 'published',
      publishedAt: new Date()
    };
    
    const post = new Post({ ...defaultPost, ...postData });
    await post.save();
    return post;
  },

  // Generate JWT token for testing
  generateAuthToken: (user) => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  },

  // Create authenticated request headers
  authHeaders: (user) => {
    const token = global.testUtils.generateAuthToken(user);
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }
};

// Mock external services
jest.mock('../services/aiService', () => ({
  generateBlogPost: jest.fn().mockResolvedValue('Generated content'),
  generateSEOMetadata: jest.fn().mockResolvedValue({
    metaTitle: 'Test Title',
    metaDescription: 'Test Description',
    keywords: ['test', 'keywords']
  }),
  isConfigured: jest.fn().mockReturnValue(true),
  healthCheck: jest.fn().mockResolvedValue(true)
}));

jest.mock('../services/cacheService', () => ({
  get: jest.fn().mockResolvedValue(null),
  set: jest.fn().mockResolvedValue(true),
  del: jest.fn().mockResolvedValue(true),
  clear: jest.fn().mockResolvedValue(true),
  getStats: jest.fn().mockReturnValue({
    memory: { keys: 0, hits: 0, misses: 0, hitRate: 0 },
    redis: { connected: false }
  }),
  healthCheck: jest.fn().mockResolvedValue({ healthy: true })
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.REFRESH_TOKEN_SECRET = 'test-refresh-secret';
process.env.BCRYPT_SALT_ROUNDS = '10';

// Suppress console logs during tests
if (process.env.NODE_ENV === 'test') {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
}
