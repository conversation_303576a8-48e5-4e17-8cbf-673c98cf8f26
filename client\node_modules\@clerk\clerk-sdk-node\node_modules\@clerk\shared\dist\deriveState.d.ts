import * as _clerk_types from '@clerk/types';
import { Resources, InitialState, UserResource, ActiveSessionResource, OrganizationResource } from '@clerk/types';

/**
 * Derives authentication state based on the current rendering context (SSR or client-side).
 */
declare const deriveState: (clerkLoaded: boolean, state: Resources, initialState: InitialState | undefined) => {
    userId: string | null | undefined;
    user: UserResource | null | undefined;
    sessionId: string | null | undefined;
    session: ActiveSessionResource | null | undefined;
    organization: OrganizationResource | null | undefined;
    orgId: string | null | undefined;
    orgRole: string | null | undefined;
    orgSlug: string | null | undefined;
    orgPermissions: _clerk_types.Autocomplete<_clerk_types.OrganizationSystemPermissionKey, string>[] | null | undefined;
    actor: _clerk_types.ActJWTClaim | null | undefined;
    factorVerificationAge: [number, number] | null;
};

export { deriveState };
