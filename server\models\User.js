const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email',
    ],
  },
  password: {
    type: String,
    required: function() {
      // Password is only required if no Clerk ID (traditional auth)
      return !this.clerkId;
    },
    minlength: [6, 'Password must be at least 6 characters'],
    select: false, // Don't include password in queries by default
  },
  clerkId: {
    type: String,
    unique: true,
    sparse: true, // Allows null values while maintaining uniqueness
    index: true
  },
  firstName: {
    type: String,
    trim: true,
    maxlength: [25, 'First name cannot be more than 25 characters']
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: [25, 'Last name cannot be more than 25 characters']
  },
  imageUrl: {
    type: String,
    default: ''
  },
  lastLoginAt: {
    type: Date,
    default: Date.now
  },
  avatar: {
    type: String,
    default: '',
  },
  bio: {
    type: String,
    maxlength: [500, 'Bio cannot be more than 500 characters'],
    default: '',
  },
  role: {
    type: String,
    enum: ['user', 'author', 'admin'],
    default: 'user',
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  verificationToken: String,
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  
  // Blog settings
  blogTitle: {
    type: String,
    default: '',
    maxlength: [100, 'Blog title cannot be more than 100 characters'],
  },
  blogDescription: {
    type: String,
    default: '',
    maxlength: [500, 'Blog description cannot be more than 500 characters'],
  },
  blogDomain: {
    type: String,
    default: '',
    unique: true,
    sparse: true, // Allow multiple null values
  },
  
  // Social links
  socialLinks: {
    twitter: { type: String, default: '' },
    linkedin: { type: String, default: '' },
    github: { type: String, default: '' },
    website: { type: String, default: '' },
  },
  
  // Settings
  settings: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'auto',
    },
    emailNotifications: {
      type: Boolean,
      default: true,
    },
    publicProfile: {
      type: Boolean,
      default: true,
    },
  },
  
  // Analytics
  totalViews: {
    type: Number,
    default: 0,
  },
  totalPosts: {
    type: Number,
    default: 0,
  },
  
}, {
  timestamps: true,
});

// Index for better query performance
userSchema.index({ email: 1 });
userSchema.index({ clerkId: 1 });
userSchema.index({ blogDomain: 1 });
userSchema.index({ createdAt: -1 });

// Hash password before saving (only for traditional auth users)
userSchema.pre('save', async function(next) {
  // Skip password hashing for Clerk users
  if (this.clerkId || !this.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Generate blog domain from name
userSchema.methods.generateBlogDomain = function() {
  if (!this.blogDomain) {
    const domain = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    this.blogDomain = domain;
  }
  return this.blogDomain;
};

// Get public profile
userSchema.methods.getPublicProfile = function() {
  return {
    _id: this._id,
    name: this.name,
    avatar: this.avatar,
    bio: this.bio,
    blogTitle: this.blogTitle,
    blogDescription: this.blogDescription,
    blogDomain: this.blogDomain,
    socialLinks: this.socialLinks,
    totalViews: this.totalViews,
    totalPosts: this.totalPosts,
    createdAt: this.createdAt,
  };
};

// Remove sensitive data when converting to JSON
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.verificationToken;
  delete user.resetPasswordToken;
  delete user.resetPasswordExpire;
  return user;
};

module.exports = mongoose.model('User', userSchema);
