import type { Web3WalletJSON } from './JSON';
import { Verification } from './Verification';
export declare class Web3Wallet {
    readonly id: string;
    readonly web3Wallet: string;
    readonly verification: Verification | null;
    constructor(id: string, web3Wallet: string, verification: Verification | null);
    static fromJSON(data: Web3WalletJSON): Web3Wallet;
}
//# sourceMappingURL=Web3Wallet.d.ts.map