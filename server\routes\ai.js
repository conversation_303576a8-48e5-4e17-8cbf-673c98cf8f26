const express = require('express');
const { body, validationResult } = require('express-validator');
const { protect } = require('../middleware/auth');
const aiService = require('../services/aiService');

const router = express.Router();

// @desc    Check AI service health
// @route   GET /api/ai/health
// @access  Private
router.get('/health', protect, async (req, res, next) => {
  try {
    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured. Please set up API keys in environment variables.',
      });
    }

    await aiService.healthCheck();

    res.json({
      success: true,
      message: 'AI service is healthy and ready',
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      message: 'AI service is currently unavailable',
      error: error.message,
    });
  }
});

// @desc    Generate blog post content
// @route   POST /api/ai/generate-post
// @access  Private
router.post('/generate-post', protect, [
  body('topic')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Topic must be between 3 and 200 characters'),
  body('tone')
    .optional()
    .isIn(['professional', 'casual', 'friendly', 'authoritative', 'conversational'])
    .withMessage('Invalid tone'),
  body('length')
    .optional()
    .isIn(['short', 'medium', 'long'])
    .withMessage('Invalid length'),
  body('audience')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Audience description too long'),
  body('keywords')
    .optional()
    .isArray()
    .withMessage('Keywords must be an array'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { topic, tone, length, audience, keywords, includeOutline } = req.body;

    const content = await aiService.generateBlogPost(topic, {
      tone,
      length,
      audience,
      keywords: keywords || [],
      includeOutline: includeOutline !== false,
    });

    res.json({
      success: true,
      message: 'Blog post generated successfully',
      data: {
        content,
        topic,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Generate blog post outline
// @route   POST /api/ai/generate-outline
// @access  Private
router.post('/generate-outline', protect, [
  body('topic')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Topic must be between 3 and 200 characters'),
  body('sections')
    .optional()
    .isInt({ min: 3, max: 10 })
    .withMessage('Sections must be between 3 and 10'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { topic, sections, audience, keywords } = req.body;

    const outline = await aiService.generateBlogOutline(topic, {
      sections: sections || 5,
      audience: audience || 'general',
      keywords: keywords || [],
    });

    res.json({
      success: true,
      message: 'Blog outline generated successfully',
      data: {
        outline,
        topic,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Improve existing content
// @route   POST /api/ai/improve-content
// @access  Private
router.post('/improve-content', protect, [
  body('content')
    .trim()
    .isLength({ min: 100 })
    .withMessage('Content must be at least 100 characters'),
  body('improvements')
    .optional()
    .isArray()
    .withMessage('Improvements must be an array'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { content, improvements } = req.body;

    const improvedContent = await aiService.improveBlogPost(
      content,
      improvements || ['seo', 'readability']
    );

    res.json({
      success: true,
      message: 'Content improved successfully',
      data: {
        originalContent: content,
        improvedContent,
        improvements: improvements || ['seo', 'readability'],
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Generate SEO metadata
// @route   POST /api/ai/generate-seo
// @access  Private
router.post('/generate-seo', protect, [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('content')
    .trim()
    .isLength({ min: 100 })
    .withMessage('Content must be at least 100 characters'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { title, content } = req.body;

    const seoData = await aiService.generateSEOMetadata(content, title);

    res.json({
      success: true,
      message: 'SEO metadata generated successfully',
      data: seoData,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Generate content ideas
// @route   POST /api/ai/content-ideas
// @access  Private
router.post('/content-ideas', protect, [
  body('niche')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Niche must be between 2 and 100 characters'),
  body('count')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Count must be between 1 and 20'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { niche, count } = req.body;

    const ideas = await aiService.generateContentIdeas(niche, count || 10);

    res.json({
      success: true,
      message: 'Content ideas generated successfully',
      data: {
        ideas,
        niche,
        count: count || 10,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Generate social media posts
// @route   POST /api/ai/social-posts
// @access  Private
router.post('/social-posts', protect, [
  body('blogTitle')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Blog title must be between 3 and 200 characters'),
  body('blogContent')
    .trim()
    .isLength({ min: 100 })
    .withMessage('Blog content must be at least 100 characters'),
  body('platforms')
    .optional()
    .isArray()
    .withMessage('Platforms must be an array'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { blogTitle, blogContent, platforms } = req.body;

    const socialPosts = await aiService.generateSocialMediaPosts(
      blogTitle,
      blogContent,
      platforms || ['twitter', 'linkedin', 'facebook']
    );

    res.json({
      success: true,
      message: 'Social media posts generated successfully',
      data: {
        socialPosts,
        platforms: platforms || ['twitter', 'linkedin', 'facebook'],
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Analyze content for SEO
// @route   POST /api/ai/analyze-seo
// @access  Private
router.post('/analyze-seo', protect, [
  body('content')
    .trim()
    .isLength({ min: 100 })
    .withMessage('Content must be at least 100 characters'),
  body('targetKeywords')
    .optional()
    .isArray()
    .withMessage('Target keywords must be an array'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { content, targetKeywords } = req.body;

    const analysis = await aiService.analyzeContentSEO(
      content,
      targetKeywords || []
    );

    res.json({
      success: true,
      message: 'SEO analysis completed successfully',
      data: {
        analysis,
        targetKeywords: targetKeywords || [],
        analyzedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Generate FAQs for topic
// @route   POST /api/ai/generate-faqs
// @access  Private
router.post('/generate-faqs', protect, [
  body('topic')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Topic must be between 3 and 200 characters'),
  body('count')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Count must be between 1 and 10'),
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    if (!aiService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: 'AI service not configured',
      });
    }

    const { topic, count } = req.body;

    const faqs = await aiService.generateFAQs(topic, count || 5);

    res.json({
      success: true,
      message: 'FAQs generated successfully',
      data: {
        faqs,
        topic,
        count: count || 5,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
