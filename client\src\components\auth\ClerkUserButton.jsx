import React from 'react';
import { UserButton } from '@clerk/clerk-react';
import { useAuth } from '../../contexts/ClerkAuthContext';

const ClerkUserButton = () => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="hidden md:block text-right">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {user?.name}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {user?.email}
        </p>
      </div>
      
      <UserButton
        appearance={{
          elements: {
            avatarBox: 'w-10 h-10',
            userButtonPopoverCard: 'shadow-lg border border-gray-200',
            userButtonPopoverActionButton: 
              'hover:bg-gray-50 text-gray-700 text-sm',
            userButtonPopoverActionButtonText: 'text-sm',
            userButtonPopoverFooter: 'hidden',
          },
        }}
        userProfileMode="navigation"
        userProfileUrl="/profile"
        afterSignOutUrl="/"
        showName={false}
      />
    </div>
  );
};

export default ClerkUserButton;
