{"name": "rivsy-blog-platform", "version": "1.0.0", "description": "A complete MERN stack blogging platform that competes with Superblog.ai", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test", "seed": "cd server && npm run seed", "lint": "npm run lint:server && npm run lint:client", "lint:server": "cd server && npm run lint", "lint:client": "cd client && npm run lint", "setup": "node install.js", "clean": "rm -rf node_modules server/node_modules client/node_modules", "reset": "npm run clean && npm run install-all", "docker:build": "docker build -t rivsy .", "docker:run": "docker run -p 3000:3000 -p 5000:5000 --env-file .env rivsy"}, "keywords": ["blog", "mern", "react", "nodejs", "mongodb", "express", "ai", "seo", "cms"], "author": "Rivsy Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}