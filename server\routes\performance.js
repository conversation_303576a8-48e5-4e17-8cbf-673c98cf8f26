const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const performanceService = require('../services/performanceService');
const cacheService = require('../services/cacheService');

const router = express.Router();

// @desc    Get performance metrics
// @route   GET /api/performance/metrics
// @access  Private/Admin
router.get('/metrics', protect, authorize('admin'), async (req, res, next) => {
  try {
    const metrics = performanceService.getMetrics();
    
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get health status
// @route   GET /api/performance/health
// @access  Private/Admin
router.get('/health', protect, authorize('admin'), async (req, res, next) => {
  try {
    const health = performanceService.getHealthStatus();
    
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get database metrics
// @route   GET /api/performance/database
// @access  Private/Admin
router.get('/database', protect, authorize('admin'), async (req, res, next) => {
  try {
    const dbMetrics = await performanceService.getDatabaseMetrics();
    
    res.json({
      success: true,
      data: dbMetrics
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get cache statistics
// @route   GET /api/performance/cache
// @access  Private/Admin
router.get('/cache', protect, authorize('admin'), async (req, res, next) => {
  try {
    const cacheStats = cacheService.getStats();
    const cacheHealth = await cacheService.healthCheck();
    
    res.json({
      success: true,
      data: {
        stats: cacheStats,
        health: cacheHealth
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get performance recommendations
// @route   GET /api/performance/recommendations
// @access  Private/Admin
router.get('/recommendations', protect, authorize('admin'), async (req, res, next) => {
  try {
    const recommendations = performanceService.getRecommendations();
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Reset performance metrics
// @route   POST /api/performance/reset
// @access  Private/Admin
router.post('/reset', protect, authorize('admin'), async (req, res, next) => {
  try {
    performanceService.reset();
    
    res.json({
      success: true,
      message: 'Performance metrics reset successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Clear cache
// @route   POST /api/performance/cache/clear
// @access  Private/Admin
router.post('/cache/clear', protect, authorize('admin'), async (req, res, next) => {
  try {
    await cacheService.clear();
    
    res.json({
      success: true,
      message: 'Cache cleared successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Warm cache
// @route   POST /api/performance/cache/warm
// @access  Private/Admin
router.post('/cache/warm', protect, authorize('admin'), async (req, res, next) => {
  try {
    await cacheService.warmCache();
    
    res.json({
      success: true,
      message: 'Cache warmed successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Invalidate cache by pattern
// @route   POST /api/performance/cache/invalidate
// @access  Private/Admin
router.post('/cache/invalidate', protect, authorize('admin'), async (req, res, next) => {
  try {
    const { pattern } = req.body;
    
    if (!pattern) {
      return res.status(400).json({
        success: false,
        message: 'Pattern is required'
      });
    }
    
    await cacheService.invalidatePattern(pattern);
    
    res.json({
      success: true,
      message: `Cache invalidated for pattern: ${pattern}`
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get system information
// @route   GET /api/performance/system
// @access  Private/Admin
router.get('/system', protect, authorize('admin'), async (req, res, next) => {
  try {
    const os = require('os');
    const process = require('process');
    
    const systemInfo = {
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        uptime: process.uptime(),
        cwd: process.cwd()
      },
      os: {
        type: os.type(),
        platform: os.platform(),
        arch: os.arch(),
        release: os.release(),
        hostname: os.hostname(),
        uptime: os.uptime(),
        loadavg: os.loadavg(),
        totalmem: os.totalmem(),
        freemem: os.freemem(),
        cpus: os.cpus().length
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    };
    
    res.json({
      success: true,
      data: systemInfo
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get comprehensive performance report
// @route   GET /api/performance/report
// @access  Private/Admin
router.get('/report', protect, authorize('admin'), async (req, res, next) => {
  try {
    const [
      metrics,
      health,
      dbMetrics,
      cacheStats,
      recommendations
    ] = await Promise.all([
      performanceService.getMetrics(),
      performanceService.getHealthStatus(),
      performanceService.getDatabaseMetrics(),
      cacheService.getStats(),
      performanceService.getRecommendations()
    ]);
    
    const report = {
      overview: {
        status: health.status,
        uptime: metrics.system.uptimeFormatted,
        totalRequests: metrics.requests.total,
        errorRate: metrics.requests.errorRate,
        avgResponseTime: metrics.requests.avgResponseTime
      },
      performance: metrics,
      health: health,
      database: dbMetrics,
      cache: cacheStats,
      recommendations: recommendations,
      generatedAt: new Date().toISOString()
    };
    
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
