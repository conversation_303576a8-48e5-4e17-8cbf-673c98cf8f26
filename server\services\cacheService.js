const NodeCache = require('node-cache');
const redis = require('redis');

class CacheService {
  constructor() {
    // In-memory cache as fallback
    this.memoryCache = new NodeCache({
      stdTTL: 600, // 10 minutes default TTL
      checkperiod: 120, // Check for expired keys every 2 minutes
      useClones: false
    });

    // Redis cache (if available)
    this.redisClient = null;
    this.initRedis();
  }

  async initRedis() {
    try {
      if (process.env.REDIS_URL) {
        this.redisClient = redis.createClient({
          url: process.env.REDIS_URL
        });

        this.redisClient.on('error', (err) => {
          console.warn('Redis connection error:', err);
          this.redisClient = null;
        });

        this.redisClient.on('connect', () => {
          console.log('✅ Redis cache connected');
        });

        await this.redisClient.connect();
      }
    } catch (error) {
      console.warn('⚠️ Redis not available, using memory cache only');
      this.redisClient = null;
    }
  }

  // Get cached value
  async get(key) {
    try {
      // Try Redis first
      if (this.redisClient) {
        const value = await this.redisClient.get(key);
        if (value) {
          return JSON.parse(value);
        }
      }

      // Fallback to memory cache
      return this.memoryCache.get(key);
    } catch (error) {
      console.warn('Cache get error:', error);
      return null;
    }
  }

  // Set cached value
  async set(key, value, ttl = 600) {
    try {
      const serializedValue = JSON.stringify(value);

      // Set in Redis
      if (this.redisClient) {
        await this.redisClient.setEx(key, ttl, serializedValue);
      }

      // Set in memory cache
      this.memoryCache.set(key, value, ttl);
      
      return true;
    } catch (error) {
      console.warn('Cache set error:', error);
      return false;
    }
  }

  // Delete cached value
  async del(key) {
    try {
      // Delete from Redis
      if (this.redisClient) {
        await this.redisClient.del(key);
      }

      // Delete from memory cache
      this.memoryCache.del(key);
      
      return true;
    } catch (error) {
      console.warn('Cache delete error:', error);
      return false;
    }
  }

  // Clear all cache
  async clear() {
    try {
      // Clear Redis
      if (this.redisClient) {
        await this.redisClient.flushAll();
      }

      // Clear memory cache
      this.memoryCache.flushAll();
      
      return true;
    } catch (error) {
      console.warn('Cache clear error:', error);
      return false;
    }
  }

  // Get cache statistics
  getStats() {
    const memoryStats = this.memoryCache.getStats();
    
    return {
      memory: {
        keys: memoryStats.keys,
        hits: memoryStats.hits,
        misses: memoryStats.misses,
        hitRate: memoryStats.hits / (memoryStats.hits + memoryStats.misses) || 0
      },
      redis: {
        connected: !!this.redisClient,
        url: process.env.REDIS_URL ? 'configured' : 'not configured'
      }
    };
  }

  // Cache middleware for Express
  middleware(ttl = 600) {
    return async (req, res, next) => {
      // Only cache GET requests
      if (req.method !== 'GET') {
        return next();
      }

      // Skip caching for authenticated requests
      if (req.headers.authorization) {
        return next();
      }

      const cacheKey = `route:${req.originalUrl}`;
      
      try {
        const cachedResponse = await this.get(cacheKey);
        
        if (cachedResponse) {
          res.set(cachedResponse.headers);
          res.set('X-Cache', 'HIT');
          return res.status(cachedResponse.status).send(cachedResponse.body);
        }

        // Store original res.send
        const originalSend = res.send;
        
        res.send = function(body) {
          // Cache successful responses
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const responseToCache = {
              status: res.statusCode,
              headers: res.getHeaders(),
              body: body
            };
            
            // Don't await to avoid blocking response
            cacheService.set(cacheKey, responseToCache, ttl).catch(console.warn);
          }
          
          res.set('X-Cache', 'MISS');
          originalSend.call(this, body);
        };

        next();
      } catch (error) {
        console.warn('Cache middleware error:', error);
        next();
      }
    };
  }

  // Cache decorator for functions
  cached(ttl = 600) {
    return (target, propertyName, descriptor) => {
      const method = descriptor.value;

      descriptor.value = async function(...args) {
        const cacheKey = `method:${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;
        
        try {
          const cachedResult = await cacheService.get(cacheKey);
          if (cachedResult !== null) {
            return cachedResult;
          }

          const result = await method.apply(this, args);
          await cacheService.set(cacheKey, result, ttl);
          
          return result;
        } catch (error) {
          console.warn('Cache decorator error:', error);
          return await method.apply(this, args);
        }
      };

      return descriptor;
    };
  }

  // Invalidate cache by pattern
  async invalidatePattern(pattern) {
    try {
      if (this.redisClient) {
        const keys = await this.redisClient.keys(pattern);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
        }
      }

      // For memory cache, we need to iterate through all keys
      const memoryKeys = this.memoryCache.keys();
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      
      memoryKeys.forEach(key => {
        if (regex.test(key)) {
          this.memoryCache.del(key);
        }
      });

      return true;
    } catch (error) {
      console.warn('Cache invalidation error:', error);
      return false;
    }
  }

  // Cache warming - preload frequently accessed data
  async warmCache() {
    try {
      console.log('🔥 Warming cache...');
      
      // Cache recent posts
      const Post = require('../models/Post');
      const recentPosts = await Post.find({ status: 'published' })
        .populate('author', 'name avatar')
        .sort({ publishedAt: -1 })
        .limit(10);
      
      await this.set('posts:recent', recentPosts, 1800); // 30 minutes

      // Cache popular posts
      const popularPosts = await Post.find({ status: 'published' })
        .populate('author', 'name avatar')
        .sort({ views: -1 })
        .limit(10);
      
      await this.set('posts:popular', popularPosts, 3600); // 1 hour

      // Cache categories and tags
      const categories = await Post.distinct('categories', { status: 'published' });
      const tags = await Post.distinct('tags', { status: 'published' });
      
      await this.set('meta:categories', categories, 7200); // 2 hours
      await this.set('meta:tags', tags, 7200); // 2 hours

      console.log('✅ Cache warmed successfully');
    } catch (error) {
      console.warn('Cache warming error:', error);
    }
  }

  // Health check
  async healthCheck() {
    try {
      const testKey = 'health:check';
      const testValue = { timestamp: Date.now() };
      
      await this.set(testKey, testValue, 60);
      const retrieved = await this.get(testKey);
      await this.del(testKey);
      
      return {
        healthy: retrieved && retrieved.timestamp === testValue.timestamp,
        stats: this.getStats()
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        stats: this.getStats()
      };
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
