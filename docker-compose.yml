version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: rivsy-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-rivsy-blog}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - rivsy-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: rivsy-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rivsy-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Rivsy Application
  rivsy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rivsy-app
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${PORT:-5000}
      CLIENT_URL: ${CLIENT_URL:-http://localhost:3000}
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DB_NAME:-rivsy-blog}?authSource=admin
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      REFRESH_TOKEN_SECRET: ${REFRESH_TOKEN_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      AZURE_API_KEY: ${AZURE_API_KEY:-}
      AZURE_API_BASE: ${AZURE_API_BASE:-}
      AZURE_DEPLOYMENT_NAME: ${AZURE_DEPLOYMENT_NAME:-}
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER:-}
      SMTP_PASS: ${SMTP_PASS:-}
    ports:
      - "3000:3000"
      - "5000:5000"
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    networks:
      - rivsy-network
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: rivsy-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    networks:
      - rivsy-network
    depends_on:
      - rivsy
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local

networks:
  rivsy-network:
    driver: bridge

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml
version: '3.8'

services:
  rivsy:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - ./server:/app/server
      - ./client:/app/client
      - /app/server/node_modules
      - /app/client/node_modules
    command: npm run dev

  # Development database with no authentication
  mongodb:
    environment:
      MONGO_INITDB_ROOT_USERNAME: ""
      MONGO_INITDB_ROOT_PASSWORD: ""
    command: mongod --noauth
