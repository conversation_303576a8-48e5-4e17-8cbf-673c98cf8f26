<p align="center">
  <a href="https://clerk.com?utm_source=github&utm_medium=clerk_backend" target="_blank" rel="noopener noreferrer">
    <picture>
      <source media="(prefers-color-scheme: dark)" srcset="https://images.clerk.com/static/logo-dark-mode-400x400.png">
      <img src="https://images.clerk.com/static/logo-light-mode-400x400.png" height="64">
    </picture>
  </a>
  <br />
  <h1 align="center">@clerk/backend</h1>
</p>

<div align="center">

[![Chat on Discord](https://img.shields.io/discord/856971667393609759.svg?logo=discord)](https://clerk.com/discord)
[![Clerk documentation](https://img.shields.io/badge/documentation-clerk-green.svg)](https://clerk.com/docs?utm_source=github&utm_medium=clerk_backend)
[![Follow on Twitter](https://img.shields.io/twitter/follow/ClerkDev?style=social)](https://twitter.com/intent/follow?screen_name=ClerkDev)

[Changelog](https://github.com/clerk/javascript/blob/main/packages/backend/CHANGELOG.md)
·
[Report a Bug](https://github.com/clerk/javascript/issues/new?assignees=&labels=needs-triage&projects=&template=BUG_REPORT.yml)
·
[Request a Feature](https://feedback.clerk.com/roadmap)
·
[Get help](https://clerk.com/contact/support?utm_source=github&utm_medium=clerk_backend)

</div>

## Getting Started

[Clerk's](https://clerk.com/?utm_source=github&utm_medium=clerk_backend) JavaScript Backend SDK exposes [Clerk's Backend API](https://clerk.com/docs/reference/backend-api) resources and low-level authentication utilities **for JavaScript environments**.

### Prerequisites

- Node.js `>=18.17.0` (or later) or any V8 isolates runtime
- An existing Clerk application. [Create your account for free](https://dashboard.clerk.com/sign-up?utm_source=github&utm_medium=clerk_backend).

### Installation

The fastest way to get started with `@clerk/backend` is by following the [JavaScript Backend SDK reference documentation](https://clerk.com/docs/references/backend/overview?utm_source=github&utm_medium=clerk_backend).

You'll learn how to install `@clerk/backend` and how to use `createClerkClient()`.

## Usage

For further information, guides, and examples visit the [JavaScript Backend SDK reference documentation](https://clerk.com/docs/references/backend/overview?utm_source=github&utm_medium=clerk_backend). It lists all the available APIs and methods.

## Testing

This project uses [vitest](https://vitest.dev/) as the unit test runner and [msw](https://mswjs.io/) for mocking network requests.

If you need to see which requests are being intercepted by `msw`, you can run the test suite with the following env: `DEBUG_MOCK_REQUESTS=true`

## Support

You can get in touch with us in any of the following ways:

- Join our official community [Discord server](https://clerk.com/discord)
- On [our support page](https://clerk.com/contact/support?utm_source=github&utm_medium=clerk_backend)

## Contributing

We're open to all community contributions! If you'd like to contribute in any way, please read [our contribution guidelines](https://github.com/clerk/javascript/blob/main/docs/CONTRIBUTING.md) and [code of conduct](https://github.com/clerk/javascript/blob/main/docs/CODE_OF_CONDUCT.md).

## Security

`@clerk/backend` follows good practices of security, but 100% security cannot be assured.

`@clerk/backend` is provided **"as is"** without any **warranty**. Use at your own risk.

_For more information and to report security issues, please refer to our [security documentation](https://github.com/clerk/javascript/blob/main/docs/SECURITY.md)._

## License

This project is licensed under the **MIT license**.

See [LICENSE](https://github.com/clerk/javascript/blob/main/packages/backend/LICENSE) for more information.
