const morgan = require('morgan');
const logger = require('../config/logger');

// Custom Morgan token for response time in milliseconds
morgan.token('response-time-ms', (req, res) => {
  if (!req._startTime) {
    return '-';
  }
  const diff = process.hrtime(req._startTime);
  const ms = diff[0] * 1000 + diff[1] * 1e-6;
  return ms.toFixed(2);
});

// Custom Morgan token for request ID
morgan.token('request-id', (req) => {
  return req.id || 'unknown';
});

// Custom Morgan token for user ID
morgan.token('user-id', (req) => {
  return req.user ? req.user._id : 'anonymous';
});

// Custom Morgan token for request body size
morgan.token('body-size', (req) => {
  if (req.body && typeof req.body === 'object') {
    return JSON.stringify(req.body).length;
  }
  return '0';
});

// Define custom Morgan format for detailed logging
const detailedFormat = ':remote-addr - :user-id [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms ms :request-id';

// Define custom Morgan format for development
const devFormat = ':method :url :status :response-time-ms ms - :res[content-length]';

// Create Morgan middleware based on environment
const createMorganMiddleware = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return morgan(isDevelopment ? devFormat : detailedFormat, {
    stream: logger.stream,
    skip: (req, res) => {
      // Skip logging for health checks and static files in production
      if (process.env.NODE_ENV === 'production') {
        return req.url === '/health' || req.url.startsWith('/static');
      }
      return false;
    },
  });
};

// Request ID middleware - adds unique ID to each request
const requestIdMiddleware = (req, res, next) => {
  req.id = generateRequestId();
  res.setHeader('X-Request-ID', req.id);
  next();
};

// Request timing middleware - tracks request start time
const requestTimingMiddleware = (req, res, next) => {
  req._startTime = process.hrtime();
  next();
};

// Enhanced request logging middleware
const enhancedRequestLogging = (req, res, next) => {
  // Store original end function
  const originalEnd = res.end;
  
  // Override end function to log request details
  res.end = function(chunk, encoding) {
    // Calculate response time
    const diff = process.hrtime(req._startTime);
    const responseTime = Math.round(diff[0] * 1000 + diff[1] * 1e-6);
    
    // Log the request using our custom logger
    logger.logRequest(req, res, responseTime);
    
    // Call original end function
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

// Error logging middleware
const errorLoggingMiddleware = (err, req, res, next) => {
  // Log the error with request context
  logger.logError(err, req, {
    requestId: req.id,
    timestamp: new Date().toISOString(),
  });
  
  next(err);
};

// Security event logging middleware
const securityLoggingMiddleware = (req, res, next) => {
  // Log suspicious activities
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
  ];
  
  const url = req.originalUrl || req.url;
  const userAgent = req.get('User-Agent') || '';
  
  // Check for suspicious patterns in URL
  if (suspiciousPatterns.some(pattern => pattern.test(url))) {
    logger.warn('Suspicious Request Detected', {
      type: 'suspicious_url',
      url,
      ip: req.ip,
      userAgent,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    });
  }
  
  // Check for suspicious user agents
  const suspiciousUserAgents = [
    /sqlmap/i,
    /nikto/i,
    /nmap/i,
    /masscan/i,
    /zap/i,
  ];
  
  if (suspiciousUserAgents.some(pattern => pattern.test(userAgent))) {
    logger.warn('Suspicious User Agent Detected', {
      type: 'suspicious_user_agent',
      userAgent,
      ip: req.ip,
      url,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    });
  }
  
  next();
};

// Rate limit logging middleware
const rateLimitLoggingMiddleware = (req, res, next) => {
  // Check if rate limit headers are present
  const remaining = res.get('X-RateLimit-Remaining');
  const limit = res.get('X-RateLimit-Limit');
  
  if (remaining !== undefined && parseInt(remaining) < parseInt(limit) * 0.1) {
    logger.warn('Rate Limit Warning', {
      type: 'rate_limit_warning',
      ip: req.ip,
      remaining: parseInt(remaining),
      limit: parseInt(limit),
      url: req.originalUrl || req.url,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    });
  }
  
  next();
};

// Generate unique request ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Export all middleware
module.exports = {
  morgan: createMorganMiddleware(),
  requestId: requestIdMiddleware,
  requestTiming: requestTimingMiddleware,
  enhancedLogging: enhancedRequestLogging,
  errorLogging: errorLoggingMiddleware,
  securityLogging: securityLoggingMiddleware,
  rateLimitLogging: rateLimitLoggingMiddleware,
};
