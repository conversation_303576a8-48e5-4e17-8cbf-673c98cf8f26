const express = require('express');
const { query, param } = require('express-validator');
const { protect, authorize } = require('../middleware/auth');
const analyticsService = require('../services/analyticsService');

const router = express.Router();

// @desc    Get user dashboard analytics
// @route   GET /api/analytics/dashboard
// @access  Private
router.get('/dashboard', protect, [
  query('timeframe')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Invalid timeframe')
], async (req, res, next) => {
  try {
    const { timeframe = '30d' } = req.query;

    const analytics = await analyticsService.getDashboardAnalytics(
      req.user._id,
      timeframe
    );

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get detailed post analytics
// @route   GET /api/analytics/post/:id
// @access  Private
router.get('/post/:id', protect, [
  param('id').isMongoId().withMessage('Invalid post ID')
], async (req, res, next) => {
  try {
    const analytics = await analyticsService.getPostAnalytics(
      req.params.id,
      req.user._id
    );

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    if (error.message === 'Post not found') {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    if (error.message === 'Access denied') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    next(error);
  }
});

// @desc    Get site-wide analytics (Admin only)
// @route   GET /api/analytics/site
// @access  Private/Admin
router.get('/site', protect, authorize('admin'), [
  query('timeframe')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Invalid timeframe')
], async (req, res, next) => {
  try {
    const { timeframe = '30d' } = req.query;

    const analytics = await analyticsService.getSiteAnalytics(timeframe);

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Track page view
// @route   POST /api/analytics/track/view
// @access  Public
router.post('/track/view', [
  param('postId').isMongoId().withMessage('Invalid post ID')
], async (req, res, next) => {
  try {
    const { postId } = req.body;
    const userAgent = req.get('User-Agent');
    const ip = req.ip || req.connection.remoteAddress;

    const result = await analyticsService.trackPageView(postId, userAgent, ip);

    res.json({
      success: result.success,
      message: result.message || 'View tracked successfully',
      data: result.views ? { views: result.views } : undefined
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get analytics summary for multiple posts
// @route   POST /api/analytics/posts/summary
// @access  Private
router.post('/posts/summary', protect, async (req, res, next) => {
  try {
    const { postIds } = req.body;

    if (!Array.isArray(postIds) || postIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Post IDs array is required'
      });
    }

    // Get analytics for multiple posts
    const summaries = await Promise.all(
      postIds.map(async (postId) => {
        try {
          return await analyticsService.getPostAnalytics(postId, req.user._id);
        } catch (error) {
          return {
            postId,
            error: error.message
          };
        }
      })
    );

    res.json({
      success: true,
      data: summaries
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get trending content
// @route   GET /api/analytics/trending
// @access  Public
router.get('/trending', [
  query('period')
    .optional()
    .isIn(['day', 'week', 'month'])
    .withMessage('Invalid period'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], async (req, res, next) => {
  try {
    const { period = 'week', limit = 10 } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(endDate.getDate() - 30);
        break;
    }

    // This would typically involve more complex trending algorithms
    // For now, we'll use a simple approach based on recent views
    const Post = require('../models/Post');

    const trendingPosts = await Post.find({
      status: 'published',
      publishedAt: { $gte: startDate, $lte: endDate }
    })
    .populate('author', 'name avatar')
    .sort({ views: -1 })
    .limit(parseInt(limit))
    .select('title slug excerpt views likes shares publishedAt categories tags');

    res.json({
      success: true,
      data: {
        posts: trendingPosts,
        period,
        dateRange: {
          start: startDate,
          end: endDate
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
