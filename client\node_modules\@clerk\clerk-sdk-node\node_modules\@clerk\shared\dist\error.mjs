import {
  Clerk<PERSON><PERSON><PERSON>po<PERSON><PERSON><PERSON>r,
  <PERSON><PERSON><PERSON><PERSON>Error,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
} from "./chunk-JXRB7SGQ.mjs";
import "./chunk-7ELT755Q.mjs";
export {
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
};
//# sourceMappingURL=error.mjs.map