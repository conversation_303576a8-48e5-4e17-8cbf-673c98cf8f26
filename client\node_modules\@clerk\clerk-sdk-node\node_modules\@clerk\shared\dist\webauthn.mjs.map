{"version": 3, "sources": ["../src/webauthn.ts"], "sourcesContent": ["import { isValidBrowser } from './browser';\n\nfunction isWebAuthnSupported() {\n  return (\n    isValidBrowser() &&\n    // Check if `PublicKeyCredential` is a constructor\n    typeof window.PublicKeyCredential === 'function'\n  );\n}\n\nasync function isWebAuthnAutofillSupported(): Promise<boolean> {\n  try {\n    return isWebAuthnSupported() && (await window.PublicKeyCredential.isConditionalMediationAvailable());\n  } catch {\n    return false;\n  }\n}\n\nasync function isWebAuthnPlatformAuthenticatorSupported(): Promise<boolean> {\n  try {\n    return (\n      typeof window !== 'undefined' &&\n      (await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable())\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport { isWebAuthnPlatformAuthenticatorSupported, isWebAuthnAutofillSupported, isWebAuthnSupported };\n"], "mappings": ";;;;;;AAEA,SAAS,sBAAsB;AAC7B,SACE,eAAe;AAAA,EAEf,OAAO,OAAO,wBAAwB;AAE1C;AAEA,eAAe,8BAAgD;AAC7D,MAAI;AACF,WAAO,oBAAoB,KAAM,MAAM,OAAO,oBAAoB,gCAAgC;AAAA,EACpG,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,eAAe,2CAA6D;AAC1E,MAAI;AACF,WACE,OAAO,WAAW,eACjB,MAAM,OAAO,oBAAoB,8CAA8C;AAAA,EAEpF,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": []}