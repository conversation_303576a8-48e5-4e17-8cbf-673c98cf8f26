const fs = require('fs').promises;
const path = require('path');
const Post = require('../models/Post');
const User = require('../models/User');
const seoService = require('./seoService');

class StaticSiteService {
  constructor() {
    this.outputDir = path.join(__dirname, '../../static-site');
    this.templateDir = path.join(__dirname, '../templates');
  }

  // Generate complete static site
  async generateStaticSite() {
    try {
      console.log('🚀 Starting static site generation...');
      
      // Create output directory
      await this.ensureDirectory(this.outputDir);
      
      // Generate all pages
      await this.generateHomePage();
      await this.generateBlogPages();
      await this.generatePostPages();
      await this.generateAuthorPages();
      await this.generateSitemaps();
      await this.generateFeeds();
      await this.copyAssets();
      
      console.log('✅ Static site generation completed!');
      return {
        success: true,
        outputDir: this.outputDir,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Static site generation failed:', error);
      throw error;
    }
  }

  // Generate homepage
  async generateHomePage() {
    console.log('📄 Generating homepage...');
    
    const recentPosts = await Post.find({ status: 'published' })
      .populate('author', 'name avatar bio')
      .sort({ publishedAt: -1 })
      .limit(6);

    const featuredPosts = await Post.find({ 
      status: 'published',
      featured: true 
    })
      .populate('author', 'name avatar bio')
      .sort({ publishedAt: -1 })
      .limit(3);

    const html = await this.renderTemplate('home', {
      title: 'Rivsy - Modern Blogging Platform',
      description: 'Create beautiful, SEO-optimized blogs with AI-powered content assistance',
      recentPosts,
      featuredPosts,
      canonicalUrl: process.env.CLIENT_URL || 'http://localhost:3000'
    });

    await this.writeFile('index.html', html);
  }

  // Generate blog listing pages
  async generateBlogPages() {
    console.log('📄 Generating blog pages...');
    
    const postsPerPage = 12;
    const totalPosts = await Post.countDocuments({ status: 'published' });
    const totalPages = Math.ceil(totalPosts / postsPerPage);

    for (let page = 1; page <= totalPages; page++) {
      const posts = await Post.find({ status: 'published' })
        .populate('author', 'name avatar bio')
        .sort({ publishedAt: -1 })
        .skip((page - 1) * postsPerPage)
        .limit(postsPerPage);

      const html = await this.renderTemplate('blog', {
        title: page === 1 ? 'Blog - Rivsy' : `Blog - Page ${page} - Rivsy`,
        description: 'Explore our collection of blog posts and tutorials',
        posts,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
        canonicalUrl: `${process.env.CLIENT_URL}/blog${page > 1 ? `?page=${page}` : ''}`
      });

      const fileName = page === 1 ? 'blog/index.html' : `blog/page/${page}/index.html`;
      await this.writeFile(fileName, html);
    }

    // Generate category pages
    await this.generateCategoryPages();
    
    // Generate tag pages
    await this.generateTagPages();
  }

  // Generate individual post pages
  async generatePostPages() {
    console.log('📄 Generating post pages...');
    
    const posts = await Post.find({ status: 'published' })
      .populate('author', 'name avatar bio socialLinks');

    for (const post of posts) {
      // Generate related posts
      const relatedPosts = await Post.find({
        status: 'published',
        _id: { $ne: post._id },
        $or: [
          { categories: { $in: post.categories } },
          { tags: { $in: post.tags } }
        ]
      })
        .populate('author', 'name avatar')
        .limit(3);

      // Generate SEO data
      const metaTags = seoService.generateMetaTags(post, post.author);
      const schema = seoService.generatePostSchema(post, post.author);

      const html = await this.renderTemplate('post', {
        post,
        relatedPosts,
        metaTags,
        schema,
        canonicalUrl: `${process.env.CLIENT_URL}/blog/${post.slug}`
      });

      await this.writeFile(`blog/${post.slug}/index.html`, html);
    }
  }

  // Generate author pages
  async generateAuthorPages() {
    console.log('📄 Generating author pages...');
    
    const authors = await User.find({ totalPosts: { $gt: 0 } });

    for (const author of authors) {
      const posts = await Post.find({ 
        author: author._id, 
        status: 'published' 
      }).sort({ publishedAt: -1 });

      const html = await this.renderTemplate('author', {
        title: `${author.name} - Author at Rivsy`,
        description: author.bio || `Posts by ${author.name}`,
        author,
        posts,
        canonicalUrl: `${process.env.CLIENT_URL}/author/${author.blogDomain}`
      });

      await this.writeFile(`author/${author.blogDomain}/index.html`, html);
    }
  }

  // Generate category pages
  async generateCategoryPages() {
    console.log('📄 Generating category pages...');
    
    const categories = await Post.distinct('categories', { status: 'published' });

    for (const category of categories) {
      const posts = await Post.find({ 
        status: 'published',
        categories: category 
      })
        .populate('author', 'name avatar')
        .sort({ publishedAt: -1 });

      const html = await this.renderTemplate('category', {
        title: `${category} - Rivsy Blog`,
        description: `Posts about ${category}`,
        category,
        posts,
        canonicalUrl: `${process.env.CLIENT_URL}/category/${category.toLowerCase().replace(/\s+/g, '-')}`
      });

      const slug = category.toLowerCase().replace(/\s+/g, '-');
      await this.writeFile(`category/${slug}/index.html`, html);
    }
  }

  // Generate tag pages
  async generateTagPages() {
    console.log('📄 Generating tag pages...');
    
    const tags = await Post.distinct('tags', { status: 'published' });

    for (const tag of tags) {
      const posts = await Post.find({ 
        status: 'published',
        tags: tag 
      })
        .populate('author', 'name avatar')
        .sort({ publishedAt: -1 });

      const html = await this.renderTemplate('tag', {
        title: `#${tag} - Rivsy Blog`,
        description: `Posts tagged with ${tag}`,
        tag,
        posts,
        canonicalUrl: `${process.env.CLIENT_URL}/tag/${tag.toLowerCase().replace(/\s+/g, '-')}`
      });

      const slug = tag.toLowerCase().replace(/\s+/g, '-');
      await this.writeFile(`tag/${slug}/index.html`, html);
    }
  }

  // Generate sitemaps and feeds
  async generateSitemaps() {
    console.log('📄 Generating sitemaps and feeds...');
    
    // XML Sitemap
    const sitemap = await seoService.generateSitemap();
    await this.writeFile('sitemap.xml', sitemap, false);

    // RSS Feed
    const rss = await seoService.generateRSSFeed();
    await this.writeFile('rss.xml', rss, false);

    // Robots.txt
    const robots = seoService.generateRobotsTxt();
    await this.writeFile('robots.txt', robots, false);

    // LLMs.txt
    const llms = seoService.generateLLMsTxt();
    await this.writeFile('llms.txt', llms, false);
  }

  // Generate feeds
  async generateFeeds() {
    console.log('📄 Generating additional feeds...');
    
    // JSON Feed
    const posts = await Post.find({ status: 'published' })
      .populate('author', 'name avatar')
      .sort({ publishedAt: -1 })
      .limit(20);

    const jsonFeed = {
      version: 'https://jsonfeed.org/version/1.1',
      title: 'Rivsy Blog',
      home_page_url: process.env.CLIENT_URL,
      feed_url: `${process.env.CLIENT_URL}/feed.json`,
      description: 'Latest posts from Rivsy - Modern Blogging Platform',
      icon: `${process.env.CLIENT_URL}/icon.png`,
      favicon: `${process.env.CLIENT_URL}/favicon.ico`,
      language: 'en',
      items: posts.map(post => ({
        id: post._id.toString(),
        url: `${process.env.CLIENT_URL}/blog/${post.slug}`,
        title: post.title,
        content_html: post.content,
        summary: post.excerpt,
        image: post.featuredImage?.url,
        date_published: post.publishedAt,
        date_modified: post.updatedAt,
        author: {
          name: post.author.name,
          avatar: post.author.avatar
        },
        tags: post.tags
      }))
    };

    await this.writeFile('feed.json', JSON.stringify(jsonFeed, null, 2), false);
  }

  // Copy static assets
  async copyAssets() {
    console.log('📄 Copying static assets...');
    
    const clientDistPath = path.join(__dirname, '../../client/dist');
    
    try {
      // Copy CSS, JS, and other assets
      const assetsDir = path.join(clientDistPath, 'assets');
      const outputAssetsDir = path.join(this.outputDir, 'assets');
      
      await this.copyDirectory(assetsDir, outputAssetsDir);
      
      // Copy public files
      const publicDir = path.join(__dirname, '../../client/public');
      await this.copyDirectory(publicDir, this.outputDir, ['index.html']);
      
    } catch (error) {
      console.warn('⚠️ Could not copy some assets:', error.message);
    }
  }

  // Render template with data
  async renderTemplate(templateName, data) {
    const templatePath = path.join(this.templateDir, `${templateName}.html`);
    
    try {
      let template = await fs.readFile(templatePath, 'utf8');
      
      // Simple template rendering (replace {{variable}} with data)
      template = template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || '';
      });
      
      // Handle more complex replacements
      if (data.metaTags) {
        const metaTagsHtml = Object.entries(data.metaTags)
          .map(([key, value]) => {
            if (key.startsWith('og:') || key.startsWith('twitter:') || key.startsWith('article:')) {
              return `<meta property="${key}" content="${value}" />`;
            } else if (key === 'canonical') {
              return `<link rel="canonical" href="${value}" />`;
            } else {
              return `<meta name="${key}" content="${value}" />`;
            }
          })
          .join('\n    ');
        
        template = template.replace('{{metaTags}}', metaTagsHtml);
      }
      
      if (data.schema) {
        const schemaHtml = `<script type="application/ld+json">${JSON.stringify(data.schema, null, 2)}</script>`;
        template = template.replace('{{schema}}', schemaHtml);
      }
      
      return template;
    } catch (error) {
      console.warn(`⚠️ Template ${templateName} not found, using basic HTML`);
      return this.generateBasicHTML(data);
    }
  }

  // Generate basic HTML when template is not available
  generateBasicHTML(data) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title || 'Rivsy Blog'}</title>
    <meta name="description" content="${data.description || 'Modern blogging platform'}">
    ${data.canonicalUrl ? `<link rel="canonical" href="${data.canonicalUrl}">` : ''}
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body>
    <header>
        <nav>
            <a href="/">Rivsy</a>
            <a href="/blog">Blog</a>
        </nav>
    </header>
    <main>
        <h1>${data.title || 'Rivsy Blog'}</h1>
        ${data.post ? `
            <article>
                <h1>${data.post.title}</h1>
                <div>${data.post.content}</div>
            </article>
        ` : ''}
        ${data.posts ? `
            <div class="posts">
                ${data.posts.map(post => `
                    <article>
                        <h2><a href="/blog/${post.slug}">${post.title}</a></h2>
                        <p>${post.excerpt}</p>
                    </article>
                `).join('')}
            </div>
        ` : ''}
    </main>
    <footer>
        <p>&copy; ${new Date().getFullYear()} Rivsy. All rights reserved.</p>
    </footer>
</body>
</html>`;
  }

  // Utility methods
  async ensureDirectory(dir) {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') throw error;
    }
  }

  async writeFile(filePath, content, createDir = true) {
    const fullPath = path.join(this.outputDir, filePath);
    
    if (createDir) {
      await this.ensureDirectory(path.dirname(fullPath));
    }
    
    await fs.writeFile(fullPath, content, 'utf8');
  }

  async copyDirectory(src, dest, exclude = []) {
    try {
      await this.ensureDirectory(dest);
      const entries = await fs.readdir(src, { withFileTypes: true });
      
      for (const entry of entries) {
        if (exclude.includes(entry.name)) continue;
        
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
          await this.copyDirectory(srcPath, destPath, exclude);
        } else {
          await fs.copyFile(srcPath, destPath);
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not copy directory ${src}:`, error.message);
    }
  }

  // Get generation status
  async getGenerationStatus() {
    try {
      const statsPath = path.join(this.outputDir, 'generation-stats.json');
      const stats = await fs.readFile(statsPath, 'utf8');
      return JSON.parse(stats);
    } catch (error) {
      return null;
    }
  }

  // Save generation stats
  async saveGenerationStats(stats) {
    const statsPath = path.join(this.outputDir, 'generation-stats.json');
    await fs.writeFile(statsPath, JSON.stringify(stats, null, 2));
  }
}

module.exports = new StaticSiteService();
