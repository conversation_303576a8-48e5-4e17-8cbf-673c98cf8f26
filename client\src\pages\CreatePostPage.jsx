import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';
import {
  Save,
  Eye,
  Sparkles,
  Settings,
  Image as ImageIcon,
  Tag,
  Globe
} from 'lucide-react';
import axios from 'axios';
import toast from 'react-hot-toast';

import RichTextEditor from '../components/RichTextEditor';
import AIAssistant from '../components/AIAssistant';

const CreatePostPage = () => {
  const navigate = useNavigate();
  const [isAIOpen, setIsAIOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const [postData, setPostData] = useState({
    title: '',
    content: '',
    excerpt: '',
    categories: [],
    tags: [],
    status: 'draft',
    featuredImage: {
      url: '',
      alt: '',
      caption: ''
    },
    metaTitle: '',
    metaDescription: '',
    keywords: []
  });

  const [newCategory, setNewCategory] = useState('');
  const [newTag, setNewTag] = useState('');

  const handleInputChange = (field, value) => {
    setPostData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFeaturedImageChange = (field, value) => {
    setPostData(prev => ({
      ...prev,
      featuredImage: {
        ...prev.featuredImage,
        [field]: value
      }
    }));
  };

  const addCategory = () => {
    if (newCategory.trim() && !postData.categories.includes(newCategory.trim())) {
      setPostData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()]
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (category) => {
    setPostData(prev => ({
      ...prev,
      categories: prev.categories.filter(c => c !== category)
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !postData.tags.includes(newTag.trim())) {
      setPostData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag) => {
    setPostData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const handleSave = async (status = 'draft') => {
    if (!postData.title.trim()) {
      toast.error('Please enter a title');
      return;
    }

    if (!postData.content.trim()) {
      toast.error('Please enter some content');
      return;
    }

    setIsSaving(true);
    try {
      const response = await axios.post('/api/posts', {
        ...postData,
        status
      });

      toast.success(`Post ${status === 'published' ? 'published' : 'saved'} successfully!`);
      navigate('/dashboard');
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to save post';
      toast.error(message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAIContentUpdate = (content) => {
    setPostData(prev => ({
      ...prev,
      content
    }));
  };

  return (
    <>
      <Helmet>
        <title>Create Post - Rivsy</title>
        <meta name="description" content="Create a new blog post with AI assistance." />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Create New Post
              </h1>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsAIOpen(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 rounded-lg hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors"
                >
                  <Sparkles className="w-4 h-4" />
                  <span>AI Assistant</span>
                </button>

                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <Settings className="w-5 h-5" />
                </button>

                <button
                  onClick={() => handleSave('draft')}
                  disabled={isSaving}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Save Draft</span>
                </button>

                <button
                  onClick={() => handleSave('published')}
                  disabled={isSaving}
                  className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  <Globe className="w-4 h-4" />
                  <span>Publish</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3 space-y-6">
              {/* Title */}
              <div>
                <input
                  type="text"
                  value={postData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter your post title..."
                  className="w-full text-3xl font-bold border-none outline-none bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>

              {/* Content Editor */}
              <RichTextEditor
                value={postData.content}
                onChange={(content) => handleInputChange('content', content)}
                showAIAssist={true}
                onAIAssist={() => setIsAIOpen(true)}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Post Settings */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Post Settings
                </h3>

                {/* Excerpt */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Excerpt
                  </label>
                  <textarea
                    value={postData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    placeholder="Brief description of your post..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Categories */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Categories
                  </label>
                  <div className="flex space-x-2 mb-2">
                    <input
                      type="text"
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addCategory()}
                      placeholder="Add category..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                    <button
                      onClick={addCategory}
                      className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm"
                    >
                      Add
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {postData.categories.map((category) => (
                      <span
                        key={category}
                        className="inline-flex items-center px-2 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 text-xs rounded-md"
                      >
                        {category}
                        <button
                          onClick={() => removeCategory(category)}
                          className="ml-1 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-200"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                {/* Tags */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags
                  </label>
                  <div className="flex space-x-2 mb-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      placeholder="Add tag..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                    <button
                      onClick={addTag}
                      className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm"
                    >
                      Add
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {postData.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs rounded-md"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Featured Image */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <ImageIcon className="w-5 h-5 mr-2" />
                  Featured Image
                </h3>

                <div className="space-y-3">
                  <input
                    type="url"
                    value={postData.featuredImage.url}
                    onChange={(e) => handleFeaturedImageChange('url', e.target.value)}
                    placeholder="Image URL..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                  />

                  <input
                    type="text"
                    value={postData.featuredImage.alt}
                    onChange={(e) => handleFeaturedImageChange('alt', e.target.value)}
                    placeholder="Alt text..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                  />

                  <input
                    type="text"
                    value={postData.featuredImage.caption}
                    onChange={(e) => handleFeaturedImageChange('caption', e.target.value)}
                    placeholder="Caption..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                  />

                  {postData.featuredImage.url && (
                    <div className="mt-3">
                      <img
                        src={postData.featuredImage.url}
                        alt={postData.featuredImage.alt}
                        className="w-full h-32 object-cover rounded-md"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* AI Assistant Modal */}
        <AIAssistant
          isOpen={isAIOpen}
          onClose={() => setIsAIOpen(false)}
          currentContent={postData.content}
          onContentUpdate={handleAIContentUpdate}
        />
      </div>
    </>
  );
};

export default CreatePostPage;
