export { h as handleValueOrFn } from '../handleValueOrFn-D2uLOn6s.mjs';

type Callback = (val?: any) => void;
/**
 * Create a promise that can be resolved or rejected from
 * outside the Promise constructor callback
 */
declare const createDeferredPromise: () => {
    promise: Promise<unknown>;
    resolve: Callback;
    reject: Callback;
};

/**
 * Check if the frontendApi ends with a staging domain
 */
declare function isStaging(frontendApi: string): boolean;

declare const logErrorInDevMode: (message: string) => void;

declare const noop: (..._args: any[]) => void;

type Milliseconds = number;
type BackoffOptions = Partial<{
    firstDelay: Milliseconds;
    maxDelay: Milliseconds;
    timeMultiple: number;
    shouldRetry: (error: unknown, iterationsCount: number) => boolean;
}>;
declare const runWithExponentialBackOff: <T>(callback: () => T | Promise<T>, options?: BackoffOptions) => Promise<T>;

declare const isDevelopmentEnvironment: () => boolean;
declare const isTestEnvironment: () => boolean;
declare const isProductionEnvironment: () => boolean;

/**
 * Merges 2 objects without creating new object references
 * The merged props will appear on the `target` object
 * If `target` already has a value for a given key it will not be overwritten
 */
declare const fastDeepMergeAndReplace: (source: Record<any, any> | undefined | null, target: Record<any, any> | undefined | null) => void;
declare const fastDeepMergeAndKeep: (source: Record<any, any> | undefined | null, target: Record<any, any> | undefined | null) => void;

export { createDeferredPromise, fastDeepMergeAndKeep, fastDeepMergeAndReplace, isDevelopmentEnvironment, isProductionEnvironment, isStaging, isTestEnvironment, logErrorInDevMode, noop, runWithExponentialBackOff };
