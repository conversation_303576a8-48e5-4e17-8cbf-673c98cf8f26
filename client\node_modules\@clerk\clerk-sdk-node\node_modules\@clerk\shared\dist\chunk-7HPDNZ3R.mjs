// src/utils/runtimeEnvironment.ts
var isDevelopmentEnvironment = () => {
  try {
    return process.env.NODE_ENV === "development";
  } catch {
  }
  return false;
};
var isTestEnvironment = () => {
  try {
    return process.env.NODE_ENV === "test";
  } catch {
  }
  return false;
};
var isProductionEnvironment = () => {
  try {
    return process.env.NODE_ENV === "production";
  } catch {
  }
  return false;
};

export {
  isDevelopmentEnvironment,
  isTestEnvironment,
  isProductionEnvironment
};
//# sourceMappingURL=chunk-7HPDNZ3R.mjs.map