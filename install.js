#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => {
  return new Promise(resolve => rl.question(query, resolve));
};

class RivsyInstaller {
  constructor() {
    this.config = {};
  }

  async run() {
    try {
      log('\n🚀 Welcome to Rivsy Blog Platform Installer!', 'cyan');
      log('=====================================\n', 'cyan');

      await this.checkPrerequisites();
      await this.gatherConfiguration();
      await this.installDependencies();
      await this.setupEnvironment();
      await this.setupDatabase();
      await this.finalizeInstallation();

      log('\n🎉 Installation completed successfully!', 'green');
      log('=====================================', 'green');
      log('\n📋 Next Steps:', 'bright');
      log('1. Start MongoDB: mongod', 'yellow');
      log('2. Start the development server: npm run dev', 'yellow');
      log('3. Open http://localhost:3000 in your browser', 'yellow');
      log('4. Create your first blog post!', 'yellow');
      log('\n📚 Documentation: README.md', 'blue');
      log('🆘 Support: https://github.com/rivsy/rivsy/issues', 'blue');

    } catch (error) {
      log(`\n❌ Installation failed: ${error.message}`, 'red');
      process.exit(1);
    } finally {
      rl.close();
    }
  }

  async checkPrerequisites() {
    log('🔍 Checking prerequisites...', 'blue');

    // Check Node.js version
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion < 18) {
        throw new Error(`Node.js 18+ required. Current version: ${nodeVersion}`);
      }
      
      log(`✅ Node.js ${nodeVersion}`, 'green');
    } catch (error) {
      throw new Error('Node.js not found. Please install Node.js 18+ from https://nodejs.org');
    }

    // Check npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      log(`✅ npm ${npmVersion}`, 'green');
    } catch (error) {
      throw new Error('npm not found. Please install npm.');
    }

    // Check MongoDB
    try {
      execSync('mongod --version', { encoding: 'utf8', stdio: 'ignore' });
      log('✅ MongoDB found', 'green');
    } catch (error) {
      log('⚠️  MongoDB not found. Please install MongoDB from https://www.mongodb.com/try/download/community', 'yellow');
      const proceed = await question('Continue without MongoDB? (you can install it later) [y/N]: ');
      if (proceed.toLowerCase() !== 'y') {
        throw new Error('MongoDB installation cancelled');
      }
    }

    // Check Git
    try {
      execSync('git --version', { encoding: 'utf8', stdio: 'ignore' });
      log('✅ Git found', 'green');
    } catch (error) {
      log('⚠️  Git not found (optional)', 'yellow');
    }

    log('✅ Prerequisites check completed\n', 'green');
  }

  async gatherConfiguration() {
    log('⚙️  Configuration Setup', 'blue');
    log('Please provide the following information:\n', 'blue');

    // Database configuration
    this.config.mongoUri = await question('MongoDB URI [mongodb://localhost:27017/rivsy-blog]: ') 
      || 'mongodb://localhost:27017/rivsy-blog';

    // JWT secrets
    this.config.jwtSecret = await question('JWT Secret (leave empty to generate): ') 
      || this.generateSecret();
    
    this.config.refreshTokenSecret = await question('Refresh Token Secret (leave empty to generate): ') 
      || this.generateSecret();

    // Server configuration
    this.config.port = await question('Server Port [5000]: ') || '5000';
    this.config.clientUrl = await question('Client URL [http://localhost:3000]: ') 
      || 'http://localhost:3000';

    // AI Configuration (optional)
    log('\n🤖 AI Configuration (Optional - can be configured later):', 'magenta');
    const configureAI = await question('Configure AI integration now? [y/N]: ');
    
    if (configureAI.toLowerCase() === 'y') {
      const aiProvider = await question('AI Provider (openai/azure/aws) [openai]: ') || 'openai';
      
      if (aiProvider === 'openai') {
        this.config.openaiApiKey = await question('OpenAI API Key: ');
      } else if (aiProvider === 'azure') {
        this.config.azureApiKey = await question('Azure OpenAI API Key: ');
        this.config.azureApiBase = await question('Azure OpenAI Endpoint: ');
        this.config.azureDeploymentName = await question('Azure Deployment Name [gpt-4]: ') || 'gpt-4';
      }
    }

    // Email configuration (optional)
    log('\n📧 Email Configuration (Optional):', 'magenta');
    const configureEmail = await question('Configure email settings now? [y/N]: ');
    
    if (configureEmail.toLowerCase() === 'y') {
      this.config.smtpHost = await question('SMTP Host: ');
      this.config.smtpPort = await question('SMTP Port [587]: ') || '587';
      this.config.smtpUser = await question('SMTP Username: ');
      this.config.smtpPass = await question('SMTP Password: ');
    }

    log('\n✅ Configuration completed\n', 'green');
  }

  async installDependencies() {
    log('📦 Installing dependencies...', 'blue');

    try {
      // Install server dependencies
      log('Installing server dependencies...', 'yellow');
      execSync('npm install', { 
        cwd: path.join(__dirname, 'server'),
        stdio: 'inherit'
      });

      // Install client dependencies
      log('Installing client dependencies...', 'yellow');
      execSync('npm install', { 
        cwd: path.join(__dirname, 'client'),
        stdio: 'inherit'
      });

      log('✅ Dependencies installed successfully\n', 'green');
    } catch (error) {
      throw new Error('Failed to install dependencies. Please check your internet connection and try again.');
    }
  }

  async setupEnvironment() {
    log('🔧 Setting up environment...', 'blue');

    const envContent = this.generateEnvContent();
    
    try {
      fs.writeFileSync('.env', envContent);
      log('✅ Environment file created (.env)', 'green');
      
      // Create .env.example for reference
      const exampleContent = this.generateEnvExample();
      fs.writeFileSync('.env.example', exampleContent);
      log('✅ Example environment file created (.env.example)', 'green');
      
    } catch (error) {
      throw new Error('Failed to create environment files');
    }

    log('✅ Environment setup completed\n', 'green');
  }

  async setupDatabase() {
    log('🗄️  Setting up database...', 'blue');

    const seedData = await question('Seed database with sample data? [Y/n]: ');
    
    if (seedData.toLowerCase() !== 'n') {
      try {
        log('Seeding database with sample data...', 'yellow');
        execSync('npm run seed', { 
          cwd: path.join(__dirname, 'server'),
          stdio: 'inherit'
        });
        log('✅ Database seeded successfully', 'green');
      } catch (error) {
        log('⚠️  Database seeding failed. You can run "npm run seed" later.', 'yellow');
      }
    }

    log('✅ Database setup completed\n', 'green');
  }

  async finalizeInstallation() {
    log('🎯 Finalizing installation...', 'blue');

    // Create necessary directories
    const dirs = [
      'uploads',
      'logs',
      'client/public/uploads'
    ];

    dirs.forEach(dir => {
      const dirPath = path.join(__dirname, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        log(`✅ Created directory: ${dir}`, 'green');
      }
    });

    // Create gitignore if it doesn't exist
    if (!fs.existsSync('.gitignore')) {
      const gitignoreContent = this.generateGitignore();
      fs.writeFileSync('.gitignore', gitignoreContent);
      log('✅ Created .gitignore file', 'green');
    }

    log('✅ Installation finalized\n', 'green');
  }

  generateSecret() {
    return require('crypto').randomBytes(64).toString('hex');
  }

  generateEnvContent() {
    return `# =============================================================================
# RIVSY BLOG PLATFORM - ENVIRONMENT CONFIGURATION
# Generated on ${new Date().toISOString()}
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=${this.config.mongoUri}

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=${this.config.port}
CLIENT_URL=${this.config.clientUrl}
SERVER_URL=http://localhost:${this.config.port}

# =============================================================================
# JWT & AUTHENTICATION
# =============================================================================
JWT_SECRET=${this.config.jwtSecret}
JWT_EXPIRE=7d
REFRESH_TOKEN_SECRET=${this.config.refreshTokenSecret}
REFRESH_TOKEN_EXPIRE=30d

# =============================================================================
# AI INTEGRATION (Optional)
# =============================================================================
${this.config.openaiApiKey ? `OPENAI_API_KEY=${this.config.openaiApiKey}` : '# OPENAI_API_KEY=your-openai-api-key'}
${this.config.azureApiKey ? `AZURE_API_KEY=${this.config.azureApiKey}` : '# AZURE_API_KEY=your-azure-api-key'}
${this.config.azureApiBase ? `AZURE_API_BASE=${this.config.azureApiBase}` : '# AZURE_API_BASE=https://your-resource.openai.azure.com/'}
${this.config.azureDeploymentName ? `AZURE_DEPLOYMENT_NAME=${this.config.azureDeploymentName}` : '# AZURE_DEPLOYMENT_NAME=gpt-4'}

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
${this.config.smtpHost ? `SMTP_HOST=${this.config.smtpHost}` : '# SMTP_HOST=smtp.gmail.com'}
${this.config.smtpPort ? `SMTP_PORT=${this.config.smtpPort}` : '# SMTP_PORT=587'}
${this.config.smtpUser ? `SMTP_USER=${this.config.smtpUser}` : '# SMTP_USER=<EMAIL>'}
${this.config.smtpPass ? `SMTP_PASS=${this.config.smtpPass}` : '# SMTP_PASS=your-app-password'}

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
DEBUG=rivsy:*
LOG_LEVEL=info
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_SALT_ROUNDS=12
`;
  }

  generateEnvExample() {
    return `# Copy this file to .env and fill in your values

# Database
MONGODB_URI=mongodb://localhost:27017/rivsy-blog

# Server
NODE_ENV=development
PORT=5000
CLIENT_URL=http://localhost:3000

# JWT Secrets (generate secure random strings)
JWT_SECRET=your-super-secret-jwt-key
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# AI Integration (Optional)
OPENAI_API_KEY=your-openai-api-key
# OR
AZURE_API_KEY=your-azure-api-key
AZURE_API_BASE=https://your-resource.openai.azure.com/
AZURE_DEPLOYMENT_NAME=gpt-4

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
`;
  }

  generateGitignore() {
    return `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
/client/dist/
/client/build/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Uploads
uploads/
/client/public/uploads/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
`;
  }
}

// Run installer if called directly
if (require.main === module) {
  const installer = new RivsyInstaller();
  installer.run();
}

module.exports = RivsyInstaller;
