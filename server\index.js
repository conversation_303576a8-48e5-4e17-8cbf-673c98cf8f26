const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
require('dotenv').config();

// Import logger and logging middleware
const logger = require('./config/logger');
const loggingMiddleware = require('./middleware/logging');

// Import routes
const authRoutes = require('./routes/auth');
const clerkAuthRoutes = require('./routes/clerkAuth');
const postRoutes = require('./routes/posts');
const userRoutes = require('./routes/users');
const aiRoutes = require('./routes/ai');
const analyticsRoutes = require('./routes/analytics');
const seoRoutes = require('./routes/seo');
const performanceRoutes = require('./routes/performance');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// Import services
const performanceService = require('./services/performanceService');
const cacheService = require('./services/cacheService');

const app = express();

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Request ID and timing middleware (must be first)
app.use(loggingMiddleware.requestId);
app.use(loggingMiddleware.requestTiming);

// Performance monitoring middleware
app.use(performanceService.middleware());

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// CORS configuration
const corsOptions = {
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true,
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Security logging middleware
app.use(loggingMiddleware.securityLogging);

// Rate limit logging middleware
app.use(loggingMiddleware.rateLimitLogging);

// Enhanced logging middleware
app.use(loggingMiddleware.morgan);
app.use(loggingMiddleware.enhancedLogging);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/auth/clerk', clerkAuthRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/users', userRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/seo', seoRoutes);
app.use('/api/performance', performanceRoutes);

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  });
}

// Error handling middleware
app.use(notFound);
app.use(loggingMiddleware.errorLogging);
app.use(errorHandler);

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    logger.info('Database Connected', {
      host: conn.connection.host,
      database: conn.connection.name,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Database Connection Failed', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
    process.exit(1);
  }
};

// Start server
const PORT = process.env.PORT || 5000;

const startServer = async () => {
  try {
    await connectDB();

    app.listen(PORT, async () => {
      logger.info('Server Started Successfully', {
        port: PORT,
        environment: process.env.NODE_ENV,
        clientUrl: process.env.CLIENT_URL,
        healthCheck: `http://localhost:${PORT}/health`,
        timestamp: new Date().toISOString(),
      });

      // Warm cache on startup
      try {
        await cacheService.warmCache();
        logger.info('Cache Warmed Successfully', {
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.warn('Cache Warming Failed', {
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    });
  } catch (error) {
    logger.error('Server Startup Failed', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
    process.exit(1);
  }
};

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    console.log('MongoDB connection closed.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    console.log('MongoDB connection closed.');
    process.exit(0);
  });
});

module.exports = app;
