{"version": 3, "sources": ["../src/errors.ts"], "sourcesContent": ["export type TokenCarrier = 'header' | 'cookie';\n\nexport const TokenVerificationErrorCode = {\n  InvalidSecretKey: 'clerk_key_invalid',\n};\n\nexport type TokenVerificationErrorCode = (typeof TokenVerificationErrorCode)[keyof typeof TokenVerificationErrorCode];\n\nexport const TokenVerificationErrorReason = {\n  TokenExpired: 'token-expired',\n  TokenInvalid: 'token-invalid',\n  TokenInvalidAlgorithm: 'token-invalid-algorithm',\n  TokenInvalidAuthorizedParties: 'token-invalid-authorized-parties',\n  TokenInvalidSignature: 'token-invalid-signature',\n  TokenNotActiveYet: 'token-not-active-yet',\n  TokenIatInTheFuture: 'token-iat-in-the-future',\n  TokenVerificationFailed: 'token-verification-failed',\n  InvalidSecretKey: 'secret-key-invalid',\n  LocalJWKMissing: 'jwk-local-missing',\n  RemoteJWKFailedToLoad: 'jwk-remote-failed-to-load',\n  RemoteJWKInvalid: 'jwk-remote-invalid',\n  RemoteJWKMissing: 'jwk-remote-missing',\n  JWKFailedToResolve: 'jwk-failed-to-resolve',\n  JWKKidMismatch: 'jwk-kid-mismatch',\n};\n\nexport type TokenVerificationErrorReason =\n  (typeof TokenVerificationErrorReason)[keyof typeof TokenVerificationErrorReason];\n\nexport const TokenVerificationErrorAction = {\n  ContactSupport: 'Contact <EMAIL>',\n  EnsureClerkJWT: 'Make sure that this is a valid Clerk generate JWT.',\n  SetClerkJWTKey: 'Set the CLERK_JWT_KEY environment variable.',\n  SetClerkSecretKey: 'Set the CLERK_SECRET_KEY environment variable.',\n  EnsureClockSync: 'Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization).',\n};\n\nexport type TokenVerificationErrorAction =\n  (typeof TokenVerificationErrorAction)[keyof typeof TokenVerificationErrorAction];\n\nexport class TokenVerificationError extends Error {\n  action?: TokenVerificationErrorAction;\n  reason: TokenVerificationErrorReason;\n  tokenCarrier?: TokenCarrier;\n\n  constructor({\n    action,\n    message,\n    reason,\n  }: {\n    action?: TokenVerificationErrorAction;\n    message: string;\n    reason: TokenVerificationErrorReason;\n  }) {\n    super(message);\n\n    Object.setPrototypeOf(this, TokenVerificationError.prototype);\n\n    this.reason = reason;\n    this.message = message;\n    this.action = action;\n  }\n\n  public getFullMessage() {\n    return `${[this.message, this.action].filter(m => m).join(' ')} (reason=${this.reason}, token-carrier=${\n      this.tokenCarrier\n    })`;\n  }\n}\n\nexport class SignJWTError extends Error {}\n\nexport const MachineTokenVerificationErrorCode = {\n  TokenInvalid: 'token-invalid',\n  InvalidSecretKey: 'secret-key-invalid',\n  UnexpectedError: 'unexpected-error',\n} as const;\n\nexport type MachineTokenVerificationErrorCode =\n  (typeof MachineTokenVerificationErrorCode)[keyof typeof MachineTokenVerificationErrorCode];\n\nexport class MachineTokenVerificationError extends Error {\n  code: MachineTokenVerificationErrorCode;\n  long_message?: string;\n  status: number;\n\n  constructor({ message, code, status }: { message: string; code: MachineTokenVerificationErrorCode; status: number }) {\n    super(message);\n    Object.setPrototypeOf(this, MachineTokenVerificationError.prototype);\n\n    this.code = code;\n    this.status = status;\n  }\n\n  public getFullMessage() {\n    return `${this.message} (code=${this.code}, status=${this.status})`;\n  }\n}\n"], "mappings": ";AAEO,IAAM,6BAA6B;AAAA,EACxC,kBAAkB;AACpB;AAIO,IAAM,+BAA+B;AAAA,EAC1C,cAAc;AAAA,EACd,cAAc;AAAA,EACd,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gBAAgB;AAClB;AAKO,IAAM,+BAA+B;AAAA,EAC1C,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,iBAAiB;AACnB;AAKO,IAAM,yBAAN,MAAM,gCAA+B,MAAM;AAAA,EAKhD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAEb,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAE5D,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EAEO,iBAAiB;AACtB,WAAO,GAAG,CAAC,KAAK,SAAS,KAAK,MAAM,EAAE,OAAO,OAAK,CAAC,EAAE,KAAK,GAAG,CAAC,YAAY,KAAK,MAAM,mBACnF,KAAK,YACP;AAAA,EACF;AACF;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAC;AAElC,IAAM,oCAAoC;AAAA,EAC/C,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,iBAAiB;AACnB;AAKO,IAAM,gCAAN,MAAM,uCAAsC,MAAM;AAAA,EAKvD,YAAY,EAAE,SAAS,MAAM,OAAO,GAAiF;AACnH,UAAM,OAAO;AACb,WAAO,eAAe,MAAM,+BAA8B,SAAS;AAEnE,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EAEO,iBAAiB;AACtB,WAAO,GAAG,KAAK,OAAO,UAAU,KAAK,IAAI,YAAY,KAAK,MAAM;AAAA,EAClE;AACF;", "names": []}