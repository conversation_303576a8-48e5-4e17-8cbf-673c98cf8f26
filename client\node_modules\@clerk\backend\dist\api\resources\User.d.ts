import { EmailAddress } from './EmailAddress';
import { ExternalAccount } from './ExternalAccount';
import type { UserJSON } from './JSON';
import { PhoneNumber } from './PhoneNumber';
import { SamlAccount } from './SamlAccount';
import { Web3Wallet } from './Web3Wallet';
export declare class User {
    readonly id: string;
    readonly passwordEnabled: boolean;
    readonly totpEnabled: boolean;
    readonly backupCodeEnabled: boolean;
    readonly twoFactorEnabled: boolean;
    readonly banned: boolean;
    readonly locked: boolean;
    readonly createdAt: number;
    readonly updatedAt: number;
    readonly imageUrl: string;
    readonly hasImage: boolean;
    readonly primaryEmailAddressId: string | null;
    readonly primaryPhoneNumberId: string | null;
    readonly primaryWeb3WalletId: string | null;
    readonly lastSignInAt: number | null;
    readonly externalId: string | null;
    readonly username: string | null;
    readonly firstName: string | null;
    readonly lastName: string | null;
    readonly publicMetadata: UserPublicMetadata;
    readonly privateMetadata: UserPrivateMetadata;
    readonly unsafeMetadata: UserUnsafeMetadata;
    readonly emailAddresses: EmailAddress[];
    readonly phoneNumbers: PhoneNumber[];
    readonly web3Wallets: Web3Wallet[];
    readonly externalAccounts: ExternalAccount[];
    readonly samlAccounts: SamlAccount[];
    readonly lastActiveAt: number | null;
    readonly createOrganizationEnabled: boolean;
    readonly createOrganizationsLimit: number | null;
    readonly deleteSelfEnabled: boolean;
    readonly legalAcceptedAt: number | null;
    private _raw;
    get raw(): UserJSON | null;
    constructor(id: string, passwordEnabled: boolean, totpEnabled: boolean, backupCodeEnabled: boolean, twoFactorEnabled: boolean, banned: boolean, locked: boolean, createdAt: number, updatedAt: number, imageUrl: string, hasImage: boolean, primaryEmailAddressId: string | null, primaryPhoneNumberId: string | null, primaryWeb3WalletId: string | null, lastSignInAt: number | null, externalId: string | null, username: string | null, firstName: string | null, lastName: string | null, publicMetadata: UserPublicMetadata | undefined, privateMetadata: UserPrivateMetadata | undefined, unsafeMetadata: UserUnsafeMetadata | undefined, emailAddresses: EmailAddress[] | undefined, phoneNumbers: PhoneNumber[] | undefined, web3Wallets: Web3Wallet[] | undefined, externalAccounts: ExternalAccount[] | undefined, samlAccounts: SamlAccount[] | undefined, lastActiveAt: number | null, createOrganizationEnabled: boolean, createOrganizationsLimit: number | null | undefined, deleteSelfEnabled: boolean, legalAcceptedAt: number | null);
    static fromJSON(data: UserJSON): User;
    get primaryEmailAddress(): EmailAddress | null;
    get primaryPhoneNumber(): PhoneNumber | null;
    get primaryWeb3Wallet(): Web3Wallet | null;
    get fullName(): string | null;
}
//# sourceMappingURL=User.d.ts.map