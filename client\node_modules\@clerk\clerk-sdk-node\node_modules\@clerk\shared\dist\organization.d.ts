import { OrganizationMembershipResource } from '@clerk/types';

/**
 * Finds the organization membership for a given organization ID from a list of memberships
 * @param organizationMemberships - Array of organization memberships to search through
 * @param organizationId - ID of the organization to find the membership for
 * @returns The matching organization membership or undefined if not found
 */
declare function getCurrentOrganizationMembership(organizationMemberships: OrganizationMembershipResource[], organizationId: string): OrganizationMembershipResource | undefined;

export { getCurrentOrganizationMembership };
