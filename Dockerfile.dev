# Development Dockerfile for Rivsy Blog Platform

FROM node:18-alpine

# Install development tools
RUN apk add --no-cache git curl

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY server/package*.json ./server/
COPY client/package*.json ./client/

# Install dependencies
RUN npm install
RUN cd server && npm install
RUN cd client && npm install

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p uploads logs

# Expose ports
EXPOSE 3000 5000

# Start development server
CMD ["npm", "run", "dev"]
