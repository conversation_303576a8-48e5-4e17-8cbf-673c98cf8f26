import {
  addClerkPrefix
} from "./chunk-IFTVZ2LQ.mjs";
import {
  versionSelector
} from "./chunk-LYW7U4SP.mjs";
import {
  isValidProxyUrl,
  proxyUrlToAbsoluteURL
} from "./chunk-6NDGN2IU.mjs";
import {
  loadScript
} from "./chunk-UHYOOJ74.mjs";
import {
  buildErrorThrower
} from "./chunk-JXRB7SGQ.mjs";
import {
  createDevOrStagingUrlCache,
  parsePublishableKey
} from "./chunk-G3VP5PJE.mjs";

// src/loadClerkJsScript.ts
var FAILED_TO_LOAD_ERROR = "Clerk: Failed to load Clerk";
var { isDevOrStagingUrl } = createDevOrStagingUrlCache();
var errorThrower = buildErrorThrower({ packageName: "@clerk/shared" });
function setClerkJsLoadingErrorPackageName(packageName) {
  errorThrower.setPackageName({ packageName });
}
var loadClerkJsScript = async (opts) => {
  const existingScript = document.querySelector("script[data-clerk-js-script]");
  if (existingScript) {
    return new Promise((resolve, reject) => {
      existingScript.addEventListener("load", () => {
        resolve(existingScript);
      });
      existingScript.addEventListener("error", () => {
        reject(FAILED_TO_LOAD_ERROR);
      });
    });
  }
  if (!opts?.publishableKey) {
    errorThrower.throwMissingPublishableKeyError();
    return;
  }
  return loadScript(clerkJsScriptUrl(opts), {
    async: true,
    crossOrigin: "anonymous",
    nonce: opts.nonce,
    beforeLoad: applyClerkJsScriptAttributes(opts)
  }).catch(() => {
    throw new Error(FAILED_TO_LOAD_ERROR);
  });
};
var clerkJsScriptUrl = (opts) => {
  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;
  if (clerkJSUrl) {
    return clerkJSUrl;
  }
  let scriptHost = "";
  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {
    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\/\//, "");
  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || "")) {
    scriptHost = addClerkPrefix(domain);
  } else {
    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || "";
  }
  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\.+$/, "")}.` : "";
  const version = versionSelector(clerkJSVersion);
  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;
};
var buildClerkJsScriptAttributes = (options) => {
  const obj = {};
  if (options.publishableKey) {
    obj["data-clerk-publishable-key"] = options.publishableKey;
  }
  if (options.proxyUrl) {
    obj["data-clerk-proxy-url"] = options.proxyUrl;
  }
  if (options.domain) {
    obj["data-clerk-domain"] = options.domain;
  }
  if (options.nonce) {
    obj.nonce = options.nonce;
  }
  return obj;
};
var applyClerkJsScriptAttributes = (options) => (script) => {
  const attributes = buildClerkJsScriptAttributes(options);
  for (const attribute in attributes) {
    script.setAttribute(attribute, attributes[attribute]);
  }
};

export {
  setClerkJsLoadingErrorPackageName,
  loadClerkJsScript,
  clerkJsScriptUrl,
  buildClerkJsScriptAttributes
};
//# sourceMappingURL=chunk-3J5I5O32.mjs.map