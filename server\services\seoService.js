const { SitemapStream, streamToPromise } = require('sitemap');
const RSS = require('rss');
const Post = require('../models/Post');
const User = require('../models/User');

class SEOService {
  // Generate sitemap for all published posts
  async generateSitemap(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    try {
      const posts = await Post.find({ status: 'published' })
        .select('slug updatedAt')
        .sort({ updatedAt: -1 });

      const sitemap = new SitemapStream({ hostname: baseUrl });

      // Add static pages
      sitemap.write({ url: '/', changefreq: 'daily', priority: 1.0 });
      sitemap.write({ url: '/blog', changefreq: 'daily', priority: 0.9 });
      sitemap.write({ url: '/about', changefreq: 'monthly', priority: 0.7 });
      sitemap.write({ url: '/contact', changefreq: 'monthly', priority: 0.6 });

      // Add blog posts
      posts.forEach(post => {
        sitemap.write({
          url: `/blog/${post.slug}`,
          lastmod: post.updatedAt,
          changefreq: 'weekly',
          priority: 0.8
        });
      });

      sitemap.end();
      return await streamToPromise(sitemap);
    } catch (error) {
      console.error('Error generating sitemap:', error);
      throw error;
    }
  }

  // Generate RSS feed
  async generateRSSFeed(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    try {
      const posts = await Post.find({ status: 'published' })
        .populate('author', 'name email')
        .sort({ publishedAt: -1 })
        .limit(20);

      const feed = new RSS({
        title: 'Rivsy Blog',
        description: 'Latest posts from Rivsy - Modern Blogging Platform',
        feed_url: `${baseUrl}/rss.xml`,
        site_url: baseUrl,
        image_url: `${baseUrl}/logo.png`,
        managingEditor: '<EMAIL> (Rivsy Team)',
        webMaster: '<EMAIL> (Rivsy Team)',
        copyright: `${new Date().getFullYear()} Rivsy`,
        language: 'en',
        categories: ['Technology', 'Web Development', 'AI', 'Blogging'],
        pubDate: new Date(),
        ttl: '60'
      });

      posts.forEach(post => {
        feed.item({
          title: post.title,
          description: post.excerpt || post.content.substring(0, 200) + '...',
          url: `${baseUrl}/blog/${post.slug}`,
          guid: post._id.toString(),
          categories: post.categories || [],
          author: post.author?.email || '<EMAIL>',
          date: post.publishedAt
        });
      });

      return feed.xml({ indent: true });
    } catch (error) {
      console.error('Error generating RSS feed:', error);
      throw error;
    }
  }

  // Generate JSON-LD structured data for a blog post
  generatePostSchema(post, author, baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    const schema = {
      '@context': 'https://schema.org',
      '@type': 'BlogPosting',
      headline: post.title,
      description: post.metaDescription || post.excerpt,
      image: post.featuredImage?.url ? [post.featuredImage.url] : [],
      datePublished: post.publishedAt,
      dateModified: post.updatedAt,
      author: {
        '@type': 'Person',
        name: author.name,
        url: `${baseUrl}/author/${author.blogDomain}`
      },
      publisher: {
        '@type': 'Organization',
        name: 'Rivsy',
        logo: {
          '@type': 'ImageObject',
          url: `${baseUrl}/logo.png`
        }
      },
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${baseUrl}/blog/${post.slug}`
      },
      articleSection: post.categories?.[0] || 'General',
      keywords: post.keywords?.join(', ') || post.tags?.join(', ') || '',
      wordCount: post.content.split(/\s+/).length,
      timeRequired: `PT${post.readingTime || 1}M`
    };

    // Add FAQ schema if post has FAQs
    if (post.faqs && post.faqs.length > 0) {
      schema.mainEntity = {
        '@type': 'FAQPage',
        mainEntity: post.faqs.map(faq => ({
          '@type': 'Question',
          name: faq.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: faq.answer
          }
        }))
      };
    }

    return schema;
  }

  // Generate breadcrumb schema
  generateBreadcrumbSchema(breadcrumbs, baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: `${baseUrl}${crumb.url}`
      }))
    };
  }

  // Generate organization schema
  generateOrganizationSchema(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Rivsy',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
      description: 'Modern blogging platform with AI-powered content assistance',
      sameAs: [
        'https://twitter.com/rivsy',
        'https://github.com/rivsy',
        'https://linkedin.com/company/rivsy'
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-0123',
        contactType: 'customer service',
        email: '<EMAIL>'
      }
    };
  }

  // Generate website schema
  generateWebsiteSchema(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Rivsy',
      url: baseUrl,
      description: 'Create beautiful, SEO-optimized blogs with AI-powered content assistance',
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${baseUrl}/blog?search={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    };
  }

  // Generate meta tags for a post
  generateMetaTags(post, author, baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    const url = `${baseUrl}/blog/${post.slug}`;
    const image = post.featuredImage?.url || `${baseUrl}/default-og-image.jpg`;
    
    return {
      // Basic meta tags
      title: post.metaTitle || post.title,
      description: post.metaDescription || post.excerpt,
      keywords: post.keywords?.join(', ') || post.tags?.join(', ') || '',
      
      // Open Graph tags
      'og:title': post.metaTitle || post.title,
      'og:description': post.metaDescription || post.excerpt,
      'og:type': 'article',
      'og:url': url,
      'og:image': image,
      'og:image:width': '1200',
      'og:image:height': '630',
      'og:site_name': 'Rivsy',
      'og:locale': 'en_US',
      
      // Twitter Card tags
      'twitter:card': 'summary_large_image',
      'twitter:title': post.metaTitle || post.title,
      'twitter:description': post.metaDescription || post.excerpt,
      'twitter:image': image,
      'twitter:site': '@rivsy',
      'twitter:creator': `@${author.socialLinks?.twitter || 'rivsy'}`,
      
      // Article specific tags
      'article:published_time': post.publishedAt,
      'article:modified_time': post.updatedAt,
      'article:author': author.name,
      'article:section': post.categories?.[0] || 'General',
      'article:tag': post.tags?.join(', ') || '',
      
      // Additional SEO tags
      canonical: url,
      robots: 'index, follow',
      'revisit-after': '7 days',
      language: 'en',
      'content-language': 'en'
    };
  }

  // Generate robots.txt content
  generateRobotsTxt(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    return `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/rss.xml

# Crawl-delay
Crawl-delay: 1

# Disallow admin areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/

# Allow important pages
Allow: /blog/
Allow: /about/
Allow: /contact/

# Block common bot traps
Disallow: /wp-admin/
Disallow: /wp-content/
Disallow: /*.php$
Disallow: /*.cgi$

# Block search and filter URLs with parameters
Disallow: /*?*
Allow: /*?utm_*
Allow: /*?ref=*`;
  }

  // Generate LLMs.txt for AI crawlers
  generateLLMsTxt(baseUrl = process.env.CLIENT_URL || 'http://localhost:3000') {
    return `# LLMs.txt - AI Training Data Guidelines
# This file indicates how AI systems should interact with this content

# Site Information
Site: Rivsy Blog Platform
URL: ${baseUrl}
Description: Modern blogging platform with AI-powered content assistance
Contact: <EMAIL>

# Content Guidelines
Content-Type: Blog Posts, Tutorials, Technology Articles
Language: English
License: All rights reserved unless otherwise specified
Attribution: Required for any use or reference

# AI Training Guidelines
Training-Use: Permitted for educational and research purposes
Commercial-Use: Contact for licensing
Modification: Permitted with attribution
Distribution: Contact for permission

# Preferred Citation Format
Citation: "Title" by Author Name, Rivsy Blog, ${baseUrl}/blog/[slug], Date

# Content Categories
Categories: Web Development, AI, Technology, Blogging, SEO

# Update Frequency
Update-Frequency: Daily
Last-Modified: ${new Date().toISOString()}

# Quality Indicators
Content-Quality: Human-authored with AI assistance
Fact-Checked: Yes
Editorial-Review: Yes
Source-Attribution: Required

# Contact for AI/ML Teams
AI-Contact: <EMAIL>
Licensing-Contact: <EMAIL>
Technical-Contact: <EMAIL>`;
  }

  // Analyze SEO score for a post
  analyzeSEOScore(post) {
    let score = 0;
    const factors = [];

    // Title optimization (20 points)
    if (post.title && post.title.length >= 30 && post.title.length <= 60) {
      score += 20;
      factors.push({ factor: 'Title Length', score: 20, status: 'good' });
    } else {
      factors.push({ factor: 'Title Length', score: 0, status: 'poor', suggestion: 'Title should be 30-60 characters' });
    }

    // Meta description (15 points)
    if (post.metaDescription && post.metaDescription.length >= 120 && post.metaDescription.length <= 160) {
      score += 15;
      factors.push({ factor: 'Meta Description', score: 15, status: 'good' });
    } else {
      factors.push({ factor: 'Meta Description', score: 0, status: 'poor', suggestion: 'Meta description should be 120-160 characters' });
    }

    // Content length (15 points)
    const wordCount = post.content.split(/\s+/).length;
    if (wordCount >= 300) {
      score += 15;
      factors.push({ factor: 'Content Length', score: 15, status: 'good' });
    } else {
      factors.push({ factor: 'Content Length', score: 0, status: 'poor', suggestion: 'Content should be at least 300 words' });
    }

    // Headings structure (10 points)
    const headingMatches = post.content.match(/^#{1,6}\s/gm);
    if (headingMatches && headingMatches.length >= 2) {
      score += 10;
      factors.push({ factor: 'Heading Structure', score: 10, status: 'good' });
    } else {
      factors.push({ factor: 'Heading Structure', score: 0, status: 'poor', suggestion: 'Use at least 2 headings (H1-H6)' });
    }

    // Featured image (10 points)
    if (post.featuredImage?.url) {
      score += 10;
      factors.push({ factor: 'Featured Image', score: 10, status: 'good' });
    } else {
      factors.push({ factor: 'Featured Image', score: 0, status: 'poor', suggestion: 'Add a featured image' });
    }

    // Categories and tags (10 points)
    if (post.categories?.length > 0 && post.tags?.length > 0) {
      score += 10;
      factors.push({ factor: 'Categories & Tags', score: 10, status: 'good' });
    } else {
      factors.push({ factor: 'Categories & Tags', score: 0, status: 'poor', suggestion: 'Add categories and tags' });
    }

    // Keywords (10 points)
    if (post.keywords?.length >= 3) {
      score += 10;
      factors.push({ factor: 'Keywords', score: 10, status: 'good' });
    } else {
      factors.push({ factor: 'Keywords', score: 0, status: 'poor', suggestion: 'Add at least 3 keywords' });
    }

    // Internal links (5 points)
    const internalLinks = post.content.match(/\[.*?\]\(\/.*?\)/g);
    if (internalLinks && internalLinks.length >= 1) {
      score += 5;
      factors.push({ factor: 'Internal Links', score: 5, status: 'good' });
    } else {
      factors.push({ factor: 'Internal Links', score: 0, status: 'poor', suggestion: 'Add internal links to other posts' });
    }

    // External links (5 points)
    const externalLinks = post.content.match(/\[.*?\]\(https?:\/\/.*?\)/g);
    if (externalLinks && externalLinks.length >= 1) {
      score += 5;
      factors.push({ factor: 'External Links', score: 5, status: 'good' });
    } else {
      factors.push({ factor: 'External Links', score: 0, status: 'poor', suggestion: 'Add relevant external links' });
    }

    return {
      score,
      grade: score >= 80 ? 'A' : score >= 60 ? 'B' : score >= 40 ? 'C' : 'D',
      factors,
      suggestions: factors.filter(f => f.status === 'poor').map(f => f.suggestion)
    };
  }
}

module.exports = new SEOService();
