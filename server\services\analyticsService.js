const Post = require('../models/Post');
const User = require('../models/User');

class AnalyticsService {
  // Get dashboard analytics for a user
  async getDashboardAnalytics(userId, timeframe = '30d') {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeframe) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Get user's posts
      const posts = await Post.find({ 
        author: userId,
        createdAt: { $gte: startDate, $lte: endDate }
      });

      const publishedPosts = posts.filter(post => post.status === 'published');
      const draftPosts = posts.filter(post => post.status === 'draft');

      // Calculate metrics
      const totalViews = posts.reduce((sum, post) => sum + (post.views || 0), 0);
      const totalLikes = posts.reduce((sum, post) => sum + (post.likes || 0), 0);
      const totalShares = posts.reduce((sum, post) => sum + (post.shares || 0), 0);
      const totalComments = posts.reduce((sum, post) => sum + (post.comments?.length || 0), 0);

      // Calculate average reading time
      const avgReadingTime = posts.length > 0 
        ? posts.reduce((sum, post) => sum + (post.readingTime || 0), 0) / posts.length 
        : 0;

      // Get top performing posts
      const topPosts = publishedPosts
        .sort((a, b) => (b.views || 0) - (a.views || 0))
        .slice(0, 5)
        .map(post => ({
          _id: post._id,
          title: post.title,
          slug: post.slug,
          views: post.views || 0,
          likes: post.likes || 0,
          publishedAt: post.publishedAt
        }));

      // Calculate growth metrics (compared to previous period)
      const previousStartDate = new Date(startDate);
      const previousEndDate = new Date(startDate);
      const timeDiff = endDate.getTime() - startDate.getTime();
      previousStartDate.setTime(previousStartDate.getTime() - timeDiff);

      const previousPosts = await Post.find({
        author: userId,
        createdAt: { $gte: previousStartDate, $lte: previousEndDate }
      });

      const previousViews = previousPosts.reduce((sum, post) => sum + (post.views || 0), 0);
      const viewsGrowth = previousViews > 0 
        ? ((totalViews - previousViews) / previousViews * 100).toFixed(1)
        : totalViews > 0 ? 100 : 0;

      // Get category performance
      const categoryStats = {};
      publishedPosts.forEach(post => {
        post.categories?.forEach(category => {
          if (!categoryStats[category]) {
            categoryStats[category] = { posts: 0, views: 0, likes: 0 };
          }
          categoryStats[category].posts++;
          categoryStats[category].views += post.views || 0;
          categoryStats[category].likes += post.likes || 0;
        });
      });

      const topCategories = Object.entries(categoryStats)
        .map(([category, stats]) => ({ category, ...stats }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 5);

      return {
        overview: {
          totalPosts: posts.length,
          publishedPosts: publishedPosts.length,
          draftPosts: draftPosts.length,
          totalViews,
          totalLikes,
          totalShares,
          totalComments,
          avgReadingTime: Math.round(avgReadingTime),
          viewsGrowth: parseFloat(viewsGrowth)
        },
        topPosts,
        topCategories,
        timeframe,
        dateRange: {
          start: startDate,
          end: endDate
        }
      };
    } catch (error) {
      console.error('Error getting dashboard analytics:', error);
      throw error;
    }
  }

  // Get detailed post analytics
  async getPostAnalytics(postId, userId) {
    try {
      const post = await Post.findById(postId).populate('author', 'name');
      
      if (!post) {
        throw new Error('Post not found');
      }

      // Check if user owns the post or is admin
      if (post.author._id.toString() !== userId.toString()) {
        const user = await User.findById(userId);
        if (user.role !== 'admin') {
          throw new Error('Access denied');
        }
      }

      // Calculate engagement rate
      const engagementRate = post.views > 0 
        ? (((post.likes || 0) + (post.shares || 0) + (post.comments?.length || 0)) / post.views * 100).toFixed(2)
        : 0;

      // Get daily views for the last 30 days (simulated data)
      const dailyViews = this.generateDailyViewsData(post.views || 0);

      // Calculate SEO metrics
      const seoMetrics = {
        titleLength: post.title.length,
        metaDescriptionLength: post.metaDescription?.length || 0,
        contentLength: post.content.split(/\s+/).length,
        headingsCount: (post.content.match(/^#{1,6}\s/gm) || []).length,
        internalLinksCount: (post.content.match(/\[.*?\]\(\/.*?\)/g) || []).length,
        externalLinksCount: (post.content.match(/\[.*?\]\(https?:\/\/.*?\)/g) || []).length,
        imagesCount: (post.content.match(/!\[.*?\]\(.*?\)/g) || []).length
      };

      return {
        post: {
          _id: post._id,
          title: post.title,
          slug: post.slug,
          status: post.status,
          publishedAt: post.publishedAt,
          createdAt: post.createdAt,
          updatedAt: post.updatedAt
        },
        metrics: {
          views: post.views || 0,
          likes: post.likes || 0,
          shares: post.shares || 0,
          comments: post.comments?.length || 0,
          readingTime: post.readingTime || 0,
          engagementRate: parseFloat(engagementRate)
        },
        seoMetrics,
        dailyViews,
        categories: post.categories || [],
        tags: post.tags || []
      };
    } catch (error) {
      console.error('Error getting post analytics:', error);
      throw error;
    }
  }

  // Get site-wide analytics (admin only)
  async getSiteAnalytics(timeframe = '30d') {
    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeframe) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Get all posts and users
      const posts = await Post.find({
        createdAt: { $gte: startDate, $lte: endDate }
      }).populate('author', 'name');

      const users = await User.find({
        createdAt: { $gte: startDate, $lte: endDate }
      });

      const publishedPosts = posts.filter(post => post.status === 'published');

      // Calculate metrics
      const totalViews = posts.reduce((sum, post) => sum + (post.views || 0), 0);
      const totalLikes = posts.reduce((sum, post) => sum + (post.likes || 0), 0);
      const totalShares = posts.reduce((sum, post) => sum + (post.shares || 0), 0);

      // Get top authors
      const authorStats = {};
      publishedPosts.forEach(post => {
        const authorId = post.author._id.toString();
        if (!authorStats[authorId]) {
          authorStats[authorId] = {
            name: post.author.name,
            posts: 0,
            views: 0,
            likes: 0
          };
        }
        authorStats[authorId].posts++;
        authorStats[authorId].views += post.views || 0;
        authorStats[authorId].likes += post.likes || 0;
      });

      const topAuthors = Object.values(authorStats)
        .sort((a, b) => b.views - a.views)
        .slice(0, 10);

      // Get most popular posts
      const popularPosts = publishedPosts
        .sort((a, b) => (b.views || 0) - (a.views || 0))
        .slice(0, 10)
        .map(post => ({
          _id: post._id,
          title: post.title,
          slug: post.slug,
          author: post.author.name,
          views: post.views || 0,
          likes: post.likes || 0,
          publishedAt: post.publishedAt
        }));

      return {
        overview: {
          totalUsers: users.length,
          totalPosts: posts.length,
          publishedPosts: publishedPosts.length,
          totalViews,
          totalLikes,
          totalShares,
          avgViewsPerPost: publishedPosts.length > 0 ? Math.round(totalViews / publishedPosts.length) : 0
        },
        topAuthors,
        popularPosts,
        timeframe,
        dateRange: {
          start: startDate,
          end: endDate
        }
      };
    } catch (error) {
      console.error('Error getting site analytics:', error);
      throw error;
    }
  }

  // Generate simulated daily views data
  generateDailyViewsData(totalViews, days = 30) {
    const data = [];
    const baseViews = Math.floor(totalViews / days);
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      // Add some randomness to make it look realistic
      const variance = Math.random() * 0.4 + 0.8; // 80% to 120% of base
      const views = Math.floor(baseViews * variance);
      
      data.push({
        date: date.toISOString().split('T')[0],
        views: Math.max(0, views)
      });
    }
    
    return data;
  }

  // Track page view (in a real app, this would be more sophisticated)
  async trackPageView(postId, userAgent, ip) {
    try {
      // In a real implementation, you would:
      // 1. Check if this is a unique view (using cookies, IP, etc.)
      // 2. Store detailed analytics data
      // 3. Update the post's view count
      
      const post = await Post.findById(postId);
      if (post) {
        post.views = (post.views || 0) + 1;
        await post.save();
        
        return {
          success: true,
          views: post.views
        };
      }
      
      return { success: false, message: 'Post not found' };
    } catch (error) {
      console.error('Error tracking page view:', error);
      throw error;
    }
  }
}

module.exports = new AnalyticsService();
