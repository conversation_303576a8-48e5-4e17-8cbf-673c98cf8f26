const axios = require('axios');

class AIService {
  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY || process.env.AZURE_API_KEY;
    this.baseURL = this.getBaseURL();
    this.model = this.getModel();
  }

  getBaseURL() {
    if (process.env.AZURE_API_KEY) {
      return process.env.AZURE_API_BASE;
    }
    return 'https://api.openai.com/v1';
  }

  getModel() {
    if (process.env.AZURE_API_KEY) {
      return process.env.AZURE_DEPLOYMENT_NAME || 'gpt-4';
    }
    return 'gpt-4';
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (process.env.AZURE_API_KEY) {
      headers['api-key'] = this.apiKey;
    } else {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    return headers;
  }

  async makeRequest(messages, options = {}) {
    try {
      const requestData = {
        model: this.model,
        messages,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        ...options
      };

      let url = `${this.baseURL}/chat/completions`;
      if (process.env.AZURE_API_KEY) {
        url += `?api-version=${process.env.AZURE_API_VERSION || '2023-12-01-preview'}`;
      }

      const response = await axios.post(url, requestData, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error('AI Service Error:', error.response?.data || error.message);
      throw new Error('AI service temporarily unavailable');
    }
  }

  async generateBlogPost(topic, options = {}) {
    const {
      tone = 'professional',
      length = 'medium',
      audience = 'general',
      keywords = [],
      includeOutline = true
    } = options;

    const lengthGuide = {
      short: '500-800 words',
      medium: '1000-1500 words',
      long: '2000-3000 words'
    };

    const messages = [
      {
        role: 'system',
        content: `You are an expert content writer and SEO specialist. Create high-quality, engaging blog posts that are optimized for search engines and provide real value to readers.`
      },
      {
        role: 'user',
        content: `Write a comprehensive blog post about "${topic}".

Requirements:
- Tone: ${tone}
- Length: ${lengthGuide[length]}
- Target audience: ${audience}
- Include these keywords naturally: ${keywords.join(', ')}
- Format in Markdown
- Include proper headings (H1, H2, H3)
- Add a compelling introduction and conclusion
- Include actionable insights and examples
${includeOutline ? '- Start with a brief outline' : ''}

Make it engaging, informative, and SEO-friendly.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 2000 });
  }

  async generateBlogOutline(topic, options = {}) {
    const {
      sections = 5,
      audience = 'general',
      keywords = []
    } = options;

    const messages = [
      {
        role: 'system',
        content: 'You are an expert content strategist. Create detailed blog post outlines that are logical, comprehensive, and SEO-optimized.'
      },
      {
        role: 'user',
        content: `Create a detailed outline for a blog post about "${topic}".

Requirements:
- ${sections} main sections
- Target audience: ${audience}
- Include these keywords: ${keywords.join(', ')}
- Each section should have 2-3 subsections
- Include suggested word count for each section
- Add SEO considerations
- Format as a structured list`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 800 });
  }

  async improveBlogPost(content, improvements = []) {
    const improvementTypes = {
      seo: 'Optimize for SEO (keywords, meta descriptions, headings)',
      readability: 'Improve readability and flow',
      engagement: 'Make more engaging and compelling',
      structure: 'Improve structure and organization',
      tone: 'Adjust tone and voice',
      length: 'Expand or condense content'
    };

    const selectedImprovements = improvements.map(imp => improvementTypes[imp] || imp).join(', ');

    const messages = [
      {
        role: 'system',
        content: 'You are an expert editor and content optimizer. Improve blog posts while maintaining the original message and adding value.'
      },
      {
        role: 'user',
        content: `Please improve the following blog post content:

${content}

Focus on these improvements: ${selectedImprovements}

Return the improved version in Markdown format, maintaining the original structure but enhancing quality, clarity, and effectiveness.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 2000 });
  }

  async generateSEOMetadata(content, title) {
    const messages = [
      {
        role: 'system',
        content: 'You are an SEO expert. Generate optimized metadata for blog posts that will improve search engine rankings.'
      },
      {
        role: 'user',
        content: `Generate SEO metadata for this blog post:

Title: ${title}
Content: ${content.substring(0, 1000)}...

Please provide:
1. Optimized meta title (50-60 characters)
2. Meta description (150-160 characters)
3. 5-8 relevant keywords
4. Suggested slug (URL-friendly)
5. Open Graph title and description

Format as JSON.`
      }
    ];

    const response = await this.makeRequest(messages, { maxTokens: 500 });
    
    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback if JSON parsing fails
      return {
        metaTitle: title.substring(0, 60),
        metaDescription: content.substring(0, 160).replace(/[#*]/g, ''),
        keywords: [],
        slug: title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        ogTitle: title,
        ogDescription: content.substring(0, 160).replace(/[#*]/g, '')
      };
    }
  }

  async generateContentIdeas(niche, count = 10) {
    const messages = [
      {
        role: 'system',
        content: 'You are a content strategist who generates creative, trending, and valuable blog post ideas.'
      },
      {
        role: 'user',
        content: `Generate ${count} creative and engaging blog post ideas for the "${niche}" niche.

For each idea, provide:
- Catchy title
- Brief description (1-2 sentences)
- Target audience
- Estimated difficulty (Beginner/Intermediate/Advanced)
- Trending potential (High/Medium/Low)

Format as a numbered list.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 1000 });
  }

  async generateSocialMediaPosts(blogTitle, blogContent, platforms = ['twitter', 'linkedin', 'facebook']) {
    const platformSpecs = {
      twitter: 'Twitter (280 characters, engaging, with hashtags)',
      linkedin: 'LinkedIn (professional tone, 1-2 paragraphs)',
      facebook: 'Facebook (casual, engaging, with call-to-action)'
    };

    const selectedPlatforms = platforms.map(p => platformSpecs[p]).join(', ');

    const messages = [
      {
        role: 'system',
        content: 'You are a social media expert who creates engaging posts that drive traffic to blog content.'
      },
      {
        role: 'user',
        content: `Create social media posts to promote this blog post:

Title: ${blogTitle}
Content summary: ${blogContent.substring(0, 500)}...

Create posts for: ${selectedPlatforms}

Each post should:
- Be platform-appropriate
- Include relevant hashtags
- Have a clear call-to-action
- Be engaging and shareable

Format each platform's post clearly labeled.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 800 });
  }

  async analyzeContentSEO(content, targetKeywords = []) {
    const messages = [
      {
        role: 'system',
        content: 'You are an SEO analyst. Analyze content and provide actionable SEO recommendations.'
      },
      {
        role: 'user',
        content: `Analyze this content for SEO optimization:

Content: ${content}
Target keywords: ${targetKeywords.join(', ')}

Provide analysis on:
1. Keyword density and placement
2. Heading structure (H1, H2, H3)
3. Content length and readability
4. Internal linking opportunities
5. Meta tag suggestions
6. Overall SEO score (1-100)
7. Specific improvement recommendations

Format as a structured analysis.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 1000 });
  }

  async generateFAQs(topic, count = 5) {
    const messages = [
      {
        role: 'system',
        content: 'You are an expert who anticipates common questions people have about topics and provides comprehensive answers.'
      },
      {
        role: 'user',
        content: `Generate ${count} frequently asked questions and detailed answers about "${topic}".

Each FAQ should:
- Address common concerns or curiosities
- Provide comprehensive, helpful answers
- Be SEO-friendly
- Include relevant keywords naturally

Format as Q&A pairs.`
      }
    ];

    return await this.makeRequest(messages, { maxTokens: 1200 });
  }

  // Check if AI service is configured and available
  isConfigured() {
    return !!(this.apiKey && (process.env.OPENAI_API_KEY || process.env.AZURE_API_KEY));
  }

  // Health check for AI service
  async healthCheck() {
    if (!this.isConfigured()) {
      throw new Error('AI service not configured');
    }

    try {
      const messages = [
        {
          role: 'user',
          content: 'Hello, please respond with "AI service is working" to confirm connectivity.'
        }
      ];

      const response = await this.makeRequest(messages, { maxTokens: 50 });
      return response.includes('working') || response.includes('AI service');
    } catch (error) {
      throw new Error('AI service health check failed');
    }
  }
}

module.exports = new AIService();
