import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>rkles, 
  X, 
  Send, 
  Lightbulb, 
  Search, 
  Edit, 
  Share2,
  FileText,
  Target,
  HelpCircle,
  Loader
} from 'lucide-react';
import axios from 'axios';
import toast from 'react-hot-toast';

const AIAssistant = ({ isOpen, onClose, currentContent = '', onContentUpdate }) => {
  const [activeTab, setActiveTab] = useState('generate');
  const [loading, setLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [result, setResult] = useState('');

  const tabs = [
    { id: 'generate', label: 'Generate', icon: FileText },
    { id: 'improve', label: 'Improve', icon: Edit },
    { id: 'seo', label: 'SEO', icon: Target },
    { id: 'ideas', label: 'Ideas', icon: Lightbulb },
    { id: 'social', label: 'Social', icon: Share2 },
    { id: 'faqs', label: 'FAQs', icon: HelpCircle },
  ];

  const handleAIRequest = async (endpoint, data) => {
    setLoading(true);
    try {
      const response = await axios.post(`/api/ai/${endpoint}`, data);
      setResult(response.data.data);
      toast.success('AI assistance completed!');
    } catch (error) {
      const message = error.response?.data?.message || 'AI service error';
      toast.error(message);
      console.error('AI request error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a topic or prompt');
      return;
    }

    await handleAIRequest('generate-post', {
      topic: prompt,
      tone: 'professional',
      length: 'medium',
      audience: 'general',
      keywords: [],
      includeOutline: true
    });
  };

  const handleImprove = async () => {
    if (!currentContent.trim()) {
      toast.error('No content to improve');
      return;
    }

    await handleAIRequest('improve-content', {
      content: currentContent,
      improvements: ['seo', 'readability', 'engagement']
    });
  };

  const handleSEOAnalysis = async () => {
    if (!currentContent.trim()) {
      toast.error('No content to analyze');
      return;
    }

    await handleAIRequest('analyze-seo', {
      content: currentContent,
      targetKeywords: []
    });
  };

  const handleGenerateIdeas = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a niche or topic');
      return;
    }

    await handleAIRequest('content-ideas', {
      niche: prompt,
      count: 10
    });
  };

  const handleGenerateSocial = async () => {
    if (!currentContent.trim()) {
      toast.error('No content to create social posts from');
      return;
    }

    await handleAIRequest('social-posts', {
      blogTitle: prompt || 'Blog Post',
      blogContent: currentContent,
      platforms: ['twitter', 'linkedin', 'facebook']
    });
  };

  const handleGenerateFAQs = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a topic for FAQs');
      return;
    }

    await handleAIRequest('generate-faqs', {
      topic: prompt,
      count: 5
    });
  };

  const handleUseResult = () => {
    if (result && typeof result === 'object' && result.content) {
      onContentUpdate?.(result.content);
    } else if (typeof result === 'string') {
      onContentUpdate?.(result);
    } else if (result && result.improvedContent) {
      onContentUpdate?.(result.improvedContent);
    }
    toast.success('Content applied to editor!');
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'generate':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Topic or Title
              </label>
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="e.g., How to build a React app with Vite"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <button
              onClick={handleGenerate}
              disabled={loading}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Sparkles className="w-4 h-4" />}
              <span>Generate Blog Post</span>
            </button>
          </div>
        );

      case 'improve':
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Improve your current content for better SEO, readability, and engagement.
            </p>
            <button
              onClick={handleImprove}
              disabled={loading || !currentContent.trim()}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Edit className="w-4 h-4" />}
              <span>Improve Content</span>
            </button>
          </div>
        );

      case 'seo':
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Analyze your content for SEO optimization and get improvement suggestions.
            </p>
            <button
              onClick={handleSEOAnalysis}
              disabled={loading || !currentContent.trim()}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Target className="w-4 h-4" />}
              <span>Analyze SEO</span>
            </button>
          </div>
        );

      case 'ideas':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Niche or Industry
              </label>
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="e.g., Web Development, Digital Marketing"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <button
              onClick={handleGenerateIdeas}
              disabled={loading}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Lightbulb className="w-4 h-4" />}
              <span>Generate Ideas</span>
            </button>
          </div>
        );

      case 'social':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Post Title (optional)
              </label>
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Blog post title"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Generate social media posts for Twitter, LinkedIn, and Facebook based on your content.
            </p>
            <button
              onClick={handleGenerateSocial}
              disabled={loading || !currentContent.trim()}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <Share2 className="w-4 h-4" />}
              <span>Generate Social Posts</span>
            </button>
          </div>
        );

      case 'faqs':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Topic for FAQs
              </label>
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="e.g., React Development, SEO Basics"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <button
              onClick={handleGenerateFAQs}
              disabled={loading}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? <Loader className="w-4 h-4 animate-spin" /> : <HelpCircle className="w-4 h-4" />}
              <span>Generate FAQs</span>
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  const renderResult = () => {
    if (!result) return null;

    let displayContent = '';
    
    if (typeof result === 'string') {
      displayContent = result;
    } else if (result.content) {
      displayContent = result.content;
    } else if (result.improvedContent) {
      displayContent = result.improvedContent;
    } else if (result.analysis) {
      displayContent = result.analysis;
    } else if (result.ideas) {
      displayContent = result.ideas;
    } else if (result.socialPosts) {
      displayContent = result.socialPosts;
    } else if (result.faqs) {
      displayContent = result.faqs;
    } else {
      displayContent = JSON.stringify(result, null, 2);
    }

    return (
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900 dark:text-white">AI Result</h4>
          <button
            onClick={handleUseResult}
            className="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 transition-colors"
          >
            Use This
          </button>
        </div>
        <div className="max-h-64 overflow-y-auto">
          <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
            {displayContent}
          </pre>
        </div>
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-6 h-6 text-indigo-600" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  AI Writing Assistant
                </h3>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'text-indigo-600 border-b-2 border-indigo-600'
                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </div>

            {/* Content */}
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              {renderTabContent()}
              {renderResult()}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AIAssistant;
