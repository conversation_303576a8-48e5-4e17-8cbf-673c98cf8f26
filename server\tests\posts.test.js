const request = require('supertest');
const app = require('../index');
const Post = require('../models/Post');

describe('Posts Endpoints', () => {
  let testUser;
  let adminUser;

  beforeEach(async () => {
    testUser = await global.testUtils.createTestUser();
    adminUser = await global.testUtils.createTestUser({
      email: '<EMAIL>',
      role: 'admin'
    });
  });

  describe('GET /api/posts', () => {
    beforeEach(async () => {
      // Create test posts
      await global.testUtils.createTestPost({
        title: 'Published Post 1',
        status: 'published',
        publishedAt: new Date()
      }, testUser);

      await global.testUtils.createTestPost({
        title: 'Published Post 2',
        status: 'published',
        publishedAt: new Date()
      }, testUser);

      await global.testUtils.createTestPost({
        title: 'Draft Post',
        status: 'draft'
      }, testUser);
    });

    it('should get published posts only', async () => {
      const response = await request(app)
        .get('/api/posts')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.every(post => post.status === 'published')).toBe(true);
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/posts?page=1&limit=1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.currentPage).toBe(1);
      expect(response.body.pagination.totalPages).toBe(2);
    });

    it('should support search', async () => {
      const response = await request(app)
        .get('/api/posts?search=Published Post 1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].title).toContain('Published Post 1');
    });

    it('should support category filtering', async () => {
      await global.testUtils.createTestPost({
        title: 'Tech Post',
        status: 'published',
        categories: ['Technology']
      }, testUser);

      const response = await request(app)
        .get('/api/posts?category=Technology')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].categories).toContain('Technology');
    });
  });

  describe('GET /api/posts/:slug', () => {
    let testPost;

    beforeEach(async () => {
      testPost = await global.testUtils.createTestPost({
        title: 'Test Post',
        slug: 'test-post',
        status: 'published'
      }, testUser);
    });

    it('should get post by slug', async () => {
      const response = await request(app)
        .get(`/api/posts/${testPost.slug}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(testPost.title);
      expect(response.body.data.slug).toBe(testPost.slug);
      expect(response.body.data.author).toBeDefined();
    });

    it('should return 404 for non-existent post', async () => {
      const response = await request(app)
        .get('/api/posts/non-existent-slug')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not found');
    });

    it('should not return draft posts to non-authors', async () => {
      const draftPost = await global.testUtils.createTestPost({
        title: 'Draft Post',
        slug: 'draft-post',
        status: 'draft'
      }, testUser);

      const response = await request(app)
        .get(`/api/posts/${draftPost.slug}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/posts', () => {
    it('should create new post successfully', async () => {
      const postData = {
        title: 'New Test Post',
        content: 'This is the content of the new test post.',
        excerpt: 'Test excerpt',
        categories: ['Technology', 'Web Development'],
        tags: ['react', 'javascript'],
        status: 'draft'
      };

      const response = await request(app)
        .post('/api/posts')
        .set(global.testUtils.authHeaders(testUser))
        .send(postData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(postData.title);
      expect(response.body.data.content).toBe(postData.content);
      expect(response.body.data.author).toBe(testUser._id.toString());
      expect(response.body.data.slug).toBeDefined();

      // Verify in database
      const post = await Post.findById(response.body.data._id);
      expect(post).toBeTruthy();
      expect(post.readingTime).toBeDefined();
    });

    it('should not create post without authentication', async () => {
      const postData = {
        title: 'New Test Post',
        content: 'Content'
      };

      const response = await request(app)
        .post('/api/posts')
        .send(postData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should not create post with invalid data', async () => {
      const postData = {
        title: '', // Empty title
        content: 'Content'
      };

      const response = await request(app)
        .post('/api/posts')
        .set(global.testUtils.authHeaders(testUser))
        .send(postData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('validation');
    });

    it('should auto-publish if status is published', async () => {
      const postData = {
        title: 'Published Post',
        content: 'Content',
        status: 'published'
      };

      const response = await request(app)
        .post('/api/posts')
        .set(global.testUtils.authHeaders(testUser))
        .send(postData)
        .expect(201);

      expect(response.body.data.status).toBe('published');
      expect(response.body.data.publishedAt).toBeDefined();
    });
  });

  describe('PUT /api/posts/:id', () => {
    let testPost;

    beforeEach(async () => {
      testPost = await global.testUtils.createTestPost({
        title: 'Original Title',
        content: 'Original content'
      }, testUser);
    });

    it('should update post successfully', async () => {
      const updateData = {
        title: 'Updated Title',
        content: 'Updated content',
        categories: ['Updated Category']
      };

      const response = await request(app)
        .put(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(testUser))
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.content).toBe(updateData.content);
      expect(response.body.data.categories).toEqual(updateData.categories);
    });

    it('should not update post without authentication', async () => {
      const updateData = {
        title: 'Updated Title'
      };

      const response = await request(app)
        .put(`/api/posts/${testPost._id}`)
        .send(updateData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should not update post by non-author', async () => {
      const otherUser = await global.testUtils.createTestUser({
        email: '<EMAIL>'
      });

      const updateData = {
        title: 'Updated Title'
      };

      const response = await request(app)
        .put(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(otherUser))
        .send(updateData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Access denied');
    });

    it('should allow admin to update any post', async () => {
      const updateData = {
        title: 'Admin Updated Title'
      };

      const response = await request(app)
        .put(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(adminUser))
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(updateData.title);
    });

    it('should update publishedAt when changing from draft to published', async () => {
      const draftPost = await global.testUtils.createTestPost({
        status: 'draft'
      }, testUser);

      const response = await request(app)
        .put(`/api/posts/${draftPost._id}`)
        .set(global.testUtils.authHeaders(testUser))
        .send({ status: 'published' })
        .expect(200);

      expect(response.body.data.status).toBe('published');
      expect(response.body.data.publishedAt).toBeDefined();
    });
  });

  describe('DELETE /api/posts/:id', () => {
    let testPost;

    beforeEach(async () => {
      testPost = await global.testUtils.createTestPost({}, testUser);
    });

    it('should delete post successfully', async () => {
      const response = await request(app)
        .delete(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(testUser))
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('deleted');

      // Verify post is deleted
      const deletedPost = await Post.findById(testPost._id);
      expect(deletedPost).toBeNull();
    });

    it('should not delete post without authentication', async () => {
      const response = await request(app)
        .delete(`/api/posts/${testPost._id}`)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should not delete post by non-author', async () => {
      const otherUser = await global.testUtils.createTestUser({
        email: '<EMAIL>'
      });

      const response = await request(app)
        .delete(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(otherUser))
        .expect(403);

      expect(response.body.success).toBe(false);
    });

    it('should allow admin to delete any post', async () => {
      const response = await request(app)
        .delete(`/api/posts/${testPost._id}`)
        .set(global.testUtils.authHeaders(adminUser))
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/posts/my/posts', () => {
    beforeEach(async () => {
      await global.testUtils.createTestPost({
        title: 'My Post 1'
      }, testUser);

      await global.testUtils.createTestPost({
        title: 'My Post 2'
      }, testUser);

      // Create post by another user
      const otherUser = await global.testUtils.createTestUser({
        email: '<EMAIL>'
      });
      await global.testUtils.createTestPost({
        title: 'Other User Post'
      }, otherUser);
    });

    it('should get only current user posts', async () => {
      const response = await request(app)
        .get('/api/posts/my/posts')
        .set(global.testUtils.authHeaders(testUser))
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.every(post => 
        post.author._id === testUser._id.toString()
      )).toBe(true);
    });

    it('should not get posts without authentication', async () => {
      const response = await request(app)
        .get('/api/posts/my/posts')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});
