import type { SignUpAttempt } from '../resources/SignUpAttempt';
import { AbstractAPI } from './AbstractApi';
type UpdateSignUpParams = {
    signUpAttemptId: string;
    externalId?: string | null;
    customAction?: boolean | null;
};
export declare class SignUpAPI extends AbstractAPI {
    get(signUpAttemptId: string): Promise<SignUpAttempt>;
    update(params: UpdateSignUpParams): Promise<SignUpAttempt>;
}
export {};
//# sourceMappingURL=SignUpApi.d.ts.map