export type IssuerResolver = string | ((iss: string) => boolean);
export declare const assertAudienceClaim: (aud?: unknown, audience?: unknown) => void;
export declare const assertHeaderType: (typ?: unknown) => void;
export declare const assertHeaderAlgorithm: (alg: string) => void;
export declare const assertSubClaim: (sub?: string) => void;
export declare const assertAuthorizedPartiesClaim: (azp?: string, authorizedParties?: string[]) => void;
export declare const assertExpirationClaim: (exp: number, clockSkewInMs: number) => void;
export declare const assertActivationClaim: (nbf: number | undefined, clockSkewInMs: number) => void;
export declare const assertIssuedAtClaim: (iat: number | undefined, clockSkewInMs: number) => void;
//# sourceMappingURL=assertions.d.ts.map