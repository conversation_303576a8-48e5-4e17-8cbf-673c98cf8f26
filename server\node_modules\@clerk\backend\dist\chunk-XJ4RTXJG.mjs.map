{"version": 3, "sources": ["../src/runtime.ts", "../src/util/rfc4648.ts", "../src/jwt/algorithms.ts", "../src/jwt/assertions.ts", "../src/jwt/cryptoKeys.ts", "../src/jwt/verifyJwt.ts"], "sourcesContent": ["/**\n * This file exports APIs that vary across runtimes (i.e. Node & Browser - V8 isolates)\n * as a singleton object.\n *\n * Runtime polyfills are written in VanillaJS for now to avoid TS complication. Moreover,\n * due to this issue https://github.com/microsoft/TypeScript/issues/44848, there is not a good way\n * to tell Typescript which conditional import to use during build type.\n *\n * The Runtime type definition ensures type safety for now.\n * Runtime js modules are copied into dist folder with bash script.\n *\n * TODO: Support TS runtime modules\n */\n\n// @ts-ignore - These are package subpaths\nimport { webcrypto as crypto } from '#crypto';\n\ntype Runtime = {\n  crypto: Crypto;\n  fetch: typeof globalThis.fetch;\n  AbortController: typeof globalThis.AbortController;\n  Blob: typeof globalThis.Blob;\n  FormData: typeof globalThis.FormData;\n  Headers: typeof globalThis.Headers;\n  Request: typeof globalThis.Request;\n  Response: typeof globalThis.Response;\n};\n\n// Invoking the global.fetch without binding it first to the globalObject fails in\n// Cloudflare Workers with an \"Illegal Invocation\" error.\n//\n// The globalThis object is supported for Node >= 12.0.\n//\n// https://github.com/supabase/supabase/issues/4417\nconst globalFetch = fetch.bind(globalThis);\n\nexport const runtime: Runtime = {\n  crypto,\n  get fetch() {\n    // We need to use the globalFetch for Cloudflare Workers but the fetch for testing\n    return process.env.NODE_ENV === 'test' ? fetch : globalFetch;\n  },\n  AbortController: globalThis.AbortController,\n  Blob: globalThis.Blob,\n  FormData: globalThis.FormData,\n  Headers: globalThis.Headers,\n  Request: globalThis.Request,\n  Response: globalThis.Response,\n};\n", "/**\n * The base64url helper was extracted from the rfc4648 package\n * in order to resolve CSJ/ESM interoperability issues\n *\n * https://github.com/swansontec/rfc4648.js\n *\n * For more context please refer to:\n * - https://github.com/evanw/esbuild/issues/1719\n * - https://github.com/evanw/esbuild/issues/532\n * - https://github.com/swansontec/rollup-plugin-mjs-entry\n */\nexport const base64url = {\n  parse(string: string, opts?: ParseOptions): Uint8Array {\n    return parse(string, base64UrlEncoding, opts);\n  },\n\n  stringify(data: ArrayLike<number>, opts?: StringifyOptions): string {\n    return stringify(data, base64UrlEncoding, opts);\n  },\n};\n\nconst base64UrlEncoding: Encoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bits: 6,\n};\n\ninterface Encoding {\n  bits: number;\n  chars: string;\n  codes?: { [char: string]: number };\n}\n\ninterface ParseOptions {\n  loose?: boolean;\n  out?: new (size: number) => { [index: number]: number };\n}\n\ninterface StringifyOptions {\n  pad?: boolean;\n}\n\nfunction parse(string: string, encoding: Encoding, opts: ParseOptions = {}): Uint8Array {\n  // Build the character lookup table:\n  if (!encoding.codes) {\n    encoding.codes = {};\n    for (let i = 0; i < encoding.chars.length; ++i) {\n      encoding.codes[encoding.chars[i]] = i;\n    }\n  }\n\n  // The string must have a whole number of bytes:\n  if (!opts.loose && (string.length * encoding.bits) & 7) {\n    throw new SyntaxError('Invalid padding');\n  }\n\n  // Count the padding bytes:\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n\n    // If we get a whole number of bytes, there is too much padding:\n    if (!opts.loose && !(((string.length - end) * encoding.bits) & 7)) {\n      throw new SyntaxError('Invalid padding');\n    }\n  }\n\n  // Allocate the output:\n  const out = new (opts.out ?? Uint8Array)(((end * encoding.bits) / 8) | 0) as Uint8Array;\n\n  // Parse the data:\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  let written = 0; // Next byte to write\n  for (let i = 0; i < end; ++i) {\n    // Read one character from the string:\n    const value = encoding.codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError('Invalid character ' + string[i]);\n    }\n\n    // Append the bits to the buffer:\n    buffer = (buffer << encoding.bits) | value;\n    bits += encoding.bits;\n\n    // Write out some bits if the buffer has a byte's worth:\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 0xff & (buffer >> bits);\n    }\n  }\n\n  // Verify that we have received just enough bits:\n  if (bits >= encoding.bits || 0xff & (buffer << (8 - bits))) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n\n  return out;\n}\n\nfunction stringify(data: ArrayLike<number>, encoding: Encoding, opts: StringifyOptions = {}): string {\n  const { pad = true } = opts;\n  const mask = (1 << encoding.bits) - 1;\n  let out = '';\n\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  for (let i = 0; i < data.length; ++i) {\n    // Slurp data into the buffer:\n    buffer = (buffer << 8) | (0xff & data[i]);\n    bits += 8;\n\n    // Write out as much as we can:\n    while (bits > encoding.bits) {\n      bits -= encoding.bits;\n      out += encoding.chars[mask & (buffer >> bits)];\n    }\n  }\n\n  // Partial character:\n  if (bits) {\n    out += encoding.chars[mask & (buffer << (encoding.bits - bits))];\n  }\n\n  // Add padding characters until we hit a byte boundary:\n  if (pad) {\n    while ((out.length * encoding.bits) & 7) {\n      out += '=';\n    }\n  }\n\n  return out;\n}\n", "const algToHash: Record<string, string> = {\n  RS256: 'SHA-256',\n  RS384: 'SHA-384',\n  RS512: 'SHA-512',\n};\nconst RSA_ALGORITHM_NAME = 'RSASSA-PKCS1-v1_5';\n\nconst jwksAlgToCryptoAlg: Record<string, string> = {\n  RS256: RSA_ALGORITHM_NAME,\n  RS384: RSA_ALGORITHM_NAME,\n  RS512: RSA_ALGORITHM_NAME,\n};\n\nexport const algs = Object.keys(algToHash);\n\nexport function getCryptoAlgorithm(algorithmName: string): RsaHashedImportParams {\n  const hash = algToHash[algorithmName];\n  const name = jwksAlgToCryptoAlg[algorithmName];\n\n  if (!hash || !name) {\n    throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(',')}.`);\n  }\n\n  return {\n    hash: { name: algToHash[algorithmName] },\n    name: jwksAlgToCryptoAlg[algorithmName],\n  };\n}\n", "import { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { algs } from './algorithms';\n\nexport type IssuerResolver = string | ((iss: string) => boolean);\n\nconst isArrayString = (s: unknown): s is string[] => {\n  return Array.isArray(s) && s.length > 0 && s.every(a => typeof a === 'string');\n};\n\nexport const assertAudienceClaim = (aud?: unknown, audience?: unknown) => {\n  const audienceList = [audience].flat().filter(a => !!a);\n  const audList = [aud].flat().filter(a => !!a);\n  const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;\n\n  if (!shouldVerifyAudience) {\n    // Notice: Clerk JWTs use AZP claim instead of Audience\n    //\n    // return {\n    //   valid: false,\n    //   reason: `Invalid JWT audience claim (aud) ${JSON.stringify(\n    //     aud,\n    //   )}. Expected a string or a non-empty array of strings.`,\n    // };\n    return;\n  }\n\n  if (typeof aud === 'string') {\n    if (!audienceList.includes(aud)) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  } else if (isArrayString(aud)) {\n    if (!aud.some(a => audienceList.includes(a))) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  }\n};\n\nexport const assertHeaderType = (typ?: unknown) => {\n  if (typeof typ === 'undefined') {\n    return;\n  }\n\n  if (typ !== 'JWT') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalid,\n      message: `Invalid JWT type ${JSON.stringify(typ)}. Expected \"JWT\".`,\n    });\n  }\n};\n\nexport const assertHeaderAlgorithm = (alg: string) => {\n  if (!algs.includes(alg)) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,\n      message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`,\n    });\n  }\n};\n\nexport const assertSubClaim = (sub?: string) => {\n  if (typeof sub !== 'string') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`,\n    });\n  }\n};\n\nexport const assertAuthorizedPartiesClaim = (azp?: string, authorizedParties?: string[]) => {\n  if (!azp || !authorizedParties || authorizedParties.length === 0) {\n    return;\n  }\n\n  if (!authorizedParties.includes(azp)) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,\n      message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected \"${authorizedParties}\".`,\n    });\n  }\n};\n\nexport const assertExpirationClaim = (exp: number, clockSkewInMs: number) => {\n  if (typeof exp !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const expiryDate = new Date(0);\n  expiryDate.setUTCSeconds(exp);\n\n  const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;\n  if (expired) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenExpired,\n      message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`,\n    });\n  }\n};\n\nexport const assertActivationClaim = (nbf: number | undefined, clockSkewInMs: number) => {\n  if (typeof nbf === 'undefined') {\n    return;\n  }\n\n  if (typeof nbf !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const notBeforeDate = new Date(0);\n  notBeforeDate.setUTCSeconds(nbf);\n\n  const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (early) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenNotActiveYet,\n      message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n\nexport const assertIssuedAtClaim = (iat: number | undefined, clockSkewInMs: number) => {\n  if (typeof iat === 'undefined') {\n    return;\n  }\n\n  if (typeof iat !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const issuedAtDate = new Date(0);\n  issuedAtDate.setUTCSeconds(iat);\n\n  const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (postIssued) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenIatInTheFuture,\n      message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n", "import { isomorphicAtob } from '@clerk/shared/isomorphicAtob';\n\nimport { runtime } from '../runtime';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey#pkcs_8_import\nfunction pemToBuffer(secret: string): ArrayBuffer {\n  const trimmed = secret\n    .replace(/-----BEGIN.*?-----/g, '')\n    .replace(/-----END.*?-----/g, '')\n    .replace(/\\s/g, '');\n\n  const decoded = isomorphicAtob(trimmed);\n\n  const buffer = new ArrayBuffer(decoded.length);\n  const bufView = new Uint8Array(buffer);\n\n  for (let i = 0, strLen = decoded.length; i < strLen; i++) {\n    bufView[i] = decoded.charCodeAt(i);\n  }\n\n  return bufView;\n}\n\nexport function importKey(\n  key: JsonWebKey | string,\n  algorithm: RsaHashedImportParams,\n  keyUsage: 'verify' | 'sign',\n): Promise<CryptoKey> {\n  if (typeof key === 'object') {\n    return runtime.crypto.subtle.importKey('jwk', key, algorithm, false, [keyUsage]);\n  }\n\n  const keyData = pemToBuffer(key);\n  const format = keyUsage === 'sign' ? 'pkcs8' : 'spki';\n\n  return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);\n}\n", "import type { Jwt, JwtPayload } from '@clerk/types';\n\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { runtime } from '../runtime';\nimport { base64url } from '../util/rfc4648';\nimport { getCryptoAlgorithm } from './algorithms';\nimport {\n  assertActivationClaim,\n  assertAudienceClaim,\n  assertAuthorizedPartiesClaim,\n  assertExpirationClaim,\n  assertHeaderAlgorithm,\n  assertHeaderType,\n  assertIssuedAtClaim,\n  assertSubClaim,\n} from './assertions';\nimport { importKey } from './cryptoKeys';\nimport type { JwtReturnType } from './types';\n\nconst DEFAULT_CLOCK_SKEW_IN_MS = 5 * 1000;\n\nexport async function hasValidSignature(jwt: Jwt, key: JsonWebKey | string): Promise<JwtReturnType<boolean, Error>> {\n  const { header, signature, raw } = jwt;\n  const encoder = new TextEncoder();\n  const data = encoder.encode([raw.header, raw.payload].join('.'));\n  const algorithm = getCryptoAlgorithm(header.alg);\n\n  try {\n    const cryptoKey = await importKey(key, algorithm, 'verify');\n\n    const verified = await runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);\n    return { data: verified };\n  } catch (error) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: (error as Error)?.message,\n        }),\n      ],\n    };\n  }\n}\n\nexport function decodeJwt(token: string): JwtReturnType<Jwt, TokenVerificationError> {\n  const tokenParts = (token || '').toString().split('.');\n  if (tokenParts.length !== 3) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalid,\n          message: `Invalid JWT form. A JWT consists of three parts separated by dots.`,\n        }),\n      ],\n    };\n  }\n\n  const [rawHeader, rawPayload, rawSignature] = tokenParts;\n\n  const decoder = new TextDecoder();\n\n  // To verify a JWS with SubtleCrypto you need to be careful to encode and decode\n  // the data properly between binary and base64url representation. Unfortunately\n  // the standard implementation in the V8 of btoa() and atob() are difficult to\n  // work with as they use \"a Unicode string containing only characters in the\n  // range U+0000 to U+00FF, each representing a binary byte with values 0x00 to\n  // 0xFF respectively\" as the representation of binary data.\n\n  // A better solution to represent binary data in Javascript is to use ES6 TypedArray\n  // and use a Javascript library to convert them to base64url that honors RFC 4648.\n\n  // Side note: The difference between base64 and base64url is the characters selected\n  // for value 62 and 63 in the standard, base64 encode them to + and / while base64url\n  // encode - and _.\n\n  // More info at https://stackoverflow.com/questions/54062583/how-to-verify-a-signed-jwt-with-subtlecrypto-of-the-web-crypto-API\n  const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));\n  const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));\n\n  const signature = base64url.parse(rawSignature, { loose: true });\n\n  const data = {\n    header,\n    payload,\n    signature,\n    raw: {\n      header: rawHeader,\n      payload: rawPayload,\n      signature: rawSignature,\n      text: token,\n    },\n  } satisfies Jwt;\n\n  return { data };\n}\n\n/**\n * @inline\n */\nexport type VerifyJwtOptions = {\n  /**\n   * A string or list of [audiences](https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.3). If passed, it is checked against the `aud` claim in the token.\n   */\n  audience?: string | string[];\n  /**\n   * An allowlist of origins to verify against, to protect your application from the subdomain cookie leaking attack.\n   * @example\n   * ```ts\n   * ['http://localhost:3000', 'https://example.com']\n   * ```\n   */\n  authorizedParties?: string[];\n  /**\n   * Specifies the allowed time difference (in milliseconds) between the Clerk server (which generates the token) and the clock of the user's application server when validating a token.\n   * @default 5000\n   */\n  clockSkewInMs?: number;\n  /**\n   * @internal\n   */\n  key: JsonWebKey | string;\n};\n\nexport async function verifyJwt(\n  token: string,\n  options: VerifyJwtOptions,\n): Promise<JwtReturnType<JwtPayload, TokenVerificationError>> {\n  const { audience, authorizedParties, clockSkewInMs, key } = options;\n  const clockSkew = clockSkewInMs || DEFAULT_CLOCK_SKEW_IN_MS;\n\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n\n  const { header, payload } = decoded;\n  try {\n    // Header verifications\n    const { typ, alg } = header;\n\n    assertHeaderType(typ);\n    assertHeaderAlgorithm(alg);\n\n    // Payload verifications\n    const { azp, sub, aud, iat, exp, nbf } = payload;\n\n    assertSubClaim(sub);\n    assertAudienceClaim([aud], [audience]);\n    assertAuthorizedPartiesClaim(azp, authorizedParties);\n    assertExpirationClaim(exp, clockSkew);\n    assertActivationClaim(nbf, clockSkew);\n    assertIssuedAtClaim(iat, clockSkew);\n  } catch (err) {\n    return { errors: [err as TokenVerificationError] };\n  }\n\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          action: TokenVerificationErrorAction.EnsureClerkJWT,\n          reason: TokenVerificationErrorReason.TokenVerificationFailed,\n          message: `Error verifying JWT signature. ${signatureErrors[0]}`,\n        }),\n      ],\n    };\n  }\n\n  if (!signatureValid) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: 'JWT signature is invalid.',\n        }),\n      ],\n    };\n  }\n\n  return { data: payload };\n}\n"], "mappings": ";;;;;;;AAeA,SAAS,aAAa,cAAc;AAmBpC,IAAM,cAAc,MAAM,KAAK,UAAU;AAElC,IAAM,UAAmB;AAAA,EAC9B;AAAA,EACA,IAAI,QAAQ;AAEV,WAAO,QAAQ,IAAI,aAAa,SAAS,QAAQ;AAAA,EACnD;AAAA,EACA,iBAAiB,WAAW;AAAA,EAC5B,MAAM,WAAW;AAAA,EACjB,UAAU,WAAW;AAAA,EACrB,SAAS,WAAW;AAAA,EACpB,SAAS,WAAW;AAAA,EACpB,UAAU,WAAW;AACvB;;;ACrCO,IAAM,YAAY;AAAA,EACvB,MAAM,QAAgB,MAAiC;AACrD,WAAO,MAAM,QAAQ,mBAAmB,IAAI;AAAA,EAC9C;AAAA,EAEA,UAAU,MAAyB,MAAiC;AAClE,WAAO,UAAU,MAAM,mBAAmB,IAAI;AAAA,EAChD;AACF;AAEA,IAAM,oBAA8B;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AACR;AAiBA,SAAS,MAAM,QAAgB,UAAoB,OAAqB,CAAC,GAAe;AAEtF,MAAI,CAAC,SAAS,OAAO;AACnB,aAAS,QAAQ,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,EAAE,GAAG;AAC9C,eAAS,MAAM,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,IACtC;AAAA,EACF;AAGA,MAAI,CAAC,KAAK,SAAU,OAAO,SAAS,SAAS,OAAQ,GAAG;AACtD,UAAM,IAAI,YAAY,iBAAiB;AAAA,EACzC;AAGA,MAAI,MAAM,OAAO;AACjB,SAAO,OAAO,MAAM,CAAC,MAAM,KAAK;AAC9B,MAAE;AAGF,QAAI,CAAC,KAAK,SAAS,GAAI,OAAO,SAAS,OAAO,SAAS,OAAQ,IAAI;AACjE,YAAM,IAAI,YAAY,iBAAiB;AAAA,IACzC;AAAA,EACF;AAGA,QAAM,MAAM,KAAK,KAAK,OAAO,YAAc,MAAM,SAAS,OAAQ,IAAK,CAAC;AAGxE,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAE5B,UAAM,QAAQ,SAAS,MAAM,OAAO,CAAC,CAAC;AACtC,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,YAAY,uBAAuB,OAAO,CAAC,CAAC;AAAA,IACxD;AAGA,aAAU,UAAU,SAAS,OAAQ;AACrC,YAAQ,SAAS;AAGjB,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,SAAS,IAAI,MAAQ,UAAU;AAAA,IACrC;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,QAAQ,MAAQ,UAAW,IAAI,MAAQ;AAC1D,UAAM,IAAI,YAAY,wBAAwB;AAAA,EAChD;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,MAAyB,UAAoB,OAAyB,CAAC,GAAW;AACnG,QAAM,EAAE,MAAM,KAAK,IAAI;AACvB,QAAM,QAAQ,KAAK,SAAS,QAAQ;AACpC,MAAI,MAAM;AAEV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAEpC,aAAU,UAAU,IAAM,MAAO,KAAK,CAAC;AACvC,YAAQ;AAGR,WAAO,OAAO,SAAS,MAAM;AAC3B,cAAQ,SAAS;AACjB,aAAO,SAAS,MAAM,OAAQ,UAAU,IAAK;AAAA,IAC/C;AAAA,EACF;AAGA,MAAI,MAAM;AACR,WAAO,SAAS,MAAM,OAAQ,UAAW,SAAS,OAAO,IAAM;AAAA,EACjE;AAGA,MAAI,KAAK;AACP,WAAQ,IAAI,SAAS,SAAS,OAAQ,GAAG;AACvC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACnIA,IAAM,YAAoC;AAAA,EACxC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,qBAAqB;AAE3B,IAAM,qBAA6C;AAAA,EACjD,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEO,IAAM,OAAO,OAAO,KAAK,SAAS;AAElC,SAAS,mBAAmB,eAA8C;AAC/E,QAAM,OAAO,UAAU,aAAa;AACpC,QAAM,OAAO,mBAAmB,aAAa;AAE7C,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,UAAM,IAAI,MAAM,yBAAyB,aAAa,qBAAqB,KAAK,KAAK,GAAG,CAAC,GAAG;AAAA,EAC9F;AAEA,SAAO;AAAA,IACL,MAAM,EAAE,MAAM,UAAU,aAAa,EAAE;AAAA,IACvC,MAAM,mBAAmB,aAAa;AAAA,EACxC;AACF;;;ACtBA,IAAM,gBAAgB,CAAC,MAA8B;AACnD,SAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,KAAK,EAAE,MAAM,OAAK,OAAO,MAAM,QAAQ;AAC/E;AAEO,IAAM,sBAAsB,CAAC,KAAe,aAAuB;AACxE,QAAM,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AACtD,QAAM,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AAC5C,QAAM,uBAAuB,aAAa,SAAS,KAAK,QAAQ,SAAS;AAEzE,MAAI,CAAC,sBAAsB;AASzB;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,CAAC,aAAa,SAAS,GAAG,GAAG;AAC/B,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,QAAQ,6BAA6B;AAAA,QACrC,SAAS,oCAAoC,KAAK,UAAU,GAAG,CAAC,yBAAyB,KAAK;AAAA,UAC5F;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,WAAW,cAAc,GAAG,GAAG;AAC7B,QAAI,CAAC,IAAI,KAAK,OAAK,aAAa,SAAS,CAAC,CAAC,GAAG;AAC5C,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,QAAQ,6BAA6B;AAAA,QACrC,SAAS,0CAA0C,KAAK,UAAU,GAAG,CAAC,yBAAyB,KAAK;AAAA,UAClG;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEO,IAAM,mBAAmB,CAAC,QAAkB;AACjD,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,QAAQ,OAAO;AACjB,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,oBAAoB,KAAK,UAAU,GAAG,CAAC;AAAA,IAClD,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,QAAgB;AACpD,MAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACvB,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,yBAAyB,KAAK,UAAU,GAAG,CAAC,gBAAgB,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AACF;AAEO,IAAM,iBAAiB,CAAC,QAAiB;AAC9C,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,kEAAkE,KAAK,UAAU,GAAG,CAAC;AAAA,IAChG,CAAC;AAAA,EACH;AACF;AAEO,IAAM,+BAA+B,CAAC,KAAc,sBAAiC;AAC1F,MAAI,CAAC,OAAO,CAAC,qBAAqB,kBAAkB,WAAW,GAAG;AAChE;AAAA,EACF;AAEA,MAAI,CAAC,kBAAkB,SAAS,GAAG,GAAG;AACpC,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,4CAA4C,KAAK,UAAU,GAAG,CAAC,eAAe,iBAAiB;AAAA,IAC1G,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAa,kBAA0B;AAC3E,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,uCAAuC,KAAK,UAAU,GAAG,CAAC;AAAA,IACrE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,aAAa,oBAAI,KAAK,CAAC;AAC7B,aAAW,cAAc,GAAG;AAE5B,QAAM,UAAU,WAAW,QAAQ,KAAK,YAAY,QAAQ,IAAI;AAChE,MAAI,SAAS;AACX,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,gCAAgC,WAAW,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IAC/G,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAyB,kBAA0B;AACvF,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,2CAA2C,KAAK,UAAU,GAAG,CAAC;AAAA,IACzE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,gBAAgB,oBAAI,KAAK,CAAC;AAChC,gBAAc,cAAc,GAAG;AAE/B,QAAM,QAAQ,cAAc,QAAQ,IAAI,YAAY,QAAQ,IAAI;AAChE,MAAI,OAAO;AACT,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,6EAA6E,cAAc,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IAC/J,CAAC;AAAA,EACH;AACF;AAEO,IAAM,sBAAsB,CAAC,KAAyB,kBAA0B;AACrF,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,QAAQ,6BAA6B;AAAA,MACrC,SAAS,0CAA0C,KAAK,UAAU,GAAG,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,eAAe,oBAAI,KAAK,CAAC;AAC/B,eAAa,cAAc,GAAG;AAE9B,QAAM,aAAa,aAAa,QAAQ,IAAI,YAAY,QAAQ,IAAI;AACpE,MAAI,YAAY;AACd,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,oEAAoE,aAAa,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IACrJ,CAAC;AAAA,EACH;AACF;;;ACxKA,SAAS,sBAAsB;AAK/B,SAAS,YAAY,QAA6B;AAChD,QAAM,UAAU,OACb,QAAQ,uBAAuB,EAAE,EACjC,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,OAAO,EAAE;AAEpB,QAAM,UAAU,eAAe,OAAO;AAEtC,QAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;AAC7C,QAAM,UAAU,IAAI,WAAW,MAAM;AAErC,WAAS,IAAI,GAAG,SAAS,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AACxD,YAAQ,CAAC,IAAI,QAAQ,WAAW,CAAC;AAAA,EACnC;AAEA,SAAO;AACT;AAEO,SAAS,UACd,KACA,WACA,UACoB;AACpB,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,QAAQ,OAAO,OAAO,UAAU,OAAO,KAAK,WAAW,OAAO,CAAC,QAAQ,CAAC;AAAA,EACjF;AAEA,QAAM,UAAU,YAAY,GAAG;AAC/B,QAAM,SAAS,aAAa,SAAS,UAAU;AAE/C,SAAO,QAAQ,OAAO,OAAO,UAAU,QAAQ,SAAS,WAAW,OAAO,CAAC,QAAQ,CAAC;AACtF;;;ACjBA,IAAM,2BAA2B,IAAI;AAErC,eAAsB,kBAAkB,KAAU,KAAkE;AAClH,QAAM,EAAE,QAAQ,WAAW,IAAI,IAAI;AACnC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,EAAE,KAAK,GAAG,CAAC;AAC/D,QAAM,YAAY,mBAAmB,OAAO,GAAG;AAE/C,MAAI;AACF,UAAM,YAAY,MAAM,UAAU,KAAK,WAAW,QAAQ;AAE1D,UAAM,WAAW,MAAM,QAAQ,OAAO,OAAO,OAAO,UAAU,MAAM,WAAW,WAAW,IAAI;AAC9F,WAAO,EAAE,MAAM,SAAS;AAAA,EAC1B,SAAS,OAAO;AACd,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,IAAI,uBAAuB;AAAA,UACzB,QAAQ,6BAA6B;AAAA,UACrC,SAAU,OAAiB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,UAAU,OAA2D;AACnF,QAAM,cAAc,SAAS,IAAI,SAAS,EAAE,MAAM,GAAG;AACrD,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,IAAI,uBAAuB;AAAA,UACzB,QAAQ,6BAA6B;AAAA,UACrC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,QAAM,CAAC,WAAW,YAAY,YAAY,IAAI;AAE9C,QAAM,UAAU,IAAI,YAAY;AAiBhC,QAAM,SAAS,KAAK,MAAM,QAAQ,OAAO,UAAU,MAAM,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;AACrF,QAAM,UAAU,KAAK,MAAM,QAAQ,OAAO,UAAU,MAAM,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;AAEvF,QAAM,YAAY,UAAU,MAAM,cAAc,EAAE,OAAO,KAAK,CAAC;AAE/D,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,MACH,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO,EAAE,KAAK;AAChB;AA6BA,eAAsB,UACpB,OACA,SAC4D;AAC5D,QAAM,EAAE,UAAU,mBAAmB,eAAe,IAAI,IAAI;AAC5D,QAAM,YAAY,iBAAiB;AAEnC,QAAM,EAAE,MAAM,SAAS,OAAO,IAAI,UAAU,KAAK;AACjD,MAAI,QAAQ;AACV,WAAO,EAAE,OAAO;AAAA,EAClB;AAEA,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAC5B,MAAI;AAEF,UAAM,EAAE,KAAK,IAAI,IAAI;AAErB,qBAAiB,GAAG;AACpB,0BAAsB,GAAG;AAGzB,UAAM,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI;AAEzC,mBAAe,GAAG;AAClB,wBAAoB,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;AACrC,iCAA6B,KAAK,iBAAiB;AACnD,0BAAsB,KAAK,SAAS;AACpC,0BAAsB,KAAK,SAAS;AACpC,wBAAoB,KAAK,SAAS;AAAA,EACpC,SAAS,KAAK;AACZ,WAAO,EAAE,QAAQ,CAAC,GAA6B,EAAE;AAAA,EACnD;AAEA,QAAM,EAAE,MAAM,gBAAgB,QAAQ,gBAAgB,IAAI,MAAM,kBAAkB,SAAS,GAAG;AAC9F,MAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,IAAI,uBAAuB;AAAA,UACzB,QAAQ,6BAA6B;AAAA,UACrC,QAAQ,6BAA6B;AAAA,UACrC,SAAS,kCAAkC,gBAAgB,CAAC,CAAC;AAAA,QAC/D,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,IAAI,uBAAuB;AAAA,UACzB,QAAQ,6BAA6B;AAAA,UACrC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,MAAM,QAAQ;AACzB;", "names": []}