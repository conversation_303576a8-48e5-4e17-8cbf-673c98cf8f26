export { createDeferredPromise, fastDeepMergeAndKeep, fastDeepMergeAndReplace, isDevelopmentEnvironment, isProductionEnvironment, isStaging, isTestEnvironment, logErrorInDevMode, noop, runWithExponentialBackOff } from './utils/index.mjs';
export { h as handleValueOrFn } from './handleValueOrFn-D2uLOn6s.mjs';
export { apiUrlFromPublishableKey } from './apiUrlFromPublishableKey.mjs';
export { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot } from './browser.mjs';
export { callWithRetry } from './callWithRetry.mjs';
export { colorToSameTypeString, hasAlpha, hexStringToRgbaColor, isHSLColor, isRGBColor, isTransparent, isValidHexString, isValidHslaString, isValidRgbaString, stringToHslaColor, stringToSameTypeColor } from './color.mjs';
export { CURRENT_DEV_INSTANCE_SUFFIXES, DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES, LOCAL_API_URL, LOCAL_ENV_SUFFIXES, PROD_API_URL, STAGING_API_URL, STAGING_ENV_SUFFIXES, iconImageUrl } from './constants.mjs';
export { RelativeDateCase, addYears, dateTo12HourTime, differenceInCalendarDays, formatRelative, normalizeDate } from './date.mjs';
export { deprecated, deprecatedObjectProperty, deprecatedProperty } from './deprecated.mjs';
export { deriveState } from './deriveState.mjs';
export { ClerkAPIResponseError, ClerkRuntimeError, ClerkWebAuthnError, EmailLinkError, EmailLinkErrorCode, EmailLinkErrorCodeStatus, ErrorThrower, ErrorThrowerOptions, MetamaskError, buildErrorThrower, errorToJSON, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMetamaskError, isNetworkError, isPasswordPwnedError, isUnauthorizedError, isUserLockedError, parseError, parseErrors } from './error.mjs';
export { SupportedMimeType, extension, readJSONFile } from './file.mjs';
export { isomorphicAtob } from './isomorphicAtob.mjs';
export { isomorphicBtoa } from './isomorphicBtoa.mjs';
export { buildPublishableKey, createDevOrStagingUrlCache, getCookieSuffix, getSuffixedCookieName, isDevelopmentFromPublishableKey, isDevelopmentFromSecretKey, isProductionFromPublishableKey, isProductionFromSecretKey, isPublishableKey, parsePublishableKey } from './keys.mjs';
export { LoadClerkJsScriptOptions, buildClerkJsScriptAttributes, clerkJsScriptUrl, loadClerkJsScript, setClerkJsLoadingErrorPackageName } from './loadClerkJsScript.mjs';
export { loadScript } from './loadScript.mjs';
export { LocalStorageBroadcastChannel } from './localStorageBroadcastChannel.mjs';
export { Poller, PollerCallback, PollerRun, PollerStop } from './poller.mjs';
export { isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy.mjs';
export { camelToSnake, deepCamelToSnake, deepSnakeToCamel, getNonUndefinedValues, isIPV4Address, isTruthy, snakeToCamel, titleize, toSentence } from './underscore.mjs';
export { addClerkPrefix, cleanDoubleSlashes, getClerkJsMajorVersionOrTag, getScriptUrl, hasLeadingSlash, hasTrailingSlash, isAbsoluteUrl, isCurrentDevAccountPortalOrigin, isLegacyDevAccountPortalOrigin, isNonEmptyURL, joinURL, parseSearchParams, stripScheme, withLeadingSlash, withTrailingSlash, withoutLeadingSlash, withoutTrailingSlash } from './url.mjs';
export { versionSelector } from './versionSelector.mjs';
export { applyFunctionToObj, filterProps, removeUndefined, without } from './object.mjs';
export { logger } from './logger.mjs';
export { createWorkerTimers } from './workerTimers/index.mjs';
export { DEV_BROWSER_JWT_KEY, extractDevBrowserJWTFromURL, setDevBrowserJWTInURL } from './devBrowser.mjs';
export { getEnvVariable } from './getEnvVariable.mjs';
export { PathMatcherParam, PathPattern, WithPathPatternWildcard, createPathMatcher } from './pathMatcher.mjs';
import '@clerk/types';
