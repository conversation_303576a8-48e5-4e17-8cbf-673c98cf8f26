const mongoose = require('mongoose');
const slugify = require('slugify');

const postSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters'],
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
  },
  content: {
    type: String,
    required: [true, 'Content is required'],
  },
  excerpt: {
    type: String,
    maxlength: [500, 'Excerpt cannot be more than 500 characters'],
  },
  
  // SEO fields
  metaTitle: {
    type: String,
    maxlength: [60, 'Meta title cannot be more than 60 characters'],
  },
  metaDescription: {
    type: String,
    maxlength: [160, 'Meta description cannot be more than 160 characters'],
  },
  keywords: [{
    type: String,
    trim: true,
  }],
  
  // Media
  featuredImage: {
    url: String,
    alt: String,
    caption: String,
  },
  
  // Author and categorization
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  categories: [{
    type: String,
    trim: true,
  }],
  tags: [{
    type: String,
    trim: true,
  }],
  
  // Publishing
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  publishedAt: {
    type: Date,
  },
  scheduledFor: {
    type: Date,
  },
  
  // Analytics
  views: {
    type: Number,
    default: 0,
  },
  likes: {
    type: Number,
    default: 0,
  },
  shares: {
    type: Number,
    default: 0,
  },
  
  // Engagement
  comments: [{
    author: {
      name: String,
      email: String,
      website: String,
    },
    content: String,
    approved: {
      type: Boolean,
      default: false,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // AI-generated content
  aiGenerated: {
    type: Boolean,
    default: false,
  },
  aiPrompt: {
    type: String,
  },
  
  // Reading time (in minutes)
  readingTime: {
    type: Number,
    default: 0,
  },
  
  // Social sharing
  socialSharing: {
    twitter: { type: Boolean, default: true },
    facebook: { type: Boolean, default: true },
    linkedin: { type: Boolean, default: true },
  },
  
  // SEO optimization
  seoScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 0,
  },
  
  // Schema markup
  schemaMarkup: {
    type: mongoose.Schema.Types.Mixed,
  },
  
}, {
  timestamps: true,
});

// Indexes for better query performance
// Note: slug already has unique index from schema definition
postSchema.index({ author: 1 });
postSchema.index({ status: 1 });
postSchema.index({ publishedAt: -1 });
postSchema.index({ categories: 1 });
postSchema.index({ tags: 1 });
postSchema.index({ views: -1 });
postSchema.index({ createdAt: -1 });

// Text index for search
postSchema.index({
  title: 'text',
  content: 'text',
  excerpt: 'text',
  categories: 'text',
  tags: 'text',
});

// Generate slug before saving
postSchema.pre('save', function(next) {
  if (this.isModified('title') || this.isNew) {
    this.slug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
  }
  
  // Set published date when status changes to published
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // Generate excerpt if not provided
  if (!this.excerpt && this.content) {
    const plainText = this.content.replace(/<[^>]*>/g, '');
    this.excerpt = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '');
  }
  
  // Calculate reading time
  if (this.content) {
    const wordsPerMinute = 200;
    const wordCount = this.content.split(/\s+/).length;
    this.readingTime = Math.ceil(wordCount / wordsPerMinute);
  }
  
  // Generate meta title and description if not provided
  if (!this.metaTitle) {
    this.metaTitle = this.title.substring(0, 60);
  }
  
  if (!this.metaDescription && this.excerpt) {
    this.metaDescription = this.excerpt.substring(0, 160);
  }
  
  next();
});

// Ensure unique slug
postSchema.pre('save', async function(next) {
  if (this.isModified('slug') || this.isNew) {
    let baseSlug = this.slug;
    let counter = 1;
    
    while (true) {
      const existingPost = await this.constructor.findOne({
        slug: this.slug,
        _id: { $ne: this._id },
      });
      
      if (!existingPost) break;
      
      this.slug = `${baseSlug}-${counter}`;
      counter++;
    }
  }
  next();
});

// Virtual for URL
postSchema.virtual('url').get(function() {
  return `/blog/${this.slug}`;
});

// Method to increment views
postSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Method to get public data
postSchema.methods.getPublicData = function() {
  return {
    _id: this._id,
    title: this.title,
    slug: this.slug,
    excerpt: this.excerpt,
    featuredImage: this.featuredImage,
    author: this.author,
    categories: this.categories,
    tags: this.tags,
    publishedAt: this.publishedAt,
    readingTime: this.readingTime,
    views: this.views,
    likes: this.likes,
    url: this.url,
  };
};

// Static method to get published posts
postSchema.statics.getPublished = function(options = {}) {
  const {
    page = 1,
    limit = 10,
    category,
    tag,
    author,
    search,
  } = options;
  
  const query = { status: 'published' };
  
  if (category) query.categories = category;
  if (tag) query.tags = tag;
  if (author) query.author = author;
  if (search) {
    query.$text = { $search: search };
  }
  
  return this.find(query)
    .populate('author', 'name avatar blogDomain')
    .sort({ publishedAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);
};

module.exports = mongoose.model('Post', postSchema);
