import {
  addClerkPrefix,
  cleanDoubleSlashes,
  getClerkJsMajorVersionOrTag,
  getScriptUrl,
  hasLeadingSlash,
  hasTrailingSlash,
  isAbsoluteUrl,
  isCurrentDevAccountPortalOrigin,
  isLegacyDevAccountPortalOrigin,
  isNonEmptyURL,
  joinURL,
  parseSearchParams,
  stripScheme,
  withLeadingSlash,
  withTrailingSlash,
  withoutLeadingSlash,
  withoutTrailingSlash
} from "./chunk-IFTVZ2LQ.mjs";
import "./chunk-3TMSNP4L.mjs";
import "./chunk-I6MTSTOF.mjs";
import "./chunk-7ELT755Q.mjs";
export {
  addClerkPrefix,
  cleanDoubleSlashes,
  getClerkJsMajorVersionOrTag,
  getScriptUrl,
  hasLeadingSlash,
  hasTrailingSlash,
  isAbsoluteUrl,
  isCurrentDevAccountPortalOrigin,
  isLegacyDevAccountPortalOrigin,
  isNonEmptyURL,
  joinURL,
  parseSearchParams,
  stripScheme,
  withLeadingSlash,
  withTrailingSlash,
  withoutLeadingSlash,
  withoutTrailingSlash
};
//# sourceMappingURL=url.mjs.map