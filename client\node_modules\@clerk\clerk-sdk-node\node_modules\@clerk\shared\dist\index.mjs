import {
  camelT<PERSON><PERSON>nake,
  deepCamelTo<PERSON>nake,
  deepSnakeToCamel,
  getNonUndefinedValues,
  isIPV4Address,
  isTruthy,
  snakeToCamel,
  titleize,
  toSentence
} from "./chunk-QE2A7CJI.mjs";
import {
  logger
} from "./chunk-CYDR2ZSA.mjs";
import {
  applyFunctionToObj,
  filterProps,
  removeUndefined,
  without
} from "./chunk-CFXQSUF6.mjs";
import {
  createPathMatcher
} from "./chunk-2ZNADCNC.mjs";
import "./chunk-JJHTUJGL.mjs";
import {
  Poller
} from "./chunk-JY46X3OC.mjs";
import {
  createWorkerTimers
} from "./chunk-ZHPWRK4R.mjs";
import {
  buildClerkJsScriptAttributes,
  clerkJsScriptUrl,
  loadClerkJsScript,
  setClerkJsLoadingErrorPackageName
} from "./chunk-3J5I5O32.mjs";
import {
  addClerkPrefix,
  cleanDoubleSlashes,
  getClerkJsMajorVersionOrTag,
  getScriptUrl,
  hasLeadingSlash,
  hasTrailingSlash,
  isAbsoluteUrl,
  isCurrentDevAccountPortalOrigin,
  isLegacyDevAccountPortalOrigin,
  isNonEmptyURL,
  joinURL,
  parseSearchParams,
  stripScheme,
  withLeadingSlash,
  withTrailingSlash,
  withoutLeadingSlash,
  withoutTrailingSlash
} from "./chunk-IFTVZ2LQ.mjs";
import {
  versionSelector
} from "./chunk-LYW7U4SP.mjs";
import {
  isHttpOrHttps,
  isProxyUrlRelative,
  isValidProxyUrl,
  proxyUrlToAbsoluteURL
} from "./chunk-6NDGN2IU.mjs";
import {
  loadScript
} from "./chunk-UHYOOJ74.mjs";
import {
  fastDeepMergeAndKeep,
  fastDeepMergeAndReplace,
  logErrorInDevMode,
  runWithExponentialBackOff
} from "./chunk-YNDNV4YF.mjs";
import {
  createDeferredPromise
} from "./chunk-BS4QFUKM.mjs";
import {
  isStaging
} from "./chunk-3TMSNP4L.mjs";
import {
  noop
} from "./chunk-7FNX7RWY.mjs";
import {
  handleValueOrFn
} from "./chunk-O32JQBM6.mjs";
import {
  LocalStorageBroadcastChannel
} from "./chunk-KZL5MSSZ.mjs";
import {
  addYears,
  dateTo12HourTime,
  differenceInCalendarDays,
  formatRelative,
  normalizeDate
} from "./chunk-XQNAC75V.mjs";
import {
  deprecated,
  deprecatedObjectProperty,
  deprecatedProperty
} from "./chunk-UEY4AZIP.mjs";
import {
  isDevelopmentEnvironment,
  isProductionEnvironment,
  isTestEnvironment
} from "./chunk-7HPDNZ3R.mjs";
import {
  deriveState
} from "./chunk-75RCOMEB.mjs";
import {
  DEV_BROWSER_JWT_KEY,
  extractDevBrowserJWTFromURL,
  setDevBrowserJWTInURL
} from "./chunk-K64INQ4C.mjs";
import {
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
} from "./chunk-JXRB7SGQ.mjs";
import {
  extension,
  readJSONFile
} from "./chunk-5JU2E5TY.mjs";
import {
  getEnvVariable
} from "./chunk-TALGHI24.mjs";
import {
  apiUrlFromPublishableKey
} from "./chunk-NNO3XJ5E.mjs";
import {
  buildPublishableKey,
  createDevOrStagingUrlCache,
  getCookieSuffix,
  getSuffixedCookieName,
  isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey,
  isProductionFromPublishableKey,
  isProductionFromSecretKey,
  isPublishableKey,
  parsePublishableKey
} from "./chunk-G3VP5PJE.mjs";
import {
  isomorphicAtob
} from "./chunk-TETGTEI2.mjs";
import {
  isomorphicBtoa
} from "./chunk-KOH7GTJO.mjs";
import {
  inBrowser,
  isBrowserOnline,
  isValidBrowser,
  isValidBrowserOnline,
  userAgentIsRobot
} from "./chunk-JKSAJ6AV.mjs";
import {
  callWithRetry
} from "./chunk-4PW5MDZA.mjs";
import {
  colorToSameTypeString,
  hasAlpha,
  hexStringToRgbaColor,
  isHSLColor,
  isRGBColor,
  isTransparent,
  isValidHexString,
  isValidHslaString,
  isValidRgbaString,
  stringToHslaColor,
  stringToSameTypeColor
} from "./chunk-X6NLIF7Y.mjs";
import {
  CURRENT_DEV_INSTANCE_SUFFIXES,
  DEV_OR_STAGING_SUFFIXES,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  PROD_API_URL,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  iconImageUrl
} from "./chunk-I6MTSTOF.mjs";
import "./chunk-7ELT755Q.mjs";
export {
  CURRENT_DEV_INSTANCE_SUFFIXES,
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  DEV_BROWSER_JWT_KEY,
  DEV_OR_STAGING_SUFFIXES,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  LocalStorageBroadcastChannel,
  PROD_API_URL,
  Poller,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  addClerkPrefix,
  addYears,
  apiUrlFromPublishableKey,
  applyFunctionToObj,
  buildClerkJsScriptAttributes,
  buildErrorThrower,
  buildPublishableKey,
  callWithRetry,
  camelToSnake,
  cleanDoubleSlashes,
  clerkJsScriptUrl,
  colorToSameTypeString,
  createDeferredPromise,
  createDevOrStagingUrlCache,
  createPathMatcher,
  createWorkerTimers,
  dateTo12HourTime,
  deepCamelToSnake,
  deepSnakeToCamel,
  deprecated,
  deprecatedObjectProperty,
  deprecatedProperty,
  deriveState,
  differenceInCalendarDays,
  errorToJSON,
  extension,
  extractDevBrowserJWTFromURL,
  fastDeepMergeAndKeep,
  fastDeepMergeAndReplace,
  filterProps,
  formatRelative,
  getClerkJsMajorVersionOrTag,
  getCookieSuffix,
  getEnvVariable,
  getNonUndefinedValues,
  getScriptUrl,
  getSuffixedCookieName,
  handleValueOrFn,
  hasAlpha,
  hasLeadingSlash,
  hasTrailingSlash,
  hexStringToRgbaColor,
  iconImageUrl,
  inBrowser,
  is4xxError,
  isAbsoluteUrl,
  isBrowserOnline,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isCurrentDevAccountPortalOrigin,
  isDevelopmentEnvironment,
  isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey,
  isEmailLinkError,
  isHSLColor,
  isHttpOrHttps,
  isIPV4Address,
  isKnownError,
  isLegacyDevAccountPortalOrigin,
  isMetamaskError,
  isNetworkError,
  isNonEmptyURL,
  isPasswordPwnedError,
  isProductionEnvironment,
  isProductionFromPublishableKey,
  isProductionFromSecretKey,
  isProxyUrlRelative,
  isPublishableKey,
  isRGBColor,
  isStaging,
  isTestEnvironment,
  isTransparent,
  isTruthy,
  isUnauthorizedError,
  isUserLockedError,
  isValidBrowser,
  isValidBrowserOnline,
  isValidHexString,
  isValidHslaString,
  isValidProxyUrl,
  isValidRgbaString,
  isomorphicAtob,
  isomorphicBtoa,
  joinURL,
  loadClerkJsScript,
  loadScript,
  logErrorInDevMode,
  logger,
  noop,
  normalizeDate,
  parseError,
  parseErrors,
  parsePublishableKey,
  parseSearchParams,
  proxyUrlToAbsoluteURL,
  readJSONFile,
  removeUndefined,
  runWithExponentialBackOff,
  setClerkJsLoadingErrorPackageName,
  setDevBrowserJWTInURL,
  snakeToCamel,
  stringToHslaColor,
  stringToSameTypeColor,
  stripScheme,
  titleize,
  toSentence,
  userAgentIsRobot,
  versionSelector,
  withLeadingSlash,
  withTrailingSlash,
  without,
  withoutLeadingSlash,
  withoutTrailingSlash
};
//# sourceMappingURL=index.mjs.map