# Rivsy Blog Platform - Setup Guide

## 🚀 Quick Start

This guide will help you set up the Rivsy Blog Platform with all the implemented features:

1. **Comprehensive .gitignore** - Covers Node.js, React/Vite, environment variables, and development files
2. **Advanced API Logging** - <PERSON> + <PERSON> for comprehensive request/response logging
3. **Complete Testing Framework** - Jest, React Testing Library, Supertest for full coverage
4. **Clerk Authentication** - OAuth providers, protected routes, session management

## 📋 Prerequisites

- Node.js 18+ and npm
- MongoDB (local or cloud)
- Clerk account (for authentication)

## 🛠️ Installation

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <your-repo-url>
cd rivsy

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 2. Environment Configuration

#### Server Environment (.env)
```bash
cd server
cp .env.example .env
```

Edit `server/.env` with your values:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/rivsy-blog

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Security
BCRYPT_SALT_ROUNDS=12
```

#### Client Environment (.env.local)
```bash
cd client
cp .env.example .env.local
```

Edit `client/.env.local`:
```env
VITE_API_URL=http://localhost:5000
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
```

### 3. Clerk Setup

1. **Create Clerk Account**: Go to [clerk.com](https://clerk.com) and create an account
2. **Create Application**: Create a new application in Clerk dashboard
3. **Configure OAuth Providers**:
   - Go to "User & Authentication" → "Social Connections"
   - Enable Google, GitHub, and other desired providers
   - Configure OAuth credentials for each provider
4. **Get API Keys**:
   - Copy your Publishable Key and Secret Key
   - Add them to your environment files

### 4. Database Setup

```bash
# Start MongoDB (if running locally)
mongod

# Or use MongoDB Atlas cloud database
# Update MONGODB_URI in .env with your connection string
```

## 🏃‍♂️ Running the Application

### Development Mode

```bash
# Terminal 1: Start the server
cd server
npm run dev

# Terminal 2: Start the client
cd client
npm run dev
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5000
- **API Documentation**: http://localhost:5000/health

### Production Mode

```bash
# Build the client
cd client
npm run build

# Start the server (serves both API and static files)
cd server
npm start
```

## 🧪 Testing

### Run All Tests
```bash
# From project root
node test-runner.js

# Or run specific test suites
node test-runner.js server    # Backend tests only
node test-runner.js client    # Frontend tests only
node test-runner.js coverage  # With coverage reports
node test-runner.js watch     # Watch mode for development
```

### Individual Test Commands
```bash
# Server tests
cd server
npm test
npm run test:watch
npm run test:coverage

# Client tests
cd client
npm test
npm run test:watch
npm run test:coverage
```

## 📊 Logging

The application includes comprehensive logging:

### Log Files Location
- **Server logs**: `server/logs/`
  - `combined-YYYY-MM-DD.log` - All logs
  - `error-YYYY-MM-DD.log` - Error logs only
  - `http-YYYY-MM-DD.log` - HTTP request logs

### Log Features
- **Request/Response Logging**: All API calls with timing
- **Error Tracking**: Detailed error logs with stack traces
- **Security Monitoring**: Suspicious activity detection
- **Performance Monitoring**: Slow operation detection
- **User Activity**: Authentication and authorization events

## 🔐 Authentication Features

### Clerk Integration
- **OAuth Providers**: Google, GitHub, and more
- **Social Login**: One-click authentication
- **Session Management**: Automatic token refresh
- **Protected Routes**: Role-based access control
- **User Profiles**: Comprehensive user management

### Available Routes
- **Public**: `/`, `/blog`, `/blog/:slug`
- **Authentication**: `/sign-in`, `/sign-up`
- **Protected**: `/dashboard`, `/create`, `/edit/:id`, `/settings`

### Role-Based Access
- **User**: Basic access to dashboard and profile
- **Author**: Can create and manage posts
- **Admin**: Full system access

## 🛡️ Security Features

- **Rate Limiting**: Prevents abuse
- **Input Sanitization**: XSS and injection protection
- **CORS Configuration**: Secure cross-origin requests
- **Helmet Security**: Security headers
- **Request Validation**: Input validation and sanitization

## 📈 Performance Features

- **Caching**: Redis-based caching (optional)
- **Compression**: Gzip compression
- **Logging**: Performance monitoring
- **Database Indexing**: Optimized queries

## 🔧 Development Tools

### Available Scripts

#### Server
```bash
npm run dev          # Development with nodemon
npm start           # Production server
npm test            # Run tests
npm run test:watch  # Watch mode testing
npm run test:coverage # Coverage reports
```

#### Client
```bash
npm run dev         # Development server
npm run build       # Production build
npm run preview     # Preview production build
npm test           # Run tests
npm run test:watch # Watch mode testing
npm run test:coverage # Coverage reports
```

## 📝 API Documentation

### Authentication Endpoints
- `POST /api/auth/clerk/sync` - Sync user with Clerk
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `DELETE /api/auth/profile` - Delete user account
- `GET /api/auth/stats` - Get user statistics

### Blog Endpoints
- `GET /api/posts` - Get all posts
- `GET /api/posts/:id` - Get specific post
- `POST /api/posts` - Create new post (auth required)
- `PUT /api/posts/:id` - Update post (auth required)
- `DELETE /api/posts/:id` - Delete post (auth required)

## 🚨 Troubleshooting

### Common Issues

1. **Clerk Authentication Errors**
   - Verify API keys in environment files
   - Check Clerk dashboard configuration
   - Ensure OAuth providers are properly configured

2. **Database Connection Issues**
   - Verify MongoDB is running
   - Check connection string in .env
   - Ensure database permissions

3. **Build Errors**
   - Clear node_modules and reinstall
   - Check for missing environment variables
   - Verify all dependencies are installed

4. **Test Failures**
   - Ensure test database is separate
   - Check environment variables for tests
   - Verify all test dependencies are installed

## 📚 Additional Resources

- [Clerk Documentation](https://clerk.com/docs)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [React Documentation](https://react.dev/)
- [Express.js Documentation](https://expressjs.com/)
- [Jest Testing Documentation](https://jestjs.io/docs/getting-started)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
